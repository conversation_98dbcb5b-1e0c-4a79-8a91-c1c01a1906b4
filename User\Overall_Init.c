/**********************************************************
  * @file     Overall_Init.c
  * @brief    系统初始化总文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  * @note     包含系统初始化相关的函数实现
***********************************************************/

#include "main.h"
#include "AnoPTv8.h"

/* 定时任务ID */
static uint16_t taskId_500us;
static uint16_t taskId_1ms;
static uint16_t taskId_1s;

#include "arm_math.h" 
#include "Sys_Can.h"
#include "start_self_test.h"
#include "ad2s1212_spi.h"
#include "RS422_CU.h"

/**
  * @brief  系统整体初始化函数
  * @param  无
  * @retval 无
  */
void AT32A423_system_init(void)
{
    /* 基础系统配置 */
    wk_system_clock_config();    /* 系统时钟配置 */
    wk_periph_clock_config();    /* 外设时钟配置 */
    wk_nvic_config();            /* 中断配置 */

    /* DMA配置 */
    wk_dma1_channel1_init();
    wk_dma_channel_config(DMA1_CHANNEL1, (uint32_t)&ADC1->odt, 
                         (uint32_t)gADC_Manager.ADC_value, ADC_REGULAR_CHANNEL_NUM);
    dma_channel_enable(DMA1_CHANNEL1, TRUE);

    dma_interrupt_enable(DMA1_CHANNEL1, DMA_FDT_INT, TRUE);

    /* 通信接口初始化 */
    wk_usart1_init();            /* USART1初始化 */
    wk_usb_otgfs1_init();        /* USB OTG FS1初始化 */
    CAN1_CAN2_Init();            /* CAN1和CAN2初始化 */ 
    wk_spi2_init();              /* SPI2初始化 */
    wk_spi3_init();              /* SPI3初始化 */

    /* 其他外设初始化 */
    wk_acc_init();               /* ACC初始化 */
    
    wk_exint_config();           /* 外部中断配置 */
    wk_gpio_config();            /* GPIO配置 */

    /* 定时器初始化 */
    wk_tmr1_init();              /* 定时器1初始化 */
    wk_tmr3_init();              /* 定时器3初始化 */
    wk_tmr13_init();             /* 定时器13初始化 */
    //wk_tmr14_init();
    wk_wdt_init();              /* WDT看门狗初始化 100ms*/

    wk_adc1_init();              /* ADC1初始化 */

    
    //wk_clkout_init();
}

/**
  * @brief  用户初始化函数
  * @param  无
  * @retval 无
  */
void user_init(void)
{
    wk_usb_app_init();
    wk_usb_app_init();            /* USB应用初始化 */
    Timer_Tasks_Init();           /* 初始化定时任务 */
    AnoPTv8HwInit();              /* AnoPTv8协议初始化 */
    AD2S1210_Init();              
    
    RS422_CU_Init();              /* RS422通信初始化 */
    ADC_Drive_Init();
    Enable_System_Interrupts();   /* 使能系统中断 */  
    //ADCSelfTest(&adc_self_test_data);
}

/**
  * @brief  使能系统中断
  * @param  无
  * @retval 无
  */
void Enable_System_Interrupts(void)
{
    /* 系统状态中断使能 */
    tmr_interrupt_enable(TMR13, TMR_OVF_INT, TRUE);    // 定时器13溢出中断
    adc_interrupt_enable(ADC1, ADC_PCCE_INT, TRUE);
    tmr_interrupt_enable(TMR1, TMR_C4_INT, TRUE);      // 定时器1计数4中断
    tmr_interrupt_enable(TMR3, TMR_OVF_INT, TRUE);     // 定时器3溢出中断
    tmr_interrupt_enable(TMR14, TMR_OVF_INT, TRUE);
    //usart_interrupt_enable(USART1, USART_RDBF_INT, TRUE);
}

/**
  * @brief  使能用户中断
  * @param  无
  * @retval 无
  */
void Disable_User_Interrupts(void)
{   
    tmr_interrupt_enable(TMR1, TMR_C4_INT, TRUE);      // 定时器1计数4中断
    tmr_interrupt_enable(TMR3, TMR_OVF_INT, TRUE);     // 定时器3溢出中断
}
