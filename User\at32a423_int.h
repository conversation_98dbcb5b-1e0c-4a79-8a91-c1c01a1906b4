/* define to prevent recursive inclusion -------------------------------------*/
#ifndef __AT32A423_INT_H
#define __AT32A423_INT_H

#ifdef __cplusplus
extern "C" {
#endif

/* includes ------------------------------------------------------------------*/
#include "at32a423.h"


void NMI_Handler(void);
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);
void SVC_Handler(void);
void Debug<PERSON>on_Handler(void);
void PendSV_Handler(void);

void SysTick_Handler(void);

void DMA1_Channel2_IRQHandler(void);
void ADC1_IRQHandler(void);
void EXINT9_5_IR<PERSON><PERSON><PERSON><PERSON>(void);
void TMR1_BRK_TMR9_IRQ<PERSON>andler(void);
void TMR1_OVF_TMR10_IRQHandler(void);
void TMR3_GLOBAL_IRQHandler(void);
// void USART1_IRQHandler(void);
void TMR13_GLOBAL_IRQHandler(void);
void OTGFS1_IRQHandler(void);
/* add user code begin exported functions */

/* add user code end exported functions */

#ifdef __cplusplus
}
#endif

#endif
