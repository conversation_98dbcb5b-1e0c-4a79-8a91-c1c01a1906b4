/**********************************************************
 * @file     motor_current_loop_example.c
 * @brief    电流环使用示例文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     展示如何使用电流环控制系统
 *           包含初始化、配置、运行等完整流程
***********************************************************/

#include "motor_current_loop.h"
#include "motor_hardware_interface.h"
#include "Sensor_Drive.h"
#include "ENC_Speed.h"

/*============================ 示例1: 基本电流环控制 ============================*/

/**
 * @brief 基本电流环控制示例
 * @note 演示最简单的电流环使用方法
 */
void example_basic_current_control(void)
{
    // 1. 快速初始化电流环
    if (motor_current_loop_quick_init() != RET_OK) {
        // 初始化失败处理
        return;
    }
    
    // 2. 启动电流环，设置目标电流为5A
    if (motor_current_loop_start(5.0f) != RET_OK) {
        // 启动失败处理
        return;
    }
    
    // 3. 运行一段时间后停止
    // 注意：实际应用中应该在定时器中断中调用motor_current_loop_interrupt_handler()
    
    // 延时函数(用户自行实现)
    // delay_ms(5000);  // 运行5秒
    
    // 4. 停止电流环
    motor_current_loop_stop();
}

/*============================ 示例2: 高级电流环配置 ============================*/

/**
 * @brief 高级电流环配置示例
 * @note 演示如何自定义电流环参数
 */
void example_advanced_current_control(void)
{
    current_loop_t *loop = current_loop_get_instance();
    
    // 1. 初始化电流环
    current_loop_init(loop);
    
    // 2. 自定义配置参数
    current_loop_config_t config = {
        // PI控制器参数
        .current_kp = 0.05f,                    // 比例增益
        .current_ki = 80.0f,                    // 积分增益
        .d_gain_scale_start = 0.85f,            // d轴增益缩放起始点
        .d_gain_scale_max_mod = 0.7f,           // d轴增益缩放最大调制度
        
        // 电机参数(根据实际电机调整)
        .motor_ld = 0.00015f,                   // d轴电感 150μH
        .motor_lq = 0.00015f,                   // q轴电感 150μH
        .motor_rs = 0.08f,                      // 定子电阻 80mΩ
        .motor_flux_linkage = 0.012f,           // 磁链 12mWb
        .motor_pole_pairs = 4,                  // 4极对
        
        // 限制参数
        .max_current = 50.0f,                   // 最大电流 50A
        .max_voltage = 350.0f,                  // 最大电压 350V
        .max_duty = 0.9f,                       // 最大占空比 90%
        
        // 滤波参数
        .current_filter_const = 0.15f,          // 电流滤波常数
        .voltage_filter_const = 0.08f,          // 电压滤波常数
        
        // 解耦控制
        .decoupling_mode = DECOUPLING_CROSS_BEMF,  // 交叉+反电动势解耦
        .temp_comp_enabled = TRUE,              // 使能温度补偿
        
        // 传感器配置
        .sensor_mode = SENSOR_MODE_ENCODER      // 编码器模式
    };
    
    // 3. 应用配置
    current_loop_config_set(loop, &config);
    
    // 4. 设置dq轴电流(id=0, iq=10A)
    current_loop_set_current_dq(loop, 0.0f, 10.0f);
    
    // 5. 使能电流环
    current_loop_enable(loop, TRUE);
}

/*============================ 示例3: 电流环状态监控 ============================*/

/**
 * @brief 电流环状态监控示例
 * @note 演示如何监控电流环运行状态
 */
void example_current_loop_monitoring(void)
{
    current_loop_t *loop = current_loop_get_instance();
    
    // 获取电流环状态
    float id_actual = current_loop_get_id(loop);        // 实际d轴电流
    float iq_actual = current_loop_get_iq(loop);        // 实际q轴电流
    float vd_output = current_loop_get_vd(loop);        // d轴电压输出
    float vq_output = current_loop_get_vq(loop);        // q轴电压输出
    float duty_cycle = current_loop_get_duty_cycle(loop); // 等效占空比
    
    // 获取详细状态(直接访问结构体)
    float i_alpha = loop->state.i_alpha;               // α轴电流
    float i_beta = loop->state.i_beta;                 // β轴电流
    float i_abs = loop->state.i_abs;                   // 电流幅值
    float theta_elec = loop->state.theta_elec;         // 电角度
    float v_bus = loop->state.v_bus;                   // 母线电压
    
    // 性能统计
    uint32_t loop_counter = loop->loop_counter;        // 循环计数器
    float max_exec_time = loop->max_exec_time_us;      // 最大执行时间
    float avg_exec_time = loop->avg_exec_time_us;      // 平均执行时间
    
    // 用户可以将这些数据发送到上位机或显示设备
    // 例如通过AnoPTv8协议发送
    
    // 示例：检查电流环是否正常工作
    if (loop->fault_flag) {
        // 电流环故障处理
        motor_current_loop_stop();
    }
    
    // 示例：检查执行时间是否过长
    if (max_exec_time > 40.0f) {  // 超过40μs
        // 执行时间过长，可能需要优化算法或降低频率
    }
}

/*============================ 示例4: 中断集成示例 ============================*/

/**
 * @brief PWM中断处理函数示例
 * @note 用户需要在PWM中断中调用此函数
 */
void TMR1_BRK_TMR9_IRQHandler(void)
{
    // 检查PWM中断标志
    if (tmr_flag_get(TMR1, TMR_OVF_FLAG) != RESET) {
        // 清除中断标志
        tmr_flag_clear(TMR1, TMR_OVF_FLAG);
        
        // 调用电流环中断处理函数
        motor_current_loop_interrupt_handler();
    }
}

/*============================ 示例5: 完整的电机控制系统 ============================*/

/**
 * @brief 完整的电机控制系统示例
 * @note 演示如何将电流环集成到完整的电机控制系统中
 */
void example_complete_motor_control_system(void)
{
    current_loop_t *loop = current_loop_get_instance();
    
    // 1. 系统初始化
    motor_current_loop_quick_init();
    
    // 2. 电机启动序列
    // 2.1 预充电(如果需要)
    // precharge_capacitors();
    
    // 2.2 编码器校准(如果需要)
    // encoder_calibration();
    
    // 2.3 电流偏移校准
    // current_offset_calibration();
    
    // 3. 启动电流环
    current_loop_enable(loop, TRUE);
    
    // 4. 运行控制循环
    while (1) {
        // 4.1 检查系统状态
        if (loop->fault_flag) {
            // 故障处理
            motor_current_loop_stop();
            break;
        }
        
        // 4.2 更新电流设定值(例如来自速度环或位置环)
        float iq_ref = GetSpeedPIOutput(1000.0f);  // 从速度环获取电流指令
        current_loop_set_current_dq(loop, 0.0f, iq_ref);
        
        // 4.3 监控和数据记录
        example_current_loop_monitoring();
        
        // 4.4 通信处理(例如与上位机通信)
        // communication_process();
        
        // 4.5 延时
        // delay_ms(1);  // 1ms控制周期
    }
}

/*============================ 示例6: 参数调试接口 ============================*/

/**
 * @brief 参数调试接口示例
 * @note 演示如何在运行时调整电流环参数
 */
void example_parameter_tuning(void)
{
    current_loop_t *loop = current_loop_get_instance();
    
    // 在线调整PI参数
    loop->pi_id.kp = 0.08f;     // 调整d轴比例增益
    loop->pi_id.ki = 100.0f;    // 调整d轴积分增益
    loop->pi_iq.kp = 0.08f;     // 调整q轴比例增益
    loop->pi_iq.ki = 100.0f;    // 调整q轴积分增益
    
    // 在线调整滤波参数
    loop->config.current_filter_const = 0.2f;  // 调整电流滤波常数
    
    // 在线调整解耦模式
    loop->config.decoupling_mode = DECOUPLING_CROSS;  // 切换解耦模式
    
    // 在线调整限制参数
    loop->config.max_current = 60.0f;          // 调整最大电流限制
    loop->config.max_duty = 0.85f;             // 调整最大占空比
}

/*============================ 使用说明 ============================*/

/*
 * 使用步骤：
 * 
 * 1. 硬件准备：
 *    - 确保PWM、ADC、编码器等硬件已正确初始化
 *    - 根据实际硬件修改motor_hardware_interface.h中的宏定义
 *    - 实现硬件相关的底层函数
 * 
 * 2. 软件集成：
 *    - 将motor_current_loop.c和motor_math_utils.c添加到工程
 *    - 包含motor_current_loop.h头文件
 *    - 在PWM中断中调用motor_current_loop_interrupt_handler()
 * 
 * 3. 参数调整：
 *    - 根据实际电机参数调整配置结构体
 *    - 通过示例6的方法在线调试参数
 *    - 监控电流环性能，确保稳定运行
 * 
 * 4. 系统集成：
 *    - 将电流环与速度环、位置环集成
 *    - 添加保护功能和故障处理
 *    - 实现与上位机的通信接口
 * 
 * 注意事项：
 * - 电流环频率建议设置为20kHz
 * - 确保中断处理函数执行时间不超过50μs
 * - 定期监控系统性能和稳定性
 * - 根据实际应用调整保护参数
 */
