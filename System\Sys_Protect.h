#ifndef __SYS_PROTECT_H
#define __SYS_PROTECT_H

#include "sysTypeDef.h"
#include "Sensor_Drive.h"


/*******************************************************************************
 * 宏定义
 ******************************************************************************/
/* 去抖阈值定义 */
#define IO_DEBOUNCE_THRESHOLD  3  // 3次采样稳定才认为有效（约1.5ms）
#define IO_ACTIVE_LEVEL        0  // 低电平有效

/* 位操作宏定义 */
#define SET_FLAG(status, flag)     ((status)->flags |=  (flag))
#define CLEAR_FLAG(status, flag)   ((status)->flags &= ~(flag))
#define CHECK_FLAG(status, flag)   ((status)->flags &   (flag))

/*******************************************************************************
 * 软件保护相关类型定义
 ******************************************************************************/
/* 单个保护项的参数配置 */
typedef struct {
    ProtectParam_TypeDef L1;  // 一级保护
    ProtectParam_TypeDef L2;  // 二级保护
    ProtectParam_TypeDef L3;  // 三级保护
} ProtectLevels_TypeDef;

/* 保护参数结构体定义 */
typedef struct {
    ProtectLevels_TypeDef current;          // 母线电流保护参数
    ProtectLevels_TypeDef current_instant;  // 母线电流瞬时保护参数
    ProtectLevels_TypeDef current_q;        // Q轴电流保护参数
    ProtectLevels_TypeDef bus_overvol;      // 母线过压保护参数
    ProtectLevels_TypeDef bus_undervol;     // 母线欠压保护参数
    ProtectLevels_TypeDef motor_temp;       // 电机温度保护参数
    ProtectLevels_TypeDef driver_temp;      // 驱动器温度保护参数
    ProtectLevels_TypeDef cap_temp;         // 电容温度保护参数
    ProtectLevels_TypeDef overspeed;        // 电机超速保护参数
    ProtectLevels_TypeDef v28_undervol;     // 28V欠压保护参数
    ProtectLevels_TypeDef v28_overvol;      // 28V过压保护参数
    ProtectLevels_TypeDef phase_unbalance;  // 三相不平衡保护参数
} ProtectParams_TypeDef;

/* 保护延时计数器结构体 */
typedef struct {
    uint32_t start_time;    // 开始计时的时间点（毫秒）
    uint32_t delay_ms;      // 需要延时的时间（毫秒）
    uint8_t level;          // 当前告警等级
    uint8_t flags;          // 状态标志位
    float last_value;       // 上次检测值
} ProtectDelay_TypeDef;

/* 保护状态结构体 */
typedef struct {
    ProtectDelay_TypeDef current;         // 相线电流保护延时
    ProtectDelay_TypeDef current_instant; // 相线电流瞬时保护延时
    ProtectDelay_TypeDef current_q;       // Q轴电流保护延时
    ProtectDelay_TypeDef bus_overvol;     // 母线过压保护延时
    ProtectDelay_TypeDef bus_undervol;    // 母线欠压保护延时
    ProtectDelay_TypeDef motor_temp;      // 电机温度保护延时
    ProtectDelay_TypeDef driver_temp;     // 驱动器温度保护延时
    ProtectDelay_TypeDef cap_temp;        // 电容温度保护延时
    ProtectDelay_TypeDef overspeed;       // 电机超速保护延时
    ProtectDelay_TypeDef v28_undervol;    // 28V欠压保护延时
    ProtectDelay_TypeDef v28_overvol;     // 28V过压保护延时
    ProtectDelay_TypeDef phase_unbalance; // 三相不平衡保护参数
} ProtectStatus_TypeDef;

/**
 * @brief  时间分级保护系数定义
 * @note   用于随告警持续时间增加调整限制系数
 */
/* 时间阈值和限制系数配置结构体 */
typedef struct {
    const uint32_t time_thresholds[3];  // 时间阈值数组（单位：ms）
    const float iq_coeffs[3];           // 对应的电流限制系数
    const float speed_coeffs[3];        // 对应的速度限制系数
} TimeBasedLimit_Config_TypeDef;

/* 告警计时和状态结构体 */
typedef struct {
    uint32_t start_time;    // 告警开始时间（毫秒）
    uint8_t active;         // 是否处于告警状态
} WarnState_TypeDef;

/* 时间分级保护状态结构体 */
typedef struct {
    WarnState_TypeDef levels[3];  // 对应不同告警等级的状态 [0]=L1, [1]=L2, [2]=L3
} TimeProtect_Status_TypeDef;



/*******************************************************************************
 * IO硬件保护相关类型定义
 ******************************************************************************/
/* IO保护类型枚举 */
typedef enum {
    IO_PROTECT_CURRENT_POS = 0,      // 相电流正向保护
    IO_PROTECT_CURRENT_NEG = 1,      // 相电流负向保护
    IO_PROTECT_BUS_VOLTAGE = 2,      // 母线电压保护
    IO_PROTECT_PHASE_VOLTAGE = 3,    // 相电压保护
    IO_PROTECT_MCU_FAULT = 4,        // 光耦故障保护
    IO_PROTECT_MAX = 5               // 保护类型数量
} IO_Protect_Type;

/* 定义IO保护状态 */
typedef struct {
    uint8_t current_state;      // 当前读取状态
    uint8_t stable_state;       // 稳定状态（去抖后）
    uint8_t debounce_counter;   // 去抖计数器
    uint8_t triggered;          // 触发标志
} IO_Protect_Status_TypeDef;

/* IO保护结果联合体 */
typedef union {
    uint16_t all;                 // 所有保护状态
    struct {
        uint16_t ui_cur_pos : 1;  // U相正向过流
        uint16_t ui_cur_neg : 1;  // U相负向过流
        uint16_t vi_cur_pos : 1;  // V相正向过流
        uint16_t vi_cur_neg : 1;  // V相负向过流
        uint16_t wi_cur_pos : 1;  // W相正向过流
        uint16_t wi_cur_neg : 1;  // W相负向过流
        uint16_t bus_vol :    1;  // 母线过压
        uint16_t uv_vol_pos : 1;  // UV线正向过压
        uint16_t uv_vol_neg : 1;  // UV线负向过压
        uint16_t uw_vol_pos : 1;  // UW线正向过压
        uint16_t uw_vol_neg : 1;  // UW线负向过压
        uint16_t flt_mcu : 1;     // 光耦故障信号
        uint16_t reserved : 4;    // 保留位
    } bits;
} IO_Protect_Result_TypeDef;

/* IO保护参数结构体 */
typedef struct {
    IO_Protect_Type type;         // 保护类型
    gpio_type* gpio_port;         // GPIO端口
    uint16_t gpio_pin;            // GPIO引脚
    uint8_t active_level;         // 有效电平（0=低有效，1=高有效）
    uint8_t debounce_count;       // 消抖计数阈值
    WarnLevel_TypeDef warn_level; // 触发时的告警等级
    IO_Protect_Result_TypeDef fault_mask; // 对应的故障位掩码
} IO_Protect_Param_TypeDef;


/*******************************************************************************
 * 全局变量声明
 ******************************************************************************/
/* 软件保护相关变量 */
extern const ProtectParams_TypeDef g_ProtectParams;    // 保护参数配置
extern ProtectStatus_TypeDef g_ProtectStatus;          // 保护状态

/* IO保护相关变量 */
extern const IO_Protect_Param_TypeDef g_IO_ProtectParams[];
extern IO_Protect_Result_TypeDef g_IO_ProtectResult;

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
/* 告警等级获取函数 */
WarnLevel_TypeDef Get_Current_Warning_Level(void);                            // 获取电流告警等级
WarnLevel_TypeDef Get_Voltage_Warning_Level(void);                            // 获取电压告警等级
WarnLevel_TypeDef Get_Temperature_Warning_Level(void);                        // 获取温度告警等级
WarnLevel_TypeDef Get_Overspeed_Warning_Level(void);                          // 获取超速告警等级

check_t Check_Current_Fault(void);                                            // 检查电流故障
check_t Check_Voltage_Fault(void);                                            // 检查电压故障
check_t Check_Temperature_Fault(void);                                        // 检查温度故障
void Update_Fault_Code(void);                                                 // 更新故障代码

void Check_Current_Protection(void);                                          // 检查电流保护
void Check_Voltage_Protection(void);                                          // 检查电压保护
void Check_Temperature_Protection(void);                                      // 检查温度保护
void Update_System_Protection(void);                                          // 更新系统保护状态

void Reset_Protect_Status(ProtectDelay_TypeDef* status);                      // 重置保护状态
void Reset_Global_Protect_Status(ProtectStatus_TypeDef* status);              // 重置全局保护状态
uint8_t Check_Protect_Delay(ProtectDelay_TypeDef* delay);                     // 检查延时是否到达
void Start_Protect_Delay(ProtectDelay_TypeDef* delay, uint32_t delay_ms, uint8_t level); // 开始延时
void Stop_Protect_Delay(ProtectDelay_TypeDef* delay);                         // 停止延时

/* IO保护相关函数 */
uint8_t Scan_IO_Protection_Optimized(void);                                   // 并行扫描所有IO保护信号并快速响应严重故障
uint8_t Update_System_Protection_Optimized(void);                             // 集中式更新保护状态并执行快速响应
void Reset_IO_Protect_Status(void);                                           // 重置IO保护状态

/* 保护限制系数相关函数 */
void Update_Limit_Coefficients(WarnLevel_TypeDef max_warn_level);             // 更新电流和速度限制系数
float Get_Iq_Limit_Coefficient();                 // 获取Q轴电流限制系数（兼容旧接口）
float Get_Speed_Limit_Coefficient();              // 获取速度限制系数（兼容旧接口）

/********************** 新增：基于ADC中断的保护系统函数声明 START **********************/
void Protection_Process_500us(void);              // 主保护处理函数，在ADC中断中调用
void Protection_HighFreq_Check(void);             // 高频保护检测（电流、电压、IO）
void Protection_LowFreq_Check(void);              // 低频保护检测（温度）

/* 内部函数声明 */
static void Update_Warning_And_Limits_Enhanced(WarnLevel_TypeDef final_level, 
                                              WarnLevel_TypeDef high_freq_level, 
                                              WarnLevel_TypeDef low_freq_level);  // 增强版限制系数更新
/********************** 新增：基于ADC中断的保护系统函数声明 END **********************/

/* 通用保护项配置结构体 */
typedef struct {
    float (*get_value_func)(void);           // 获取测量值的函数指针
    const ProtectLevels_TypeDef* thresholds; // 阈值配置
    ProtectDelay_TypeDef* status;            // 延时状态
    WarnLevel_TypeDef* warn_level;           // 告警等级指针
    uint8_t fault_bit_index;                 // 故障位索引
    uint8_t compare_direction;               // 比较方向：0=(欠压), 1=(过压/超限)
    const char* item_name;                   // 保护项名称（调试用）
} ProtectItem_Config_t;

/* 通用保护检查结果 */
typedef struct {
    WarnLevel_TypeDef level;        // 检查结果等级
    uint8_t fault_triggered;        // 是否触发故障
    float measured_value;           // 测量值
} ProtectCheckResult_t;

/********************** 函数声明 **********************/

/* 保护参数获取函数 */

/* 通用保护检查模板函数 */
WarnLevel_TypeDef Check_Protection_Generic(
    float current_value,
    const ProtectLevels_TypeDef* thresholds,
    ProtectDelay_TypeDef* status,
    WarnLevel_TypeDef* warn_level_ptr,
    uint8_t* fault_flag_ptr,
    uint8_t compare_direction
);

/* 批量保护检查函数 */
WarnLevel_TypeDef Check_All_Protections_Batch(void);

#endif /* __SYS_PROTECT_H */
