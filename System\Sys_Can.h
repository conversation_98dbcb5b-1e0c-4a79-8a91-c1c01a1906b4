#ifndef __SYS_CAN_H
#define __SYS_CAN_H

#include "at32a423_can.h"
#include "sysTypeDef.h"
#include "Sensor_Drive.h"
#include "Sys_TimerEvent.h"

/* CAN接口定义 */
typedef enum {
    CAN_INTERFACE_1 = 0,    // CAN1接口
    CAN_INTERFACE_2 = 1     // CAN2接口
} CAN_Interface_TypeDef;

/* CAN设备定义 */
#define CAN_DEVICE_HOST          0x01    // 飞管计算机ID
#define CAN_DEVICE_MOTOR1        0x03    // 电机控制器1
#define CAN_DEVICE_MOTOR2        0x04    // 电机控制器2
#define CAN_DEVICE_MOTOR3        0x05    // 电机控制器3
#define CAN_DEVICE_MOTOR4        0x06    // 电机控制器4
#define CAN_DEVICE_MOTOR5        0x07    // 电机控制器5
#define CAN_DEVICE_MOTOR6        0x08    // 电机控制器6
#define CAN_DEVICE_MOTOR7        0x09    // 电机控制器7
#define CAN_DEVICE_MOTOR8        0x0A    // 电机控制器8

/* 本机设备ID定义 */
#define CAN_DEVICE_SELF           CAN_DEVICE_MOTOR1    // 本机设备ID，默认为电机控制器1

/* 命令字定义 */
#define CMD_MOTOR_STOP           0x00    // 停止电机
#define CMD_MOTOR_START          0x01    // 启动电机
#define CMD_MOTOR_LOCK           0x02    // 锁桨

/* 通信状态定义 */
#define CAN_DATA_STATE_SINGLE    0x01    // 单帧数据
#define CAN_DATA_STATE_GROUP     0x02    // 数据组


/* 故障标志位定义 - Status1 */
#define STATUS1_CONTROLLER_READY    0x01    // 控制器就绪
#define STATUS1_LOCK_ROTOR          0x02    // 锁桨状态(前旋翼无此项)
#define STATUS1_MOTOR_RUNNING       0x04    // 电机运行状态
#define STATUS1_CONTROLLER_FAULT    0x08    // 控制器故障状态
#define STATUS1_PUMP_RUNNING        0x10    // 液冷泵运行状态(无此项)
#define STATUS1_REDUCER_WARN        0x20    // 减速器过温黄区(无此项)
#define STATUS1_REDUCER_FAULT       0x40    // 减速器过温红区(无此项)

/* 故障标志位定义 - Status2 */
#define STATUS2_HALL_FAULT          0x01    // 霍尔传感器故障(无此项)
#define STATUS2_LOCK_FAULT          0x02    // 锁桨故障(无此项)
#define STATUS2_BUS_OVERVOL         0x04    // 母线过压
#define STATUS2_BUS_UNDERVOL        0x08    // 母线欠压
#define STATUS2_V28B_OVERVOL        0x10    // 28V备用过压
#define STATUS2_V28B_UNDERVOL       0x20    // 28V备用欠压
#define STATUS2_V28M_OVERVOL        0x40    // 28V主电源过压
#define STATUS2_V28M_UNDERVOL       0x80    // 28V主电源欠压

/* 故障标志位定义 - Status3 */
#define STATUS3_CAN_FAULT           0x01    // CAN通讯故障
#define STATUS3_POSITION_FAULT      0x02    // 位置传感器故障
#define STATUS3_PUMP_FAULT          0x04    // 液冷泵故障(无此项)
#define STATUS3_HW_OVERCUR          0x08    // 硬件过流故障
#define STATUS3_HW_SELFTEST         0x10    // 硬件自检故障
#define STATUS3_SPEED_TRACK         0x20    // 转速跟踪故障
#define STATUS3_SW_OVERCUR          0x40    // 软件过流故障

/* 故障标志位定义 - Status4 */
#define STATUS4_MOTOR_TEMP_WARN     0x01    // 电机过温黄区
#define STATUS4_MOTOR_TEMP_FAULT    0x02    // 电机过温红区
#define STATUS4_CTRL_TEMP_WARN      0x04    // 控制器过温
#define STATUS4_MOTOR_OVERSPD_WARN  0x08    // 电机超速告警
#define STATUS4_MOTOR_OVERSPD_FAULT 0x10    // 电机超速故障

/* 通信控制参数 */
#define CAN_RX_TIMEOUT_MS       2000     // 接收超时时间(ms)
#define CAN_TX_INTERVAL_MS      20       // 发送间隔(ms)

/* 警告级别定义 - 与sysTypeDef.h中保持一致 */
#define WARN_LEVEL_LOW     WARN_LEVEL_1    // 低级警告
#define WARN_LEVEL_HIGH    WARN_LEVEL_3    // 高级警告

/* CAN ID掩码定义 */
#define CAN_ID_MASK              0x1FFFF000  // 29位ID掩码
#define CAN_ID_IDE_FLAG          0x04        // IDE位标志(扩展帧)

/* CAN帧ID预定义  */
// 发送设备(D28~D25): 01H, 接收设备(D24~D21): 03H, 状态(D20~D18): 01H, 指令(D17~D12): 02H, 帧计数(D11~D0): 00H
// 计算: (01H<<25)|(03H<<21)|(01H<<18)|(02H<<12)|(00H<<0) = 0x02642000
#define CAN_RX_FRAME_ID ((uint32_t)((CAN_DEVICE_HOST << 25) | (CAN_DEVICE_SELF << 21) | (CAN_DATA_STATE_SINGLE << 18) | (0x02 << 12) | (0x00)))

// 发送帧ID: [电机控制器->主机] 数据组模式, 第一包，指令字段为02H(电机控制器反馈)
#define CAN_TX_FRAME1_ID ((uint32_t)((CAN_DEVICE_SELF << 25) | (CAN_DEVICE_HOST << 21) | (CAN_DATA_STATE_GROUP << 18) | (0x02 << 12) | (0x00)))

// 发送帧ID: [电机控制器->主机] 数据组模式, 第二包，指令字段为02H(电机控制器反馈)
#define CAN_TX_FRAME2_ID ((uint32_t)((CAN_DEVICE_SELF << 25) | (CAN_DEVICE_HOST << 21) | (CAN_DATA_STATE_GROUP << 18) | (0x02 << 12) | (0x01)))

// 发送帧ID: [电机控制器->主机] 数据组模式, 第三包，指令字段为02H(电机控制器反馈)
#define CAN_TX_FRAME3_ID ((uint32_t)((CAN_DEVICE_SELF << 25) | (CAN_DEVICE_HOST << 21) | (CAN_DATA_STATE_GROUP << 18) | (0x02 << 12) | (0x02)))

/* 电机转速限制 */
#define MOTOR_MAX_SPEED    12000    // 最大转速限制(rpm)
#define MOTOR_MIN_SPEED    -12000   // 最小转速限制(rpm)

/* CAN命令字定义 */
#define CMD_MOTOR_STOP     0x00     // 停止电机
#define CMD_MOTOR_START    0x01     // 启动电机  
#define CMD_MOTOR_LOCK     0x02     // 锁桨(前旋翼无此功能)

/* 状态字位定义  */
#define STATUS1_CONTROLLER_READY   0x01    // 控制器就绪
#define STATUS1_LOCK_ROTOR         0x02    // 锁桨状态
#define STATUS1_MOTOR_RUNNING      0x04    // 电机运行
#define STATUS1_CONTROLLER_FAULT   0x08    // 控制器故障

/* 性能优化预计算常量*/
#define EXPECTED_ID_MASKED          ((CAN_RX_FRAME_ID) & (CAN_ID_MASK))        // 预计算期望ID掩码
#define COMPAT_ID_MASKED            (0x40116224 & (CAN_ID_MASK))               // 预计算兼容ID掩码  
#define VALID_CMD_MASK              0x07                                       // 有效命令掩码 (0x00,0x01,0x02)

/* 快速数据访问宏定义 */
#define GET_SPEED_CMD(high, low)    ((int16_t)((high) << 8 | (low)))          // 快速速度解析

/* CAN接收命令结构体 */
typedef struct {
    uint32_t arbitration;            // 仲裁段 (固定ID)
    uint8_t msg_cnt;                 // 帧计数
    uint8_t cmd_word;                // 命令字
    uint8_t speed_cmd_low;           // 指令转速低字节
    uint8_t speed_cmd_high;          // 指令转速高字节
    uint8_t reserved[3];             // 保留字节
    uint8_t checksum;                // 校验和
} CanRxCmd_TypeDef;

/* 
 * 数据包字段说明
 * 
 * 接收数据包 (CAN_RX_FRAME_ID):
 * byte 0: 消息计数器 - 递增值，用于检测丢包
 * byte 1: 命令字 - 0x01:停止电机, 0x02:启动电机, 0x03:锁桨
 * byte 2-3: 速度指令 - int16_t类型，单位:rpm，低字节在前
 * byte 4-6: 保留字节 - 未使用
 * byte 7: 校验和 - 前7个字节的累加和取反加1
 *
 * 发送数据包1 (CAN_TX_FRAME1_ID):
 * byte 0: 消息计数器 - 递增值，用于检测丢包
 * byte 1-2: 转矩反馈 - int16_t类型，单位:0.1N·m/LSB，低字节在前
 * byte 3-4: 速度反馈 - int16_t类型，单位:rpm，低字节在前
 * byte 5: 母线电压 - uint8_t类型，单位:4V/LSB
 * byte 6: 28V主电源电压 - uint8_t类型，单位:0.2V/LSB
 * byte 7: 校验和 - 前7个字节的累加和取反加1
 *
 * 发送数据包2 (CAN_TX_FRAME2_ID):
 * byte 0: 消息计数器 - 递增值，用于检测丢包
 * byte 1: 28V备用电源电压 - uint8_t类型，单位:0.2V/LSB
 * byte 2: 母线电流 - uint8_t类型，单位:2A/LSB
 * byte 3: Q轴电流 - int8_t类型，单位:4A/LSB
 * byte 4: 减速器温度 - int8_t类型，单位:2℃/LSB (不适用于当前系统)
 * byte 5: 电机温度 - int8_t类型，单位:2℃/LSB
 * byte 6: 轴承温度 - int8_t类型，单位:2℃/LSB (不适用于当前系统)
 * byte 7: 校验和 - 前7个字节的累加和取反加1
 *
 * 发送数据包3 (CAN_TX_FRAME3_ID):
 * byte 0: 消息计数器 - 递增值，用于检测丢包
 * byte 1: 控制器温度 - int8_t类型，单位:2℃/LSB
 * byte 2: 冷却液温度 - int8_t类型，单位:2℃/LSB (不适用于当前系统)
 * byte 3: 状态字1 - 控制器就绪(0x01), 锁桨状态(0x02), 电机运行(0x04), 控制器故障(0x08)
 * byte 4: 状态字2 - 母线过压(0x01), 母线欠压(0x02), 28V备用电源过压(0x04), 28V备用电源欠压(0x08), 28V主电源过压(0x10), 28V主电源欠压(0x20)
 * byte 5: 状态字3 - CAN通信故障(0x01), 位置传感器故障(0x02), 硬件过流故障(0x04), 硬件自检故障(0x08), 转速跟踪故障(0x10), 软件过流故障(0x20)
 * byte 6: 状态字4 - 电机温度预警(0x01), 电机温度故障(0x02), 控制器温度预警(0x04), 电机超速预警(0x08), 电机超速故障(0x10)
 * byte 7: 校验和 - 前7个字节的累加和取反加1
 */

/* CAN接收双缓冲区结构体 - V2 */
typedef struct {
    CanRxCmd_TypeDef* receiving_buffer;      // 当前接收缓冲区指针
    CanRxCmd_TypeDef* processing_buffer;     // 当前处理缓冲区指针
    CanRxCmd_TypeDef buffer_a;               // 实际缓冲区A
    CanRxCmd_TypeDef buffer_b;               // 实际缓冲区B
    uint8_t last_msg_cnt;                    // 上一次的消息计数
    uint32_t last_rx_time;                   // 最后接收时间(ms)
    volatile uint8_t buffer_ready;           // 缓冲区就绪标志
} CanRxDblBuffer_TypeDef;

/* CAN通信状态管理结构体 */
typedef struct {
    uint8_t active_interface;        // 当前活动接口(0:CAN1, 1:CAN2)
    uint8_t auto_switch_enabled;     // CAN自动切换使能
    uint8_t timeout_flag;            // 超时标志
    uint8_t motor_cmd;               // 电机控制命令(解析后)
    int16_t speed_setpoint;          // 转速设定值(解析后)
    uint32_t can1_error_count;       // CAN1错误计数(丢包统计|校验失败)
    uint32_t can2_error_count;       // CAN2错误计数(丢包统计|校验失败)
} CanComStatus_TypeDef;

#define CanComStatus_DEFAULTS {0, 1, 0, 0, 0, 0, 0}
extern CanComStatus_TypeDef can_status;


/* 函数声明 */
void CAN1_CAN2_Init(void);
void CAN_User_Init(void);
void CAN_ManageCommunication(void);
void CAN_ProcessTxMessages(void);
void CAN_MonitorTimeout(void);
void CAN_SwitchInterface(CAN_Interface_TypeDef interface);
void CAN1_RX0_IRQ_Callback(void);
void  CAN2_RX0_IRQ_Callback(void);

/* 性能优化新增函数声明 */
void CAN_ProcessDelayedOperations(void);    // 处理被延迟的非关键操作

void CAN_TestTransmit(void);

uint8_t CAN_SendPacket1(void);
uint8_t CAN_SendPacket2(void);
uint8_t CAN_SendPacket3(void);
uint8_t Calculate_LRC(uint8_t *data, uint8_t len);

void CAN1_RX0_IRQHandler(void);
void CAN2_RX0_IRQHandler(void);

uint8_t UpdateStatusWord1(void);
uint8_t UpdateStatusWord2(void);
uint8_t UpdateStatusWord3(void);
uint8_t UpdateStatusWord4(void);

/* 状态字1位域定义 */
typedef struct {
    uint8_t controller_ready : 1;    // 控制器就绪
    uint8_t lock_rotor : 1;          // 锁桨状态(前旋翼无此项)
    uint8_t motor_running : 1;       // 电机运行状态
    uint8_t controller_fault : 1;    // 控制器故障状态
    uint8_t pump_running : 1;        // 液冷泵运行状态(无此项)
    uint8_t reducer_warn : 1;        // 减速器过温黄区(无此项)
    uint8_t reducer_fault : 1;       // 减速器过温红区(无此项)
    uint8_t reserved : 1;            // 保留位
} StatusWord1_Bits;

/* 状态字2位域定义 */
typedef struct {
    uint8_t hall_fault : 1;          // 霍尔传感器故障(无此项)
    uint8_t lock_fault : 1;          // 锁桨故障(无此项)
    uint8_t bus_overvol : 1;         // 母线过压
    uint8_t bus_undervol : 1;        // 母线欠压
    uint8_t v28b_overvol : 1;        // 28V备用过压
    uint8_t v28b_undervol : 1;       // 28V备用欠压
    uint8_t v28m_overvol : 1;        // 28V主电源过压
    uint8_t v28m_undervol : 1;       // 28V主电源欠压
} StatusWord2_Bits;

/* 状态字3位域定义 */
typedef struct {
    uint8_t can_fault : 1;           // CAN通讯故障
    uint8_t position_fault : 1;      // 位置传感器故障
    uint8_t pump_fault : 1;          // 液冷泵故障(无此项)
    uint8_t hw_overcur : 1;          // 硬件过流故障
    uint8_t hw_selftest : 1;         // 硬件自检故障
    uint8_t speed_track : 1;         // 转速跟踪故障
    uint8_t sw_overcur : 1;          // 软件过流故障
    uint8_t reserved : 1;            // 保留位
} StatusWord3_Bits;

/* 状态字4位域定义 */
typedef struct {
    uint8_t motor_temp_warn : 1;     // 电机过温黄区
    uint8_t motor_temp_fault : 1;    // 电机过温红区
    uint8_t ctrl_temp_warn : 1;      // 控制器过温
    uint8_t motor_overspd_warn : 1;  // 电机超速告警
    uint8_t motor_overspd_fault : 1; // 电机超速故障
    uint8_t reserved1 : 1;           // 保留位
    uint8_t reserved2 : 1;           // 保留位
    uint8_t reserved3 : 1;           // 保留位
} StatusWord4_Bits;

/* 状态字1联合体 */
typedef union {
    StatusWord1_Bits bits;           // 位访问
    uint8_t all;                     // 字节访问
} StatusWord1_TypeDef;

/* 状态字2联合体 */
typedef union {
    StatusWord2_Bits bits;           // 位访问
    uint8_t all;                     // 字节访问
} StatusWord2_TypeDef;

/* 状态字3联合体 */
typedef union {
    StatusWord3_Bits bits;           // 位访问
    uint8_t all;                     // 字节访问
} StatusWord3_TypeDef;

/* 状态字4联合体 */
typedef union {
    StatusWord4_Bits bits;           // 位访问
    uint8_t all;                     // 字节访问
} StatusWord4_TypeDef;

/* 系统状态字结构体 - 用于CAN通信 */
typedef struct {
    StatusWord1_TypeDef status1;     // 状态字1
    StatusWord2_TypeDef status2;     // 状态字2
    StatusWord3_TypeDef status3;     // 状态字3
    StatusWord4_TypeDef status4;     // 状态字4
} CanSystemStatus_TypeDef;

/* 全局状态字变量声明 */
extern CanSystemStatus_TypeDef can_system_status;

#endif /* __SYS_CAN_H */
