/**
  **************************************************************************
  * @file     usbd_app.c
  * @brief    USB设备应用程序
  * <AUTHOR>
  * @date     2025-01-01
  * @version  V1.2.0
  * @note     此文件包含USB设备的初始化、数据收发和中断处理等功能。
  * 
  * @optimization 极简性能优化说明：
  *         - 直接调用AnoPTv8HwRecvBytes处理接收数据
  *         - 删除所有状态管理字段：write_index、packet_count、total_bytes等
  *         - 缓冲区仅用作临时存储，每次从头开始使用
  **************************************************************************
  */

#include "usb_conf.h"
#include "usb_core.h"
#include "wk_system.h"
#include "cdc_class.h"
#include "cdc_desc.h"


#include "usb_app.h"
#include "HWInterface.h"
#include "MotorData.h"


/************************** USB_Drive USB驱动 接受命令函数 start********************************/

otg_core_type otg_core_struct_fs1;

// 定义USB接收状态结构体
rx_buffer_state_t usb_rx_state = {0};

/**
  * @brief  USB应用程序初始化
  * @param  none
  * @retval none
  */
void wk_usb_app_init(void)
{
    usbd_init(&otg_core_struct_fs1,
              USB_FULL_SPEED_CORE_ID,
              USB_OTG1_ID,
              &cdc_class_handler,
              &cdc_desc_handler);
}


/**
  * @brief  USB应用程序任务处理
  * @param  none
  * @retval none
  */
void wk_usb_app_task(void)
{
    uint16_t length = 0;

    /* fs1 device cdc - 直接接收并处理数据 */
    length = usb_vcp_get_rxdata(&otg_core_struct_fs1.dev, usb_rx_state.buffer);
    
    if(length > 0)
    {
        // 直接处理接收到的数据
        AnoPTv8HwRecvBytes(usb_rx_state.buffer, length);
    }
}


/**
  * @brief  USB中断处理函数
  * @param  none
  * @retval none
  */
void wk_otgfs1_irq_handler(void)
{
  usbd_irq_handler(&otg_core_struct_fs1);
}

/**
  * @brief  USB延时函数
  * @param  ms: 延时的毫秒数
  * @retval none
  */
void usb_delay_ms(uint32_t ms)
{

  wk_delay_ms(ms);

}

/************************** USB_Drive USB驱动 接受命令函数 end********************************/

/************************** USB_Drive USB驱动 发送函数 start ********************************/

/**
  * @brief  usb send data function
  * @param  data: pointer to data buffer
  * @param  len: data length
  * @retval error status: SUCCESS or ERROR
  */
void usb_send_data(uint8_t *data, uint16_t len)
{
  usb_vcp_send_data(&otg_core_struct_fs1.dev, data, len);
}

/************************** USB_Drive USB驱动 发送函数 end ********************************/





