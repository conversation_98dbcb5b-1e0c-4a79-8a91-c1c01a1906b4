/**********************************************************
 * @file     motor_math_utils.c
 * @brief    电机控制数学工具函数实现
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     包含坐标变换、SVPWM、PI控制器等核心算法
***********************************************************/

#include "motor_current_loop.h"
#include <math.h>

// 宏定义
#define SQ(x)   ((x) * (x))     // 平方宏

/*============================ 坐标变换函数 ============================*/

/**
 * @brief Clarke变换 (abc → αβ)
 * @param ia A相电流(A)
 * @param ib B相电流(A)
 * @param ic C相电流(A)
 * @param i_alpha α轴电流指针(A)
 * @param i_beta β轴电流指针(A)
 */
void clarke_transform(float ia, float ib, float ic, float *i_alpha, float *i_beta)
{
    /*
     * Clarke变换矩阵(功率不变):
     * [α]   (2/3) × [1    -1/2   -1/2 ] [a]
     * [β] =        [0   √3/2   -√3/2] [b]
     *                                  [c]
     * 
     * 简化形式(利用三相平衡条件 ia + ib + ic = 0):
     * α = ia
     * β = (1/√3) × ia + (2/√3) × ib
     */
    
    *i_alpha = ia;
    *i_beta = ONE_DIV_SQRT3 * ia + TWO_DIV_SQRT3 * ib;
}

/**
 * @brief Park变换 (αβ → dq)
 * @param i_alpha α轴电流(A)
 * @param i_beta β轴电流(A)
 * @param sin_theta 角度正弦值
 * @param cos_theta 角度余弦值
 * @param id d轴电流指针(A)
 * @param iq q轴电流指针(A)
 */
void park_transform(float i_alpha, float i_beta, float sin_theta, float cos_theta, float *id, float *iq)
{
    /*
     * Park变换矩阵:
     * [d]   [ cos(θ)  sin(θ)] [α]
     * [q] = [-sin(θ)  cos(θ)] [β]
     * 
     * 变换方程:
     * d = α×cos(θ) + β×sin(θ)
     * q = β×cos(θ) - α×sin(θ)
     */
    
    *id = cos_theta * i_alpha + sin_theta * i_beta;
    *iq = cos_theta * i_beta - sin_theta * i_alpha;
}

/**
 * @brief 反Park变换 (dq → αβ)
 * @param vd d轴电压(V)
 * @param vq q轴电压(V)
 * @param sin_theta 角度正弦值
 * @param cos_theta 角度余弦值
 * @param v_alpha α轴电压指针(V)
 * @param v_beta β轴电压指针(V)
 */
void inverse_park_transform(float vd, float vq, float sin_theta, float cos_theta, float *v_alpha, float *v_beta)
{
    /*
     * 反Park变换矩阵:
     * [α]   [cos(θ)  -sin(θ)] [d]
     * [β] = [sin(θ)   cos(θ)] [q]
     * 
     * 变换方程:
     * α = d×cos(θ) - q×sin(θ)
     * β = d×sin(θ) + q×cos(θ)
     */
    
    *v_alpha = cos_theta * vd - sin_theta * vq;
    *v_beta = sin_theta * vd + cos_theta * vq;
}

/*============================ SVPWM算法 ============================*/

/**
 * @brief 空间矢量PWM计算
 * @param v_alpha α轴电压(V)
 * @param v_beta β轴电压(V)
 * @param v_bus 母线电压(V)
 * @param duty_a A相占空比指针
 * @param duty_b B相占空比指针
 * @param duty_c C相占空比指针
 * @return 计算结果
 */
ret_t svpwm_calculate(float v_alpha, float v_beta, float v_bus, float *duty_a, float *duty_b, float *duty_c)
{
    if (duty_a == NULL || duty_b == NULL || duty_c == NULL || v_bus < 0.1f) {
        return RET_ERROR;
    }
    
    // 归一化电压
    float v_alpha_norm = v_alpha / v_bus;
    float v_beta_norm = v_beta / v_bus;
    
    // 计算扇区判断变量
    float a = v_beta_norm;
    float b = (SQRT3 * v_alpha_norm - v_beta_norm) * 0.5f;
    float c = (-SQRT3 * v_alpha_norm - v_beta_norm) * 0.5f;
    
    // 扇区判断
    uint8_t sector = 0;
    if (a > 0) sector += 1;
    if (b > 0) sector += 2;
    if (c > 0) sector += 4;
    
    // 时间计算变量
    float t1, t2, t0;
    float ta, tb, tc;
    
    switch (sector) {
        case 3: // 扇区1 (60°-120°)
            t1 = a;
            t2 = b;
            break;
            
        case 1: // 扇区2 (0°-60°)
            t1 = b;
            t2 = -c;
            break;
            
        case 5: // 扇区3 (300°-360°)
            t1 = -c;
            t2 = -a;
            break;
            
        case 4: // 扇区4 (240°-300°)
            t1 = -a;
            t2 = -b;
            break;
            
        case 6: // 扇区5 (180°-240°)
            t1 = -b;
            t2 = c;
            break;
            
        case 2: // 扇区6 (120°-180°)
            t1 = c;
            t2 = a;
            break;
            
        default:
            t1 = 0;
            t2 = 0;
            break;
    }
    
    // 零矢量时间
    t0 = 1.0f - t1 - t2;
    
    // 过调制处理
    if (t0 < 0) {
        float scale = 1.0f / (t1 + t2);
        t1 *= scale;
        t2 *= scale;
        t0 = 0;
    }
    
    // 计算三相占空比
    switch (sector) {
        case 3: // 扇区1
            ta = t0 * 0.5f;
            tb = ta + t1;
            tc = tb + t2;
            break;
            
        case 1: // 扇区2
            tb = t0 * 0.5f;
            ta = tb + t1;
            tc = ta + t2;
            break;
            
        case 5: // 扇区3
            tb = t0 * 0.5f;
            tc = tb + t1;
            ta = tc + t2;
            break;
            
        case 4: // 扇区4
            tc = t0 * 0.5f;
            tb = tc + t1;
            ta = tb + t2;
            break;
            
        case 6: // 扇区5
            tc = t0 * 0.5f;
            ta = tc + t1;
            tb = ta + t2;
            break;
            
        case 2: // 扇区6
            ta = t0 * 0.5f;
            tc = ta + t1;
            tb = tc + t2;
            break;
            
        default:
            ta = tb = tc = 0.5f;
            break;
    }
    
    *duty_a = ta;
    *duty_b = tb;
    *duty_c = tc;
    
    return RET_OK;
}

/*============================ PI控制器函数 ============================*/

/**
 * @brief PI控制器初始化
 * @param pi PI控制器结构体指针
 * @param kp 比例增益
 * @param ki 积分增益
 * @param max_integral 积分限幅
 * @param max_output 输出限幅
 */
void pi_controller_init(pi_controller_t *pi, float kp, float ki, float max_integral, float max_output)
{
    if (pi == NULL) return;
    
    pi->kp = kp;
    pi->ki = ki;
    pi->integral = 0.0f;
    pi->max_integral = max_integral;
    pi->max_output = max_output;
    pi->error_prev = 0.0f;
    pi->anti_windup_enabled = TRUE;
}

/**
 * @brief PI控制器更新
 * @param pi PI控制器结构体指针
 * @param error 误差值
 * @param dt 采样时间(s)
 * @return 控制器输出
 */
float pi_controller_update(pi_controller_t *pi, float error, float dt)
{
    if (pi == NULL) return 0.0f;
    
    // 比例项
    float p_term = pi->kp * error;
    
    // 积分项更新
    pi->integral += pi->ki * error * dt;
    
    // 积分限幅
    limit_value(&pi->integral, -pi->max_integral, pi->max_integral);
    
    // 计算输出
    float output = p_term + pi->integral;
    
    // 积分抗饱和
    if (pi->anti_windup_enabled) {
        if ((output > pi->max_output && error > 0) || 
            (output < -pi->max_output && error < 0)) {
            // 输出饱和且误差会加剧饱和，停止积分累积
            pi->integral -= pi->ki * error * dt;
        }
    }
    
    // 输出限幅
    limit_value(&output, -pi->max_output, pi->max_output);
    
    // 保存误差
    pi->error_prev = error;
    
    return output;
}

/**
 * @brief PI控制器重置
 * @param pi PI控制器结构体指针
 */
void pi_controller_reset(pi_controller_t *pi)
{
    if (pi == NULL) return;
    
    pi->integral = 0.0f;
    pi->error_prev = 0.0f;
}

/*============================ 工具函数 ============================*/

/**
 * @brief 数值限幅函数
 * @param value 待限幅的数值指针
 * @param min_val 最小值
 * @param max_val 最大值
 */
void limit_value(float *value, float min_val, float max_val)
{
    if (value == NULL) return;
    
    if (*value > max_val) {
        *value = max_val;
    } else if (*value < min_val) {
        *value = min_val;
    }
}

/**
 * @brief 角度归一化函数
 * @param angle 角度指针(rad)
 */
void normalize_angle(float *angle)
{
    if (angle == NULL) return;
    
    while (*angle > PI) {
        *angle -= TWO_PI;
    }
    while (*angle < -PI) {
        *angle += TWO_PI;
    }
}

/**
 * @brief 低通滤波器
 * @param input 输入值
 * @param prev_output 上次输出值
 * @param alpha 滤波系数(0-1)
 * @return 滤波后的输出
 */
float low_pass_filter(float input, float prev_output, float alpha)
{
    return alpha * input + (1.0f - alpha) * prev_output;
}

/**
 * @brief 角度差计算函数
 * @param angle1 角度1(rad)
 * @param angle2 角度2(rad)
 * @return 角度差(rad)，范围[-π, π]
 */
float angle_difference(float angle1, float angle2)
{
    float diff = angle1 - angle2;
    normalize_angle(&diff);
    return diff;
}
