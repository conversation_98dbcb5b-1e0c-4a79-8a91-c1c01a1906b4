#ifndef __ADC_PMSM_H
#define __ADC_PMSM_H

#include "at32a423.h"

/********** 硬件访问宏定义 **********/
// ADC寄存器访问宏
#define ADC_READ_PDT1()                 (ADC1->pdt1_bit.pdt1)  // 读取A相电流
#define ADC_READ_PDT2()                 (ADC1->pdt2_bit.pdt2)  // 读取B相电流
#define ADC_READ_PDT3()                 (ADC1->pdt3_bit.pdt3)  // 读取C相电流

// ADC控制宏
#define ADC_TRIGGER_PREEMPT()           adc_preempt_software_trigger_enable(ADC1, TRUE) // 使能ADC抢占
#define ADC_GET_PCCE_FLAG()             adc_flag_get(ADC1, ADC_PCCE_FLAG)  // 获取PCCE标志
#define ADC_CLEAR_PCCE_FLAG()           adc_flag_clear(ADC1, ADC_PCCE_FLAG) // 清除PCCE标志

/********** ADC电流采样相关常量定义 **********/
// ADC转换相关常量
#define ADC_TO_CURRENT_COEFF    0.38671875f    // ADC计数转电流系数
#define ADC_OFFSET_VOLTAGE      1000.0f        // ADC偏置

// 滤波相关常量
#define MOVING_AVG_WINDOW_SIZE  3              // 滑动平均窗口大小

// 一阶低通滤波器参数
#define LPF1_ALPHA              0.5652f        // fc=4.0kHz: α=0.5652
                                              // 采样频率20kHz时不同截止频率对应的系数：
                                              // fc=1.5kHz: α=0.3203
                                              // fc=2.0kHz: α=0.3858
                                              // fc=2.5kHz: α=0.4414
                                              // fc=3.0kHz: α=0.4886
                                              // fc=3.5kHz: α=0.5294
                                              // fc=4.0kHz: α=0.5652
                                              // fc=4.5kHz: α=0.5967
                                              // fc=5.0kHz: α=0.6247
                                              // fc=5.5kHz: α=0.6498
                                              // fc=6.0kHz: α=0.6724
                                              // fc=6.5kHz: α=0.6928
                                              // fc=7.0kHz: α=0.7115
                                              // fc=7.5kHz: α=0.7286
                                              // fc=8.0kHz: α=0.7444



/********** 数据结构定义 **********/

// ADC电流采样专用滑动平均滤波器状态
typedef struct {
    float buffer[MOVING_AVG_WINDOW_SIZE];  // 滑动窗口缓冲区
    uint8_t index;                         // 当前索引
    float sum;                             // 当前窗口和值
} ADC_MovingAvgFilter_t;

// ADC电流采样数据结构
typedef struct {
    // 原始ADC值
    uint16_t adc_raw[3];               // [0]=Ia, [1]=Ib, [2]=Ic

    // 电流值
    float current_raw[3];              // 原始电流值
    float current_corrected[3];        // 零序校正后的电流值
    float current_filtered[3];         // 最终滤波输出

    // 零飘补偿值
    float offset_compensation[3];      // 各通道零飘补偿

    // 滤波器参数
    float lpf_alpha;                   // 一阶低通滤波器系数 (可调参数)

    // 滤波器状态
    float lpf1_prev[3];                    // 一阶低通滤波器上次输出值
    ADC_MovingAvgFilter_t moving_avg[3];   // 滑动平均滤波器（备用）
} ADC_CurrentData_t;

/********** 全局变量声明 **********/
extern ADC_CurrentData_t g_adc_current_data;

/********** 电流访问宏定义 **********/
// 三相零飘补偿+滤波后电流访问宏（单位：A）
#define ADC_GET_IA_FILTERED()    (g_adc_current_data.current_filtered[0])  // A相滤波后电流
#define ADC_GET_IB_FILTERED()    (g_adc_current_data.current_filtered[1])  // B相滤波后电流
#define ADC_GET_IC_FILTERED()    (g_adc_current_data.current_filtered[2])  // C相滤波后电流

// 三相原始电流访问宏（单位：A，仅做零飘补偿）
#define ADC_GET_IA_RAW()         (g_adc_current_data.current_raw[0])       // A相原始电
#define ADC_GET_IB_RAW()         (g_adc_current_data.current_raw[1])       // B相原始电流
#define ADC_GET_IC_RAW()         (g_adc_current_data.current_raw[2])       // C相原始电流

/********** 函数声明 **********/
void ADC_PMSM_Init(void);
void ADC_PMSM_ProcessCurrents(float* ia, float* ib, float* ic);
void ADC_PMSM_CalibrateOffset(void);

// 内联辅助函数
static inline float MovingAvg_Update(ADC_MovingAvgFilter_t* filter, float input);

#endif /* __ADC_PMSM_H */
