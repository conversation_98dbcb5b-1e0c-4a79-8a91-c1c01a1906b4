/**********************************************************
  * @file     RS422_CU.h
  * @brief    RS422控制单元头文件
  * <AUTHOR>
  * @version  V1.0.0
  * @date     2024-07-01
  * @note     提供RS422通信控制电机的接口
************************************************************/

#ifndef __RS422_CU_H
#define __RS422_CU_H

#include "SysFSM.h"

/* 协议定义 */
#define RS422_FRAME_START    0x4A    // 起始符
#define RS422_FRAME_END      0x55    // 结束符
#define RS422_BROADCAST_ADDR 0x00    // 广播地址

#define RS422_DEVICE_ADDR    0x01    // 本设备地址

/* 命令类型定义 */
#define CMD_START           0x01    // 启动电机
#define CMD_STOP            0x02    // 停止电机
#define CMD_SPEED           0x03    // 调速命令
#define CMD_REQUEST         0x04    // 请求状态

/* 缓冲区定义 */
#define RS422_TX_BUFFER_SIZE 32     // 发送缓冲区大小
#define RS422_FRAME_MAX_SIZE 32     // 帧最大长度

/* 状态机状态定义 */
typedef enum {
    FRAME_STATE_IDLE = 0,        // 空闲状态，等待帧起始
    FRAME_STATE_ADDR = 1,        // 接收地址
    FRAME_STATE_CMD = 2,         // 接收命令
    FRAME_STATE_LEN = 3,         // 接收数据长度
    FRAME_STATE_DATA = 4,        // 接收数据
    FRAME_STATE_CHECKSUM = 5,    // 接收校验和
    FRAME_STATE_END = 6          // 接收帧结束
} RS422_FrameState_t;

/* 帧格式结构体 */
typedef struct {
    uint8_t start;            // 起始符
    uint8_t address;          // 设备地址
    uint8_t command;          // 命令类型
    uint8_t data_len;         // 数据长度
    uint8_t data[RS422_FRAME_MAX_SIZE]; // 数据域
    uint8_t checksum;         // 校验和
    uint8_t end;              // 结束符
} RS422_Frame_TypeDef;

/* RS422通信控制单元结构体 */
typedef struct {
    uint8_t tx_buffer[RS422_TX_BUFFER_SIZE]; // 发送缓冲区
    uint16_t tx_count;                       // 发送计数
    RS422_Frame_TypeDef frame;               // 当前处理的帧
} RS422_CU_TypeDef;

/* 函数声明 */
// 初始化
void RS422_CU_Init(void);

// 发送
void RS422_CU_SendResponse(uint8_t cmd, uint8_t* data, uint8_t len);
uint8_t RS422_CU_CalculateChecksum(uint8_t* data, uint16_t len);

// 命令处理函数
void RS422_CU_StartMotor(void);
void RS422_CU_StopMotor(void);
void RS422_CU_SetSpeed(uint16_t speed);
void RS422_CU_RequestStatus(uint8_t param);

/* 外部变量声明 */
extern RS422_CU_TypeDef g_rs422_cu;

#endif /* __RS422_CU_H */