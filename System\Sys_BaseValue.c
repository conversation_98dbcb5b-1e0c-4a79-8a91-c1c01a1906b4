// #include "Sys_BaseValue.h"

// #include "MathBasic.h"
// #include "Sys_SysParameter.h"

// #define KOmegBase  (2.0f * PI / 60.0f)  // RPM转换为rad/s

// ARM_TypeSysBaseValue ARM_SysBaseValue;
// /**
//  * @brief 系统基值计算函数
//  * @details 计算系统各物理量基值:
//  *          1. 电压基值(网侧、电机侧)
//  *          2. 转速基值(弧度)
//  *          3. 电流基值
//  *          4. 角度基值
//  *          5. 功率基值
//  *          6. 阻抗基值
//  *          7. 电感基值
//  *          8. 磁链基值
//  *          9. 机械角度基值
//  *          10. 转矩基值
//  *          11. 转动惯量基值
//  *          12. 摩擦系数基值
//  * @param[in] p 系统基值结构体指针
//  */

// void ARM_fnSysBaseValueCal(ARM_TypeSysBaseValue *p)
// {
// 	float Temp1,Temp2;//
// 					 //
// 	p->fVoltBaseValueGrid = SQRT2By3 * SysRatedParameter.fVFDInputVolatge;	                 	                 
// 	//====电压基值====
// 	p->fVoltBaseValue = SQRT2By3 * SysRatedParameter.fMotorRatedVoltage;
		 
// 	//====转速基值，弧度====
// 	p->fSpeedBaseValue = PI2 * SysRatedParameter.fMotorRatedFre;
		 
// 	//====电流基值====                                                       
// 	p->fCurrBaseValue = SQRT2By3 * SysRatedParameter.fMotorRatedPower * 1000.0 / SysRatedParameter.fMotorRatedVoltage;		
	 
// 	//====系统角度基值====
// 	p->fThetaBaseValue = PI2;	 
	 
// 	//====功率基值(3/2) * Vbase * Ibase====
// 	p->fPowerBaseValue = 1.5 * p->fVoltBaseValue * p->fCurrBaseValue;
	 
// 	//====系统阻抗基值Ubase/Ibase====
// 	p->fZBaseValue = p->fVoltBaseValue/p->fCurrBaseValue;
		 
// 	//====系统电感基值====
// 	p->fLBaseValue = p->fVoltBaseValue/(p->fSpeedBaseValue * p->fCurrBaseValue);
		 
// 	//====系统磁链基值====
// 	p->fPhirBaseValue = p->fVoltBaseValue/p->fSpeedBaseValue;
		 
// 	//====机械角度基值====
// 	p->fOmegBaseValue = SysRatedParameter.fMotorRateSpeed * KOmegBase;
	 
// 	//====系统转矩基值====
// 	p->fTeBaseValue = p->fPowerBaseValue/p->fOmegBaseValue;
	 
// 	//====系统转动惯量基值====
// 	Temp1 = POW2(SysRatedParameter.fMotorPoleNum);//
// 											   //	 
// 	Temp2 = POW3(p->fSpeedBaseValue);//
// 								  //
// 	p->fJBaseValue = (Temp1 * p->fPowerBaseValue)/Temp2;
	 
// 	//====系统摩擦系数基值====
// 	p->fFBaseValue = p->fTeBaseValue/p->fOmegBaseValue;
// }
