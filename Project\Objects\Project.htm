<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Tue Aug  5 10:19:57 2025
<BR><P>
<H3>Maximum Stack Usage =        392 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[c9]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[7f]">ACC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7f]">ACC_IRQHandler</a><BR>
 <LI><a href="#[f6]">Ad2s_DecoderReadGeneralMode</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[f6]">Ad2s_DecoderReadGeneralMode</a><BR>
 <LI><a href="#[33]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[33]">BusFault_Handler</a><BR>
 <LI><a href="#[31]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[31]">HardFault_Handler</a><BR>
 <LI><a href="#[32]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[32]">MemManage_Handler</a><BR>
 <LI><a href="#[34]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[34]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[7f]">ACC_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4b]">ADC1_IRQHandler</a> from sys_isr_controller.o(.text.ADC1_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[2a]">ADC_Drive_Init</a> from sensor_drive.o(.text.ADC_Drive_Init) referenced 2 times from sensor_drive.o(.data.gADC_Manager)
 <LI><a href="#[2d]">ADC_Get_PT1000_Temp</a> from lbq_design.o(.text.ADC_Get_PT1000_Temp) referenced 2 times from sensor_drive.o(.data.gADC_Manager)
 <LI><a href="#[2c]">ADC_Get_PT100_Temp</a> from lbq_design.o(.text.ADC_Get_PT100_Temp) referenced 2 times from sensor_drive.o(.data.gADC_Manager)
 <LI><a href="#[2b]">ADC_Process_500us</a> from sensor_drive.o(.text.ADC_Process_500us) referenced 2 times from sensor_drive.o(.data.gADC_Manager)
 <LI><a href="#[8d]">AnoPTv8CmdFun_MotorClearFault</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault) referenced from motorcmd.o(.rodata._pCmdInfoMotorClearFault)
 <LI><a href="#[8f]">AnoPTv8CmdFun_MotorGetFault</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault) referenced from motorcmd.o(.rodata._pCmdInfoMotorGetFault)
 <LI><a href="#[8e]">AnoPTv8CmdFun_MotorReset</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorReset) referenced from motorcmd.o(.rodata._pCmdInfoMotorEStop)
 <LI><a href="#[90]">AnoPTv8CmdFun_MotorSelfTest</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest) referenced from motorcmd.o(.rodata._pCmdInfoMotorSelfTest)
 <LI><a href="#[91]">AnoPTv8CmdFun_MotorStart</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorStart) referenced from motorcmd.o(.rodata._pCmdInfoMotorStart)
 <LI><a href="#[92]">AnoPTv8CmdFun_MotorStop</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) referenced from motorcmd.o(.rodata._pCmdInfoMotorStop)
 <LI><a href="#[93]">AnoPTv8CmdFun_WaveCtrl</a> from motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) referenced from motorcmd.o(.rodata._pCmdInfoWaveCtrl)
 <LI><a href="#[94]">AnoPTv8CmdFun_WaveStopAll</a> from motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) referenced from motorcmd.o(.rodata._pCmdInfoWaveStopAll)
 <LI><a href="#[33]">BusFault_Handler</a> from at32a423_int.o(.text.BusFault_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4d]">CAN1_RX0_IRQHandler</a> from at32a423_int.o(.text.CAN1_RX0_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4e]">CAN1_RX1_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4f]">CAN1_SE_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4c]">CAN1_TX_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[72]">CAN2_RX0_IRQHandler</a> from at32a423_int.o(.text.CAN2_RX0_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[73]">CAN2_RX1_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[74]">CAN2_SE_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[71]">CAN2_TX_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[3e]">CRM_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[9e]">Check_EmergencyStop</a> from sysfsm.o(.text.Check_EmergencyStop) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[9f]">Check_FaultCondition</a> from sysfsm.o(.text.Check_FaultCondition) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[9b]">Check_Fault_Unrecoverable</a> from sysfsm.o(.text.Check_Fault_Unrecoverable) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[a2]">Check_Host_SelfTest_Command</a> from sysfsm.o(.text.Check_Host_SelfTest_Command) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[96]">Check_InitComplete</a> from sysfsm.o(.text.Check_InitComplete) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[95]">Check_PowerStable</a> from sysfsm.o(.text.Check_PowerStable) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[a1]">Check_RecoveryCondition</a> from sysfsm.o(.text.Check_RecoveryCondition) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[99]">Check_SelfTestFail</a> from sysfsm.o(.text.Check_SelfTestFail) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[97]">Check_SelfTestPass</a> from sysfsm.o(.text.Check_SelfTestPass) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[98]">Check_SelfTestRetry</a> from sysfsm.o(.text.Check_SelfTestRetry) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[a0]">Check_ShutdownCondition</a> from sysfsm.o(.text.Check_ShutdownCondition) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[9c]">Check_StartCondition</a> from sysfsm.o(.text.Check_StartCondition) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[9d]">Check_StopCondition</a> from sysfsm.o(.text.Check_StopCondition) referenced 2 times from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[9a]">Check_SystemNormal</a> from sysfsm.o(.text.Check_SystemNormal) referenced from sysfsm.o(.rodata.state_transitions)
 <LI><a href="#[44]">DMA1_Channel1_IRQHandler</a> from at32a423_int.o(.text.DMA1_Channel1_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[45]">DMA1_Channel2_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[46]">DMA1_Channel3_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[47]">DMA1_Channel4_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[48]">DMA1_Channel5_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[49]">DMA1_Channel6_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[4a]">DMA1_Channel7_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6c]">DMA2_Channel1_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6d]">DMA2_Channel2_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6e]">DMA2_Channel3_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6f]">DMA2_Channel4_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[70]">DMA2_Channel5_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[76]">DMA2_Channel6_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[77]">DMA2_Channel7_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[7e]">DMAMUX_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[36]">DebugMon_Handler</a> from at32a423_int.o(.text.DebugMon_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[62]">ERTCAlarm_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[3c]">ERTC_WKUP_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[3f]">EXINT0_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[61]">EXINT15_10_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[40]">EXINT1_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[41]">EXINT2_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[42]">EXINT3_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[43]">EXINT4_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[50]">EXINT9_5_IRQHandler</a> from at32a423_int.o(.text.EXINT9_5_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[3d]">FLASH_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[7b]">FPU_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[c]">Filter_calc</a> from mathbasic.o(.text.Filter_calc) referenced 2 times from sys_isr_controller.o(.data.Filter_speed)
 <LI><a href="#[31]">HardFault_Handler</a> from at32a423_int.o(.text.HardFault_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[59]">I2C1_ERR_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[58]">I2C1_EVT_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[5b]">I2C2_ERR_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[5a]">I2C2_EVT_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[7a]">I2C3_ERR_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[79]">I2C3_EVT_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[32]">MemManage_Handler</a> from at32a423_int.o(.text.MemManage_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[30]">NMI_Handler</a> from at32a423_int.o(.text.NMI_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[75]">OTGFS1_IRQHandler</a> from at32a423_int.o(.text.OTGFS1_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[63]">OTGFS1_WKUP_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[3a]">PVM_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[2e]">Para_derive</a> from motor_vectorcontrol.o(.text.Para_derive) referenced 2 times from motor_vectorcontrol.o(.data.para)
 <LI><a href="#[37]">PendSV_Handler</a> from at32a423_int.o(.text.PendSV_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[2f]">Reset_Handler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[5c]">SPI1_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[5d]">SPI2_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[67]">SPI3_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[35]">SVC_Handler</a> from at32a423_int.o(.text.SVC_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[1]">SelfTest_Init</a> from start_self_test.o(.text.SelfTest_Init) referenced 2 times from start_self_test.o(.data..L_MergedGlobals)
 <LI><a href="#[2]">SelfTest_Reset</a> from start_self_test.o(.text.SelfTest_Reset) referenced 2 times from start_self_test.o(.data..L_MergedGlobals)
 <LI><a href="#[88]">State_Fault_Handler</a> from sysfsm.o(.text.State_Fault_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[85]">State_Hold_Handler</a> from sysfsm.o(.text.State_Hold_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[83]">State_InitExternal_Handler</a> from sysfsm.o(.text.State_InitExternal_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[82]">State_PowerOn_Handler</a> from sysfsm.o(.text.State_PowerOn_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[86]">State_Ready_Handler</a> from sysfsm.o(.text.State_Ready_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[87]">State_Running_Handler</a> from sysfsm.o(.text.State_Running_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[84]">State_SelfTest_Handler</a> from sysfsm.o(.text.State_SelfTest_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[89]">State_Shutdown_Handler</a> from sysfsm.o(.text.State_Shutdown_Handler) referenced from sysfsm.o(.text.System_StatusUpdate)
 <LI><a href="#[38]">SysTick_Handler</a> from at32a423_int.o(.text.SysTick_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[80]">SystemInit</a> from system_at32a423.o(.text.SystemInit) referenced from startup_at32a423.o(.text)
 <LI><a href="#[3b]">TAMP_STAMP_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[64]">TMR12_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[65]">TMR13_GLOBAL_IRQHandler</a> from at32a423_int.o(.text.TMR13_GLOBAL_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[66]">TMR14_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[51]">TMR1_BRK_TMR9_IRQHandler</a> from at32a423_int.o(.text.TMR1_BRK_TMR9_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[54]">TMR1_CH_IRQHandler</a> from at32a423_int.o(.text.TMR1_CH_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[52]">TMR1_OVF_TMR10_IRQHandler</a> from at32a423_int.o(.text.TMR1_OVF_TMR10_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[53]">TMR1_TRG_HALL_TMR11_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[55]">TMR2_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[56]">TMR3_GLOBAL_IRQHandler</a> from at32a423_int.o(.text.TMR3_GLOBAL_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[57]">TMR4_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6a]">TMR6_DAC_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[6b]">TMR7_GLOBAL_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[8b]">Task_1ms</a> from sysfsm.o(.text.Task_1ms) referenced 2 times from sysfsm.o(.text.Timer_Tasks_Init)
 <LI><a href="#[8c]">Task_1s</a> from sysfsm.o(.text.Task_1s) referenced 2 times from sysfsm.o(.text.Timer_Tasks_Init)
 <LI><a href="#[8a]">Task_500us</a> from sysfsm.o(.text.Task_500us) referenced 2 times from sysfsm.o(.text.Timer_Tasks_Init)
 <LI><a href="#[5e]">USART1_IRQHandler</a> from rs422_cu.o(.text.USART1_IRQHandler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[5f]">USART2_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[60]">USART3_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[68]">USART4_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[69]">USART5_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[78]">USART6_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[7c]">USART7_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[7d]">USART8_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[34]">UsageFault_Handler</a> from at32a423_int.o(.text.UsageFault_Handler) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[d]">Vector_ctrl_ResetTs</a> from motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[10]">Vector_ctrl_calc</a> from motor_vectorcontrol.o(.text.Vector_ctrl_calc) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[e]">Vector_ctrl_init</a> from motor_vectorcontrol.o(.text.Vector_ctrl_init) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[f]">Vector_ctrl_reset</a> from motor_vectorcontrol.o(.text.Vector_ctrl_reset) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[11]">VoltageBalance_calc</a> from motor_vectorcontrol.o(.text.VoltageBalance_calc) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[39]">WWDT_IRQHandler</a> from startup_at32a423.o(.text) referenced from startup_at32a423.o(RESET)
 <LI><a href="#[81]">__main</a> from __main.o(!!!main) referenced from startup_at32a423.o(.text)
 <LI><a href="#[3]">clarke_calc</a> from motor_vectorcontrol.o(.text.clarke_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.3)
 <LI><a href="#[18]">class_clear_handler</a> from cdc_class.o(.text.class_clear_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1b]">class_ept0_rx_handler</a> from cdc_class.o(.text.class_ept0_rx_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1a]">class_ept0_tx_handler</a> from cdc_class.o(.text.class_ept0_tx_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1f]">class_event_handler</a> from cdc_class.o(.text.class_event_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1c]">class_in_handler</a> from cdc_class.o(.text.class_in_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[17]">class_init_handler</a> from cdc_class.o(.text.class_init_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1d]">class_out_handler</a> from cdc_class.o(.text.class_out_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[19]">class_setup_handler</a> from cdc_class.o(.text.class_setup_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[1e]">class_sof_handler</a> from cdc_class.o(.text.class_sof_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[b]">fnAISample</a> from sysctl_analogprocess.o(.text.fnAISample) referenced 2 times from sysctl_globalvar.o(.data.AnalogInput)
 <LI><a href="#[13]">fnSysBaseValueCal</a> from sysctl_analogprocess.o(.text.fnSysBaseValueCal) referenced 2 times from sysctl_globalvar.o(.data.SysBaseValue)
 <LI><a href="#[14]">fnSysMooreCal</a> from sysctl_sysmoore.o(.text.fnSysMooreCal) referenced 2 times from sysctl_globalvar.o(.data.SysMoore)
 <LI><a href="#[15]">fnSysOffsetInit</a> from sysctl_analogprocess.o(.text.fnSysOffsetInit) referenced 2 times from sysctl_globalvar.o(.data.SysSampOffset)
 <LI><a href="#[16]">fnSysOffsetParameterCal</a> from sysctl_analogprocess.o(.text.fnSysOffsetParameterCal) referenced 2 times from sysctl_globalvar.o(.data.SysSampOffset)
 <LI><a href="#[29]">get_device_config_string</a> from cdc_desc.o(.text.get_device_config_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[22]">get_device_configuration</a> from cdc_desc.o(.text.get_device_configuration) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[20]">get_device_descriptor</a> from cdc_desc.o(.text.get_device_descriptor) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[28]">get_device_interface_string</a> from cdc_desc.o(.text.get_device_interface_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[24]">get_device_lang_id</a> from cdc_desc.o(.text.get_device_lang_id) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[25]">get_device_manufacturer_string</a> from cdc_desc.o(.text.get_device_manufacturer_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[23]">get_device_other_speed</a> from cdc_desc.o(.text.get_device_other_speed) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[26]">get_device_product_string</a> from cdc_desc.o(.text.get_device_product_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[21]">get_device_qualifier</a> from cdc_desc.o(.text.get_device_qualifier) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[27]">get_device_serial_string</a> from cdc_desc.o(.text.get_device_serial_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[4]">iclarke_calc</a> from motor_vectorcontrol.o(.text.iclarke_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.3)
 <LI><a href="#[6]">ipark_calc</a> from motor_vectorcontrol.o(.text.ipark_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.4)
 <LI><a href="#[0]">lpfl_calc</a> from motor_vectorcontrol.o(.text.lpfl_calc) referenced 6 times from motor_vectorcontrol.o(.data..L_MergedGlobals)
 <LI><a href="#[0]">lpfl_calc</a> from motor_vectorcontrol.o(.text.lpfl_calc) referenced 10 times from motor_vectorcontrol.o(.data..L_MergedGlobals.1)
 <LI><a href="#[0]">lpfl_calc</a> from motor_vectorcontrol.o(.text.lpfl_calc) referenced 10 times from motor_vectorcontrol.o(.data..L_MergedGlobals.2)
 <LI><a href="#[0]">lpfl_calc</a> from motor_vectorcontrol.o(.text.lpfl_calc) referenced 4 times from motor_vectorcontrol.o(.data..L_MergedGlobals.3)
 <LI><a href="#[12]">marsest_calc</a> from motor_vectorcontrol.o(.text.marsest_calc) referenced 2 times from sysctl_globalvar.o(.data.SynMotorVc)
 <LI><a href="#[5]">park_calc</a> from motor_vectorcontrol.o(.text.park_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.4)
 <LI><a href="#[7]">pi_current_const_calc</a> from motor_vectorcontrol.o(.text.pi_current_const_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.4)
 <LI><a href="#[8]">pi_flux_calc</a> from motor_vectorcontrol.o(.text.pi_flux_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.5)
 <LI><a href="#[a]">pi_fun_calc</a> from motor_vectorcontrol.o(.text.pi_fun_calc) referenced 4 times from motor_vectorcontrol.o(.data..L_MergedGlobals.6)
 <LI><a href="#[a]">pi_fun_calc</a> from motor_vectorcontrol.o(.text.pi_fun_calc) referenced 4 times from motor_vectorcontrol.o(.data..L_MergedGlobals.7)
 <LI><a href="#[a]">pi_fun_calc</a> from motor_vectorcontrol.o(.text.pi_fun_calc) referenced 4 times from motor_vectorcontrol.o(.data..L_MergedGlobals.8)
 <LI><a href="#[a]">pi_fun_calc</a> from motor_vectorcontrol.o(.text.pi_fun_calc) referenced 4 times from motor_vectorcontrol.o(.data..L_MergedGlobals.9)
 <LI><a href="#[a]">pi_fun_calc</a> from motor_vectorcontrol.o(.text.pi_fun_calc) referenced 2 times from motor_vectorcontrol.o(.data.pi_isxz)
 <LI><a href="#[9]">pi_speed_calc</a> from motor_vectorcontrol.o(.text.pi_speed_calc) referenced 2 times from motor_vectorcontrol.o(.data..L_MergedGlobals.5)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[81]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(.text)
</UL>
<P><STRONG><a name="[a3]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[a5]"></a>__scatterload_rt2</STRONG> (Thumb, 84 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[202]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[203]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[204]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[205]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[206]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[207]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[208]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))

<P><STRONG><a name="[a6]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[209]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[af]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a8]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[aa]"></a>__rt_lib_init_heap_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000007))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_heap_2 &rArr; _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[20a]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[20b]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[20c]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[20d]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[20e]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[20f]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[210]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[211]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[212]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[213]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[214]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[215]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[216]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[217]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[218]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[219]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[21a]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[21b]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[21c]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[21d]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[21e]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[b4]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[21f]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[220]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[221]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[222]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[223]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[224]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[225]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[a4]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[226]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[ac]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[ae]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[227]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[b0]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[228]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[ca]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[b3]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[229]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[b5]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[2f]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>ACC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN1_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>CAN2_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>CRM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>DMA2_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>DMA2_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>DMA2_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>DMA2_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>DMAMUX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>ERTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>ERTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>EXINT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>EXINT15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>EXINT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>EXINT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>EXINT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXINT4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>I2C1_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>I2C1_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C2_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C2_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>I2C3_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>I2C3_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>OTGFS1_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>TMR12_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>TMR14_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TMR1_TRG_HALL_TMR11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>TMR2_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>TMR4_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>TMR6_DAC_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>TMR7_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>USART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>USART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>USART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>WWDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a423.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[c9]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_at32a423.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[b7]"></a>calloc</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, calloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = calloc &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_Init
</UL>

<P><STRONG><a name="[ba]"></a>free</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, h1_free.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = free
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>

<P><STRONG><a name="[15c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
</UL>

<P><STRONG><a name="[22a]"></a>_ll_udiv</STRONG> (Thumb, 240 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[a7]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
</UL>

<P><STRONG><a name="[de]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>

<P><STRONG><a name="[bd]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[22b]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[22c]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[22d]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[22e]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sic_SelfTest_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS422_CU_Init
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Drive_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[22f]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[230]"></a>__rt_memclr_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[c5]"></a>_memset_w</STRONG> (Thumb, 74 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>

<P><STRONG><a name="[231]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[233]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>__rt_heap_descriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_heap_descriptor_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>

<P><STRONG><a name="[234]"></a>__use_no_heap</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>__heap$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>_terminate_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[237]"></a>_init_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[bf]"></a>__Heap_Full</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[c1]"></a>__Heap_Broken</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[ab]"></a>_init_alloc</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Initialize
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_heap_2
</UL>

<P><STRONG><a name="[b8]"></a>malloc</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, h1_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calloc
</UL>

<P><STRONG><a name="[bc]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calloc
</UL>

<P><STRONG><a name="[238]"></a>__rt_memclr</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>_memset</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[239]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[c8]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[23a]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[c3]"></a>__Heap_Initialize</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, h1_init.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[23b]"></a>_maybe_terminate_alloc</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, maybetermalloc1.o(.text), UNUSED)

<P><STRONG><a name="[23c]"></a>__Heap_DescSize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, h1_init.o(.text), UNUSED)

<P><STRONG><a name="[c0]"></a>__Heap_ProvideMemory</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, h1_extend.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
</UL>

<P><STRONG><a name="[c2]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Broken
</UL>

<P><STRONG><a name="[ad]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[b2]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[c7]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[c6]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[b6]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>

<P><STRONG><a name="[23d]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[23e]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[cb]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
</UL>

<P><STRONG><a name="[23f]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[cd]"></a>AD2S1210_Init</STRONG> (Thumb, 316 bytes, Stack size 16 bytes, ad2s1212_spi.o(.text.AD2S1210_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD2S1210_Init &rArr; AD2S1210_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[d1]"></a>AD2S1210_READ</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD2S1210_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[d0]"></a>AD2S1210_WRITE</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_WRITE))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD2S1210_WRITE
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[4b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 488 bytes, Stack size 40 bytes, sys_isr_controller.o(.text.ADC1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ADC1_IRQHandler &rArr; GetRotorAngle &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_flag_get
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bus_Voltage
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorSpeed
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorAngle
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetElectricalAngle
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[12b]"></a>ADC_DMA_Complete_Callback</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, sensor_drive.o(.text.ADC_DMA_Complete_Callback))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[2a]"></a>ADC_Drive_Init</STRONG> (Thumb, 252 bytes, Stack size 16 bytes, sensor_drive.o(.text.ADC_Drive_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + Unknown Stack Size
<LI>Call Chain = ADC_Drive_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MA_Filter_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sensor_drive.o(.data.gADC_Manager)
</UL>
<P><STRONG><a name="[13e]"></a>ADC_Get_Enable_Flag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, sensor_drive.o(.text.ADC_Get_Enable_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelfTest_Manager
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[2d]"></a>ADC_Get_PT1000_Temp</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, lbq_design.o(.text.ADC_Get_PT1000_Temp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Get_PT1000_Temp
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sensor_drive.o(.data.gADC_Manager)
</UL>
<P><STRONG><a name="[2c]"></a>ADC_Get_PT100_Temp</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, lbq_design.o(.text.ADC_Get_PT100_Temp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Get_PT100_Temp
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sensor_drive.o(.data.gADC_Manager)
</UL>
<P><STRONG><a name="[2b]"></a>ADC_Process_500us</STRONG> (Thumb, 564 bytes, Stack size 24 bytes, sensor_drive.o(.text.ADC_Process_500us))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ADC_Process_500us &rArr; Check_Temp_Sensor_Valid
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Get_PT1000_Temp
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Get_PT100_Temp
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MA_Filter_Update
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Temp_Sensor_Valid
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sensor_drive.o(.data.gADC_Manager)
</UL>
<P><STRONG><a name="[e1]"></a>AT32A423_system_init</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, overall_init.o(.text.AT32A423_system_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = AT32A423_system_init &rArr; wk_usart1_init &rArr; usart_init &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_enable
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma_channel_config
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_otgfs1_init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_acc_init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_periph_clock_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f6]"></a>Ad2s_DecoderReadGeneralMode</STRONG> (Thumb, 252 bytes, Stack size 32 bytes, sysctl_ioad2s1210.o(.text.Ad2s_DecoderReadGeneralMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = Ad2s_DecoderReadGeneralMode &rArr;  Ad2s_DecoderReadGeneralMode (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_GetAngle
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
</UL>

<P><STRONG><a name="[f7]"></a>Ad2s_GetAngle</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, sysctl_ioad2s1210.o(.text.Ad2s_GetAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Ad2s_GetAngle &rArr; Ad2s_DecoderReadGeneralMode &rArr;  Ad2s_DecoderReadGeneralMode (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetElectricalAngle
</UL>

<P><STRONG><a name="[f8]"></a>AnoPTv8CmdFrameAnl</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>

<P><STRONG><a name="[10e]"></a>AnoPTv8CmdGetCount</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdGetCount))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
</UL>

<P><STRONG><a name="[10f]"></a>AnoPTv8CmdGetInfo</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdGetInfo))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
</UL>

<P><STRONG><a name="[134]"></a>AnoPTv8CmdRegister</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdRegister))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorCmdInit
</UL>

<P><STRONG><a name="[112]"></a>AnoPTv8GetParamType</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8GetParamType))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
</UL>

<P><STRONG><a name="[fd]"></a>AnoPTv8HwInit</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, hwinterface.o(.text.AnoPTv8HwInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AnoPTv8HwInit &rArr; MotorCmdInit
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorCmdInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorParamsInit
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[10c]"></a>AnoPTv8HwParCmdRecvCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[10b]"></a>AnoPTv8HwParCmdResetParameter</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwParCmdResetParameter))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[100]"></a>AnoPTv8HwRecvBytes</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwRecvBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
</UL>

<P><STRONG><a name="[102]"></a>AnoPTv8HwSendBytes</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwSendBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8TxLargeBufSend
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>

<P><STRONG><a name="[14a]"></a>AnoPTv8HwSendFrame</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwSendFrame))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[104]"></a>AnoPTv8HwTrigger1ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwTrigger1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AnoPTv8HwTrigger1ms &rArr; AnoPTv8TxLargeBufSend &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8TxLargeBufSend
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[106]"></a>AnoPTv8ParFrameAnl</STRONG> (Thumb, 364 bytes, Stack size 8 bytes, anoptv8par.o(.text.AnoPTv8ParFrameAnl))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParCmdRecvCallback
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParCmdResetParameter
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>

<P><STRONG><a name="[110]"></a>AnoPTv8ParGetCount</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParGetCount))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
</UL>

<P><STRONG><a name="[111]"></a>AnoPTv8ParGetInfo</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParGetInfo))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
</UL>

<P><STRONG><a name="[136]"></a>AnoPTv8ParRegister</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParRegister))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorParamsInit
</UL>

<P><STRONG><a name="[101]"></a>AnoPTv8RecvBytes</STRONG> (Thumb, 416 bytes, Stack size 40 bytes, anoptv8run.o(.text.AnoPTv8RecvBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvBytes
</UL>

<P><STRONG><a name="[10d]"></a>AnoPTv8SendBuf</STRONG> (Thumb, 332 bytes, Stack size 40 bytes, anoptv8framefactory.o(.text.AnoPTv8SendBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorDataSendFrame
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
</UL>

<P><STRONG><a name="[f9]"></a>AnoPTv8SendCheck</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8SendCheck &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[fb]"></a>AnoPTv8SendCmdInfo</STRONG> (Thumb, 210 bytes, Stack size 208 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = AnoPTv8SendCmdInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetInfo
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[fa]"></a>AnoPTv8SendCmdNum</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCmdNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = AnoPTv8SendCmdNum &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[107]"></a>AnoPTv8SendDevInfo</STRONG> (Thumb, 96 bytes, Stack size 64 bytes, anoptv8framefactory.o(.text.AnoPTv8SendDevInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = AnoPTv8SendDevInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[10a]"></a>AnoPTv8SendParInfo</STRONG> (Thumb, 170 bytes, Stack size 232 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[109]"></a>AnoPTv8SendParNum</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = AnoPTv8SendParNum &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[108]"></a>AnoPTv8SendParVal</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParVal))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AnoPTv8SendParVal &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetParamType
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[fc]"></a>AnoPTv8SendStr</STRONG> (Thumb, 122 bytes, Stack size 120 bytes, anoptv8framefactory.o(.text.AnoPTv8SendStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFun_WaveStopAll
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFun_WaveCtrl
</UL>

<P><STRONG><a name="[105]"></a>AnoPTv8TxLargeBufSend</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, anoptv8run.o(.text.AnoPTv8TxLargeBufSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AnoPTv8TxLargeBufSend &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
</UL>

<P><STRONG><a name="[33]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[eb]"></a>CAN1_CAN2_Init</STRONG> (Thumb, 400 bytes, Stack size 64 bytes, sys_can.o(.text.CAN1_CAN2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84 + Unknown Stack Size
<LI>Call Chain = CAN1_CAN2_Init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_enable
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_default_para_init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_base_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_default_para_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_baudrate_set
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_baudrate_default_para_init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[4d]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, at32a423_int.o(.text.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN1_RX0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_flag_clear
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[120]"></a>CAN1_RX0_IRQ_Callback</STRONG> (Thumb, 240 bytes, Stack size 40 bytes, sys_can.o(.text.CAN1_RX0_IRQ_Callback), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_toggle
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
</UL>

<P><STRONG><a name="[72]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, at32a423_int.o(.text.CAN2_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CAN2_RX0_IRQHandler &rArr; CAN2_RX0_IRQ_Callback &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_flag_clear
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_flag_get
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQ_Callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[123]"></a>CAN2_RX0_IRQ_Callback</STRONG> (Thumb, 240 bytes, Stack size 40 bytes, sys_can.o(.text.CAN2_RX0_IRQ_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CAN2_RX0_IRQ_Callback &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_toggle
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQHandler
</UL>

<P><STRONG><a name="[9e]"></a>Check_EmergencyStop</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_EmergencyStop))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[9f]"></a>Check_FaultCondition</STRONG> (Thumb, 106 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_FaultCondition))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[9b]"></a>Check_Fault_Unrecoverable</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_Fault_Unrecoverable))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[a2]"></a>Check_Host_SelfTest_Command</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_Host_SelfTest_Command))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[96]"></a>Check_InitComplete</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_InitComplete))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[95]"></a>Check_PowerStable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_PowerStable))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[a1]"></a>Check_RecoveryCondition</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_RecoveryCondition))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[99]"></a>Check_SelfTestFail</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_SelfTestFail))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[97]"></a>Check_SelfTestPass</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_SelfTestPass))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[98]"></a>Check_SelfTestRetry</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_SelfTestRetry))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[a0]"></a>Check_ShutdownCondition</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_ShutdownCondition))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[9c]"></a>Check_StartCondition</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_StartCondition))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[9d]"></a>Check_StopCondition</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_StopCondition))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[9a]"></a>Check_SystemNormal</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sysfsm.o(.text.Check_SystemNormal))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.rodata.state_transitions)
</UL>
<P><STRONG><a name="[e0]"></a>Check_Temp_Sensor_Valid</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, lbq_design.o(.text.Check_Temp_Sensor_Valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Check_Temp_Sensor_Valid
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>

<P><STRONG><a name="[124]"></a>ClockSelfTest</STRONG> (Thumb, 170 bytes, Stack size 48 bytes, start_self_test.o(.text.ClockSelfTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ClockSelfTest &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_failure_detection_enable
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_init_data_set
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_block_calculate
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_data_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelfTest_Manager
</UL>

<P><STRONG><a name="[44]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, at32a423_int.o(.text.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; ADC_Process_500us &rArr; Check_Temp_Sensor_Valid
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_get
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Complete_Callback
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>EXINT9_5_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, at32a423_int.o(.text.EXINT9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXINT9_5_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exint_flag_get
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exint_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>Filter_calc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, mathbasic.o(.text.Filter_calc))
<BR>[Address Reference Count : 1]<UL><LI> sys_isr_controller.o(.data.Filter_speed)
</UL>
<P><STRONG><a name="[d7]"></a>GetElectricalAngle</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, sysctl_rotorget.o(.text.GetElectricalAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = GetElectricalAngle &rArr; Ad2s_GetAngle &rArr; Ad2s_DecoderReadGeneralMode &rArr;  Ad2s_DecoderReadGeneralMode (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_GetAngle
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[d8]"></a>GetRotorAngle</STRONG> (Thumb, 296 bytes, Stack size 32 bytes, sysctl_rotorget.o(.text.GetRotorAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = GetRotorAngle &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmplt
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>GetRotorSpeed</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, sysctl_rotorget.o(.text.GetRotorSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = GetRotorSpeed &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>Get_Bus_Voltage</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sensor_drive.o(.text.Get_Bus_Voltage))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[31]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[dd]"></a>MA_Filter_Init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, lbq_design.o(.text.MA_Filter_Init))
<BR><BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Drive_Init
</UL>

<P><STRONG><a name="[df]"></a>MA_Filter_Update</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, lbq_design.o(.text.MA_Filter_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MA_Filter_Update
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Process_500us
</UL>

<P><STRONG><a name="[32]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[ff]"></a>MotorCmdInit</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, motorcmd.o(.text.MotorCmdInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MotorCmdInit
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
</UL>

<P><STRONG><a name="[135]"></a>MotorDataSendFrame</STRONG> (Thumb, 218 bytes, Stack size 0 bytes, motordata.o(.text.MotorDataSendFrame))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MotorDataSendFrame &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_wave
</UL>

<P><STRONG><a name="[fe]"></a>MotorParamsInit</STRONG> (Thumb, 544 bytes, Stack size 8 bytes, motorparams.o(.text.MotorParamsInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MotorParamsInit
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
</UL>

<P><STRONG><a name="[30]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>OTGFS1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_int.o(.text.OTGFS1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = OTGFS1_IRQHandler &rArr; wk_otgfs1_irq_handler &rArr; usbd_irq_handler &rArr; usbd_outept_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_otgfs1_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[138]"></a>OpenLoopFreqCtrl_Update</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OpenLoopFreqCtrl_Update &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_calc
</UL>

<P><STRONG><a name="[2e]"></a>Para_derive</STRONG> (Thumb, 552 bytes, Stack size 96 bytes, motor_vectorcontrol.o(.text.Para_derive))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Para_derive &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data.para)
</UL>
<P><STRONG><a name="[37]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[14b]"></a>Print_System_Status_Distributed</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motorcmd.o(.text.Print_System_Status_Distributed))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[13b]"></a>RS422_CU_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, rs422_cu.o(.text.RS422_CU_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = RS422_CU_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[35]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[152]"></a>SVPWM</STRONG> (Thumb, 868 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.SVPWM))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_calc
</UL>

<P><STRONG><a name="[1]"></a>SelfTest_Init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, start_self_test.o(.text.SelfTest_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = SelfTest_Init &rArr; Sic_SelfTest_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sic_SelfTest_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> start_self_test.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[13d]"></a>SelfTest_Manager</STRONG> (Thumb, 1652 bytes, Stack size 152 bytes, start_self_test.o(.text.SelfTest_Manager))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = SelfTest_Manager &rArr; ClockSelfTest &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Get_Enable_Flag
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sic_SelfTest_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;State_SelfTest_Handler
</UL>

<P><STRONG><a name="[2]"></a>SelfTest_Reset</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, start_self_test.o(.text.SelfTest_Reset))
<BR>[Address Reference Count : 1]<UL><LI> start_self_test.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[13c]"></a>Sic_SelfTest_Init</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, sic_selftest.o(.text.Sic_SelfTest_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = Sic_SelfTest_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelfTest_Manager
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelfTest_Init
</UL>

<P><STRONG><a name="[38]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, system_at32a423.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_auto_step_mode_enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(.text)
</UL>
<P><STRONG><a name="[163]"></a>System_StatusUpdate</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, sysfsm.o(.text.System_StatusUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = System_StatusUpdate
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[65]"></a>TMR13_GLOBAL_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, at32a423_int.o(.text.TMR13_GLOBAL_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TMR13_GLOBAL_IRQHandler &rArr; TimerEvent_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_software_trigger_enable
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Get_Enable_Flag
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_Handler
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TMR1_BRK_TMR9_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, at32a423_int.o(.text.TMR1_BRK_TMR9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR1_BRK_TMR9_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TMR1_CH_IRQHandler</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, at32a423_int.o(.text.TMR1_CH_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR1_CH_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_software_trigger_enable
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TMR1_OVF_TMR10_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, at32a423_int.o(.text.TMR1_OVF_TMR10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR1_OVF_TMR10_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>TMR3_GLOBAL_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, at32a423_int.o(.text.TMR3_GLOBAL_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR3_GLOBAL_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[14d]"></a>TimerEvent_AddTask</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, sys_timerevent.o(.text.TimerEvent_AddTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TimerEvent_AddTask
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Init
</UL>

<P><STRONG><a name="[143]"></a>TimerEvent_Handler</STRONG> (Thumb, 194 bytes, Stack size 32 bytes, sys_timerevent.o(.text.TimerEvent_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TimerEvent_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[146]"></a>TimerEvent_Init</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, sys_timerevent.o(.text.TimerEvent_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = TimerEvent_Init &rArr; calloc &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calloc
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Init
</UL>

<P><STRONG><a name="[147]"></a>Timer_Tasks_Execute</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, sysfsm.o(.text.Timer_Tasks_Execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = Timer_Tasks_Execute &rArr; update_wave &rArr; MotorDataSendFrame &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_wave
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF5ADCData
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Print_System_Status_Distributed
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendFrame
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14c]"></a>Timer_Tasks_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, sysfsm.o(.text.Timer_Tasks_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = Timer_Tasks_Init &rArr; TimerEvent_Init &rArr; calloc &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_AddTask
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[5e]"></a>USART1_IRQHandler</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, rs422_cu.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[149]"></a>UpdateF5ADCData</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motordata.o(.text.UpdateF5ADCData))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[34]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a423_int.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a423.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>Vector_ctrl_ResetTs</STRONG> (Thumb, 162 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[10]"></a>Vector_ctrl_calc</STRONG> (Thumb, 988 bytes, Stack size 32 bytes, motor_vectorcontrol.o(.text.Vector_ctrl_calc))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Vector_ctrl_calc &rArr; OpenLoopFreqCtrl_Update &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVPWM
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[e]"></a>Vector_ctrl_init</STRONG> (Thumb, 864 bytes, Stack size 40 bytes, motor_vectorcontrol.o(.text.Vector_ctrl_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Vector_ctrl_init &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[f]"></a>Vector_ctrl_reset</STRONG> (Thumb, 480 bytes, Stack size 24 bytes, motor_vectorcontrol.o(.text.Vector_ctrl_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Vector_ctrl_reset &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[11]"></a>VoltageBalance_calc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.VoltageBalance_calc))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[1a6]"></a>acc_calibration_mode_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, at32a423_acc.o(.text.acc_calibration_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_acc_init
</UL>

<P><STRONG><a name="[1a3]"></a>acc_write_c1</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_acc.o(.text.acc_write_c1))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_acc_init
</UL>

<P><STRONG><a name="[1a4]"></a>acc_write_c2</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_acc.o(.text.acc_write_c2))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_acc_init
</UL>

<P><STRONG><a name="[1a5]"></a>acc_write_c3</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_acc.o(.text.acc_write_c3))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_acc_init
</UL>

<P><STRONG><a name="[1ab]"></a>adc_base_config</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_base_config))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1aa]"></a>adc_base_default_para_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_base_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b6]"></a>adc_calibration_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_calibration_init))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b7]"></a>adc_calibration_init_status_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_calibration_init_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b8]"></a>adc_calibration_start</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_calibration_start))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b9]"></a>adc_calibration_status_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_calibration_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1a9]"></a>adc_common_config</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_common_config))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1a8]"></a>adc_common_default_para_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_common_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1af]"></a>adc_dma_mode_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_dma_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b0]"></a>adc_dma_request_repeat_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_dma_request_repeat_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b5]"></a>adc_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[d6]"></a>adc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[12c]"></a>adc_flag_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[1a2]"></a>adc_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[d5]"></a>adc_interrupt_flag_get</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[1ad]"></a>adc_ordinary_channel_set</STRONG> (Thumb, 216 bytes, Stack size 8 bytes, at32a423_adc.o(.text.adc_ordinary_channel_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adc_ordinary_channel_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1ae]"></a>adc_ordinary_conversion_trigger_set</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_ordinary_conversion_trigger_set))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[142]"></a>adc_ordinary_software_trigger_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_ordinary_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[1b1]"></a>adc_preempt_channel_length_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_preempt_channel_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b2]"></a>adc_preempt_channel_set</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, at32a423_adc.o(.text.adc_preempt_channel_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adc_preempt_channel_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b4]"></a>adc_preempt_conversion_trigger_set</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_preempt_conversion_trigger_set))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1b3]"></a>adc_preempt_offset_value_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_preempt_offset_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[145]"></a>adc_preempt_software_trigger_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_preempt_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_CH_IRQHandler
</UL>

<P><STRONG><a name="[1ac]"></a>adc_resolution_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_adc.o(.text.adc_resolution_set))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[151]"></a>arm_cos_f32</STRONG> (Thumb, 148 bytes, Stack size 0 bytes, arm_cos_f32.o(.text.arm_cos_f32))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_calc
</UL>

<P><STRONG><a name="[150]"></a>arm_sin_f32</STRONG> (Thumb, 140 bytes, Stack size 0 bytes, arm_sin_f32.o(.text.arm_sin_f32))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_calc
</UL>

<P><STRONG><a name="[117]"></a>can_base_init</STRONG> (Thumb, 228 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_base_init))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[118]"></a>can_baudrate_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_baudrate_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[119]"></a>can_baudrate_set</STRONG> (Thumb, 190 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_baudrate_set))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[116]"></a>can_default_para_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[11a]"></a>can_filter_default_para_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_filter_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[11b]"></a>can_filter_init</STRONG> (Thumb, 274 bytes, Stack size 8 bytes, at32a423_can.o(.text.can_filter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_filter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[11f]"></a>can_flag_clear</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQHandler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[153]"></a>can_flag_get</STRONG> (Thumb, 208 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_flag_get
</UL>

<P><STRONG><a name="[11d]"></a>can_interrupt_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[11e]"></a>can_interrupt_flag_get</STRONG> (Thumb, 160 bytes, Stack size 0 bytes, at32a423_can.o(.text.can_interrupt_flag_get))
<BR><BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQHandler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[121]"></a>can_message_receive</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, at32a423_can.o(.text.can_message_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQ_Callback
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQ_Callback
</UL>

<P><STRONG><a name="[3]"></a>clarke_calc</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.clarke_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.3)
</UL>
<P><STRONG><a name="[129]"></a>crc_block_calculate</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, at32a423_crc.o(.text.crc_block_calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crc_block_calculate
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
</UL>

<P><STRONG><a name="[127]"></a>crc_data_reset</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_crc.o(.text.crc_data_reset))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
</UL>

<P><STRONG><a name="[128]"></a>crc_init_data_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_crc.o(.text.crc_init_data_set))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
</UL>

<P><STRONG><a name="[1a7]"></a>crm_adc_clock_select</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_adc_clock_select))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[1cc]"></a>crm_ahb_div_set</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_ahb_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1ce]"></a>crm_apb1_div_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_apb1_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1cd]"></a>crm_apb2_div_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_apb2_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13f]"></a>crm_auto_step_mode_enable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_auto_step_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[126]"></a>crm_clock_failure_detection_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_clock_failure_detection_enable))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
</UL>

<P><STRONG><a name="[1c8]"></a>crm_clock_source_enable</STRONG> (Thumb, 130 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_clock_source_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[125]"></a>crm_clocks_freq_get</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, at32a423_crm.o(.text.crm_clocks_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClockSelfTest
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
</UL>

<P><STRONG><a name="[1c9]"></a>crm_flag_get</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1ca]"></a>crm_hext_stable_wait</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_hext_stable_wait))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1c3]"></a>crm_periph_clock_enable</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_periph_clock_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1cb]"></a>crm_pll_config</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, at32a423_crm.o(.text.crm_pll_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crm_pll_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1c7]"></a>crm_reset</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_reset))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[1cf]"></a>crm_sysclk_switch</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_sysclk_switch))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[165]"></a>crm_sysclk_switch_status_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_sysclk_switch_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[167]"></a>crm_usart_clock_get</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_usart_clock_get))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
</UL>

<P><STRONG><a name="[1e1]"></a>crm_usart_clock_select</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, at32a423_crm.o(.text.crm_usart_clock_select))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[1e7]"></a>crm_usb_clock_source_select</STRONG> (Thumb, 178 bytes, Stack size 4 bytes, at32a423_crm.o(.text.crm_usb_clock_source_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = crm_usb_clock_source_select
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_otgfs1_init
</UL>

<P><STRONG><a name="[15d]"></a>delay_init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, delay.o(.text.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clock_source_config
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>dma_channel_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_channel_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[1bb]"></a>dma_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[12d]"></a>dma_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[1bc]"></a>dma_init</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_init))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[e8]"></a>dma_interrupt_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[12a]"></a>dma_interrupt_flag_get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[1ba]"></a>dma_reset</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dma_reset))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[1bd]"></a>dmamux_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dmamux_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[1be]"></a>dmamux_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_dma.o(.text.dmamux_init))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[1c0]"></a>exint_default_para_init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_exint.o(.text.exint_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
</UL>

<P><STRONG><a name="[12f]"></a>exint_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_exint.o(.text.exint_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXINT9_5_IRQHandler
</UL>

<P><STRONG><a name="[12e]"></a>exint_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_exint.o(.text.exint_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXINT9_5_IRQHandler
</UL>

<P><STRONG><a name="[1c1]"></a>exint_init</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, at32a423_exint.o(.text.exint_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = exint_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
</UL>

<P><STRONG><a name="[b]"></a>fnAISample</STRONG> (Thumb, 132 bytes, Stack size 0 bytes, sysctl_analogprocess.o(.text.fnAISample))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.AnalogInput)
</UL>
<P><STRONG><a name="[160]"></a>fnParaUpdateSysSamScaParameter</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysMooreCal
</UL>

<P><STRONG><a name="[13]"></a>fnSysBaseValueCal</STRONG> (Thumb, 328 bytes, Stack size 56 bytes, sysctl_analogprocess.o(.text.fnSysBaseValueCal))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fnSysBaseValueCal &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SysBaseValue)
</UL>
<P><STRONG><a name="[14]"></a>fnSysMooreCal</STRONG> (Thumb, 570 bytes, Stack size 32 bytes, sysctl_sysmoore.o(.text.fnSysMooreCal))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = fnSysMooreCal
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnParaUpdateSysSamScaParameter
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SysMoore)
</UL>
<P><STRONG><a name="[15]"></a>fnSysOffsetInit</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, sysctl_analogprocess.o(.text.fnSysOffsetInit))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SysSampOffset)
</UL>
<P><STRONG><a name="[16]"></a>fnSysOffsetParameterCal</STRONG> (Thumb, 208 bytes, Stack size 0 bytes, sysctl_analogprocess.o(.text.fnSysOffsetParameterCal))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SysSampOffset)
</UL>
<P><STRONG><a name="[cf]"></a>gpio_bits_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_gpio.o(.text.gpio_bits_reset))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_GetAngle
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysMooreCal
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[ce]"></a>gpio_bits_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_gpio.o(.text.gpio_bits_set))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_GetAngle
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysMooreCal
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[122]"></a>gpio_bits_toggle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_gpio.o(.text.gpio_bits_toggle))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQ_Callback
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQ_Callback
</UL>

<P><STRONG><a name="[113]"></a>gpio_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_gpio.o(.text.gpio_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[115]"></a>gpio_init</STRONG> (Thumb, 122 bytes, Stack size 20 bytes, at32a423_gpio.o(.text.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[114]"></a>gpio_pin_mux_config</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, at32a423_gpio.o(.text.gpio_pin_mux_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gpio_pin_mux_config
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[4]"></a>iclarke_calc</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.iclarke_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.3)
</UL>
<P><STRONG><a name="[6]"></a>ipark_calc</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.ipark_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.4)
</UL>
<P><STRONG><a name="[0]"></a>lpfl_calc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.lpfl_calc))
<BR>[Address Reference Count : 4]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.1)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.2)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.3)
</UL>
<P><STRONG><a name="[b1]"></a>main</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysMooreCal
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_StatusUpdate
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[12]"></a>marsest_calc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.marsest_calc))
<BR>[Address Reference Count : 1]<UL><LI> sysctl_globalvar.o(.data.SynMotorVc)
</UL>
<P><STRONG><a name="[11c]"></a>nvic_irq_enable</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, at32a423_misc.o(.text.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_CAN2_Init
</UL>

<P><STRONG><a name="[1c2]"></a>nvic_priority_group_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, at32a423_misc.o(.text.nvic_priority_group_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
</UL>

<P><STRONG><a name="[5]"></a>park_calc</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.park_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.4)
</UL>
<P><STRONG><a name="[7]"></a>pi_current_const_calc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.pi_current_const_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.4)
</UL>
<P><STRONG><a name="[8]"></a>pi_flux_calc</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.pi_flux_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.5)
</UL>
<P><STRONG><a name="[a]"></a>pi_fun_calc</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.pi_fun_calc))
<BR>[Address Reference Count : 5]<UL><LI> motor_vectorcontrol.o(.data.pi_isxz)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.6)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.7)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.8)
<LI> motor_vectorcontrol.o(.data..L_MergedGlobals.9)
</UL>
<P><STRONG><a name="[9]"></a>pi_speed_calc</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, motor_vectorcontrol.o(.text.pi_speed_calc))
<BR>[Address Reference Count : 1]<UL><LI> motor_vectorcontrol.o(.data..L_MergedGlobals.5)
</UL>
<P><STRONG><a name="[1bf]"></a>scfg_exint_line_config</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, at32a423_scfg.o(.text.scfg_exint_line_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = scfg_exint_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_exint_config
</UL>

<P><STRONG><a name="[1c4]"></a>spi_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
</UL>

<P><STRONG><a name="[1c6]"></a>spi_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
</UL>

<P><STRONG><a name="[d4]"></a>spi_i2s_data_receive</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[d2]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[d3]"></a>spi_i2s_flag_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ad2s_DecoderReadGeneralMode
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[1c5]"></a>spi_init</STRONG> (Thumb, 286 bytes, Stack size 0 bytes, at32a423_spi.o(.text.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi2_init
</UL>

<P><STRONG><a name="[164]"></a>system_core_clock_update</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, system_at32a423.o(.text.system_core_clock_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = system_core_clock_update &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[15e]"></a>systick_clock_source_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a423_misc.o(.text.systick_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[1d4]"></a>tmr_base_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_base_init))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1dd]"></a>tmr_brk_filter_value_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_brk_filter_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1dc]"></a>tmr_brkdt_config</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_brkdt_config))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d9]"></a>tmr_channel_value_set</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_channel_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d1]"></a>tmr_clock_source_div_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_clock_source_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d0]"></a>tmr_cnt_dir_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_cnt_dir_set))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d5]"></a>tmr_counter_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_counter_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1e0]"></a>tmr_encoder_mode_config</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_encoder_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
</UL>

<P><STRONG><a name="[141]"></a>tmr_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_CH_IRQHandler
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR3_GLOBAL_IRQHandler
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_OVF_TMR10_IRQHandler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_BRK_TMR9_IRQHandler
</UL>

<P><STRONG><a name="[1df]"></a>tmr_input_channel_init</STRONG> (Thumb, 466 bytes, Stack size 8 bytes, at32a423_tmr.o(.text.tmr_input_channel_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = tmr_input_channel_init
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
</UL>

<P><STRONG><a name="[1a1]"></a>tmr_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[140]"></a>tmr_interrupt_flag_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_CH_IRQHandler
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR3_GLOBAL_IRQHandler
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_OVF_TMR10_IRQHandler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_BRK_TMR9_IRQHandler
</UL>

<P><STRONG><a name="[1da]"></a>tmr_output_channel_buffer_enable</STRONG> (Thumb, 94 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_output_channel_buffer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d8]"></a>tmr_output_channel_config</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, at32a423_tmr.o(.text.tmr_output_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tmr_output_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1db]"></a>tmr_output_channel_immediately_set</STRONG> (Thumb, 94 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_output_channel_immediately_set))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[db]"></a>tmr_output_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_output_enable))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysMooreCal
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1de]"></a>tmr_overflow_request_source_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_overflow_request_source_set))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
</UL>

<P><STRONG><a name="[1d3]"></a>tmr_period_buffer_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_period_buffer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d7]"></a>tmr_primary_mode_select</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_primary_mode_select))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d2]"></a>tmr_repetition_counter_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_repetition_counter_set))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr13_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[1d6]"></a>tmr_sub_sync_mode_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_tmr.o(.text.tmr_sub_sync_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[148]"></a>update_wave</STRONG> (Thumb, 148 bytes, Stack size 20 bytes, motor_vectorcontrol.o(.text.update_wave))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = update_wave &rArr; MotorDataSendFrame &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorDataSendFrame
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[14f]"></a>usart_data_receive</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[1e6]"></a>usart_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[1e5]"></a>usart_hardware_flow_control_set</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_hardware_flow_control_set))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[166]"></a>usart_init</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, at32a423_usart.o(.text.usart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = usart_init &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usart_clock_get
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[14e]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 280 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[1e4]"></a>usart_parity_selection_config</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_parity_selection_config))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[1e3]"></a>usart_receiver_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_receiver_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[1e2]"></a>usart_transmitter_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_usart.o(.text.usart_transmitter_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usart1_init
</UL>

<P><STRONG><a name="[178]"></a>usb_connect</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_connect))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[16f]"></a>usb_disconnect</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_disconnect))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[197]"></a>usb_ept0_setup</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept0_setup))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[19d]"></a>usb_ept0_start</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept0_start))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
</UL>

<P><STRONG><a name="[16c]"></a>usb_ept_clear_stall</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_clear_stall))
<BR><BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_clear_stall
</UL>

<P><STRONG><a name="[187]"></a>usb_ept_close</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, at32a423_usb.o(.text.usb_ept_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_ept_close
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_close
</UL>

<P><STRONG><a name="[170]"></a>usb_ept_default_init</STRONG> (Thumb, 282 bytes, Stack size 20 bytes, usbd_core.o(.text.usb_ept_default_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_ept_default_init
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[18f]"></a>usb_ept_in_clear</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_in_clear))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
</UL>

<P><STRONG><a name="[18e]"></a>usb_ept_in_interrupt</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_in_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
</UL>

<P><STRONG><a name="[188]"></a>usb_ept_open</STRONG> (Thumb, 292 bytes, Stack size 8 bytes, at32a423_usb.o(.text.usb_ept_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_ept_open
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
</UL>

<P><STRONG><a name="[19b]"></a>usb_ept_out_clear</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_out_clear))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
</UL>

<P><STRONG><a name="[19a]"></a>usb_ept_out_interrupt</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_out_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
</UL>

<P><STRONG><a name="[180]"></a>usb_ept_stall</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_ept_stall))
<BR><BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_stall
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[176]"></a>usb_flush_rx_fifo</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_flush_rx_fifo))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[175]"></a>usb_flush_tx_fifo</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_flush_tx_fifo))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_flush_tx_fifo
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>

<P><STRONG><a name="[18d]"></a>usb_get_all_in_interrupt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_get_all_in_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
</UL>

<P><STRONG><a name="[199]"></a>usb_get_all_out_interrupt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_get_all_out_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
</UL>

<P><STRONG><a name="[194]"></a>usb_global_clear_interrupt</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_clear_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[193]"></a>usb_global_get_all_interrupt</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_get_all_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[172]"></a>usb_global_init</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_init))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[19e]"></a>usb_global_interrupt_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_rxflvl_handler
</UL>

<P><STRONG><a name="[191]"></a>usb_global_select_core</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_select_core))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[173]"></a>usb_global_set_mode</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_global_set_mode))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[171]"></a>usb_interrupt_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[179]"></a>usb_interrupt_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[174]"></a>usb_open_phy_clk</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_open_phy_clk))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[19f]"></a>usb_read_packet</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, at32a423_usb.o(.text.usb_read_packet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_read_packet
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_rxflvl_handler
</UL>

<P><STRONG><a name="[103]"></a>usb_send_data</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usb_app.o(.text.usb_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[19c]"></a>usb_set_address</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_set_address))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_device_addr
</UL>

<P><STRONG><a name="[18a]"></a>usb_set_rx_fifo</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_set_rx_fifo))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_fifo_alloc
</UL>

<P><STRONG><a name="[18b]"></a>usb_set_tx_fifo</STRONG> (Thumb, 150 bytes, Stack size 20 bytes, at32a423_usb.o(.text.usb_set_tx_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_set_tx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_fifo_alloc
</UL>

<P><STRONG><a name="[186]"></a>usb_stop_phy_clk</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_stop_phy_clk))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enter_suspend
</UL>

<P><STRONG><a name="[185]"></a>usb_suspend_status_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a423_usb.o(.text.usb_suspend_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enter_suspend
</UL>

<P><STRONG><a name="[169]"></a>usb_vcp_get_rxdata</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, cdc_class.o(.text.usb_vcp_get_rxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usb_vcp_get_rxdata &rArr; usbd_ept_recv
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
</UL>

<P><STRONG><a name="[168]"></a>usb_vcp_send_data</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, cdc_class.o(.text.usb_vcp_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>

<P><STRONG><a name="[189]"></a>usb_write_packet</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, at32a423_usb.o(.text.usb_write_packet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_write_packet
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>

<P><STRONG><a name="[16b]"></a>usbd_clear_stall</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_clear_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_clear_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_clear_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
</UL>

<P><STRONG><a name="[16d]"></a>usbd_core_in_handler</STRONG> (Thumb, 284 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_core_in_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_core_in_handler &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
</UL>

<P><STRONG><a name="[16e]"></a>usbd_core_init</STRONG> (Thumb, 970 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_core_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_core_init &rArr; usbd_fifo_alloc &rArr; usb_set_tx_fifo
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_interrupt_enable
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flush_rx_fifo
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_set_mode
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_init
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_interrupt_disable
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_fifo_alloc
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_open_phy_clk
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_default_init
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_disconnect
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_connect
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flush_tx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[17a]"></a>usbd_core_out_handler</STRONG> (Thumb, 196 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_core_out_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_core_out_handler &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
</UL>

<P><STRONG><a name="[17b]"></a>usbd_core_setup_handler</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_core_setup_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_stall
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_parse
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
</UL>

<P><STRONG><a name="[159]"></a>usbd_ctrl_recv</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_ctrl_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_ctrl_recv
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[15b]"></a>usbd_ctrl_send</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ctrl_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[181]"></a>usbd_ctrl_send_status</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ctrl_send_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_ctrl_send_status &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
</UL>

<P><STRONG><a name="[15a]"></a>usbd_ctrl_unsupport</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_ctrl_unsupport))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_ctrl_unsupport
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[17d]"></a>usbd_device_request</STRONG> (Thumb, 630 bytes, Stack size 16 bytes, usbd_sdr.o(.text.usbd_device_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usbd_device_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_device_addr
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[17f]"></a>usbd_endpoint_request</STRONG> (Thumb, 270 bytes, Stack size 16 bytes, usbd_sdr.o(.text.usbd_endpoint_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_stall
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_clear_stall
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[184]"></a>usbd_enter_suspend</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_enter_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_enter_suspend
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_stop_phy_clk
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_suspend_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[154]"></a>usbd_ept_close</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ept_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_ept_close &rArr; usb_ept_close
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_close
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_clear_handler
</UL>

<P><STRONG><a name="[157]"></a>usbd_ept_open</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, usbd_core.o(.text.usbd_ept_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usbd_ept_open &rArr; usb_ept_open
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_open
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_init_handler
</UL>

<P><STRONG><a name="[158]"></a>usbd_ept_recv</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_ept_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_ept_recv
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_in_handler
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_get_rxdata
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_init_handler
</UL>

<P><STRONG><a name="[16a]"></a>usbd_ept_send</STRONG> (Thumb, 398 bytes, Stack size 40 bytes, usbd_core.o(.text.usbd_ept_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_write_packet
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flush_tx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_out_handler
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_in_handler
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_send_data
</UL>

<P><STRONG><a name="[177]"></a>usbd_fifo_alloc</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_fifo_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usbd_fifo_alloc &rArr; usb_set_tx_fifo
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_tx_fifo
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_rx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[156]"></a>usbd_flush_tx_fifo</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_flush_tx_fifo))
<BR><BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flush_tx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_in_handler
</UL>

<P><STRONG><a name="[155]"></a>usbd_get_recv_len</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_get_recv_len))
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_out_handler
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_ept0_rx_handler
</UL>

<P><STRONG><a name="[18c]"></a>usbd_inept_handler</STRONG> (Thumb, 354 bytes, Stack size 40 bytes, usbd_int.o(.text.usbd_inept_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = usbd_inept_handler &rArr; usbd_core_in_handler &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_in_clear
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_in_interrupt
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_get_all_in_interrupt
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_write_packet
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_in_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[190]"></a>usbd_init</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, usb_core.o(.text.usbd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = usbd_init &rArr; usbd_core_init &rArr; usbd_fifo_alloc &rArr; usb_set_tx_fifo
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_select_core
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_init
</UL>

<P><STRONG><a name="[17e]"></a>usbd_interface_request</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, usbd_sdr.o(.text.usbd_interface_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_interface_request &rArr; usbd_ctrl_send_status &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[192]"></a>usbd_irq_handler</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, usbd_int.o(.text.usbd_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = usbd_irq_handler &rArr; usbd_outept_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_rxflvl_handler
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept0_setup
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_outept_handler
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_inept_handler
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_clear_interrupt
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_get_all_interrupt
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enter_suspend
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_open_phy_clk
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_otgfs1_irq_handler
</UL>

<P><STRONG><a name="[195]"></a>usbd_outept_handler</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, usbd_int.o(.text.usbd_outept_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = usbd_outept_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_out_clear
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_out_interrupt
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_get_all_out_interrupt
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_out_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[196]"></a>usbd_reset_handler</STRONG> (Thumb, 610 bytes, Stack size 16 bytes, usbd_int.o(.text.usbd_reset_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_reset_handler &rArr; usbd_fifo_alloc &rArr; usb_set_tx_fifo
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept0_start
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_fifo_alloc
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_address
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flush_tx_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[198]"></a>usbd_rxflvl_handler</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, usbd_int.o(.text.usbd_rxflvl_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_rxflvl_handler &rArr; usb_read_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_packet
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_global_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[182]"></a>usbd_set_device_addr</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_set_device_addr))
<BR><BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_address
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
</UL>

<P><STRONG><a name="[183]"></a>usbd_set_stall</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_set_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_set_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
</UL>

<P><STRONG><a name="[17c]"></a>usbd_setup_request_parse</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usbd_sdr.o(.text.usbd_setup_request_parse))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[161]"></a>user_init</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, overall_init.o(.text.user_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = user_init &rArr; Timer_Tasks_Init &rArr; TimerEvent_Init &rArr; calloc &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_enable
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS422_CU_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Drive_Init
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[144]"></a>wdt_counter_reload</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_wdt.o(.text.wdt_counter_reload))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR13_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[1e9]"></a>wdt_divider_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a423_wdt.o(.text.wdt_divider_set))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[1eb]"></a>wdt_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a423_wdt.o(.text.wdt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[1e8]"></a>wdt_register_write_enable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a423_wdt.o(.text.wdt_register_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[1ea]"></a>wdt_reload_value_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a423_wdt.o(.text.wdt_reload_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[ee]"></a>wk_acc_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_acc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_acc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;acc_write_c3
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;acc_write_c2
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;acc_write_c1
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;acc_calibration_mode_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f5]"></a>wk_adc1_init</STRONG> (Thumb, 760 bytes, Stack size 48 bytes, at32a423_wk_config.o(.text.wk_adc1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = wk_adc1_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_adc_clock_select
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_get
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_offset_value_set
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_conversion_trigger_set
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_conversion_trigger_set
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_channel_set
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_channel_length_set
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_channel_set
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_status_get
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_start
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init_status_get
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_request_repeat_enable
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_resolution_set
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_common_config
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_common_default_para_init
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_config
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_default_para_init
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[e5]"></a>wk_dma1_channel1_init</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, at32a423_wk_config.o(.text.wk_dma1_channel1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wk_dma1_channel1_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmamux_init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmamux_enable
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_init
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_default_para_init
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[e6]"></a>wk_dma_channel_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a423_wk_config.o(.text.wk_dma_channel_config))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[ef]"></a>wk_exint_config</STRONG> (Thumb, 74 bytes, Stack size 40 bytes, at32a423_wk_config.o(.text.wk_exint_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = wk_exint_config &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scfg_exint_line_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exint_init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exint_default_para_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f0]"></a>wk_gpio_config</STRONG> (Thumb, 348 bytes, Stack size 48 bytes, at32a423_wk_config.o(.text.wk_gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = wk_gpio_config &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[e4]"></a>wk_nvic_config</STRONG> (Thumb, 218 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_nvic_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = wk_nvic_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_config
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[137]"></a>wk_otgfs1_irq_handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, usb_app.o(.text.wk_otgfs1_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = wk_otgfs1_irq_handler &rArr; usbd_irq_handler &rArr; usbd_outept_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTGFS1_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>wk_periph_clock_config</STRONG> (Thumb, 208 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_periph_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_periph_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[ec]"></a>wk_spi2_init</STRONG> (Thumb, 166 bytes, Stack size 40 bytes, at32a423_wk_config.o(.text.wk_spi2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = wk_spi2_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[ed]"></a>wk_spi3_init</STRONG> (Thumb, 164 bytes, Stack size 40 bytes, at32a423_wk_config.o(.text.wk_spi3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = wk_spi3_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[e2]"></a>wk_system_clock_config</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = wk_system_clock_config &rArr; system_core_clock_update &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_pll_config
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb2_div_set
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb1_div_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_ahb_div_set
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_source_enable
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_hext_stable_wait
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_reset
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_auto_step_mode_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f3]"></a>wk_tmr13_init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_tmr13_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_tmr13_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_repetition_counter_set
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f1]"></a>wk_tmr1_init</STRONG> (Thumb, 564 bytes, Stack size 56 bytes, at32a423_wk_config.o(.text.wk_tmr1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = wk_tmr1_init &rArr; tmr_output_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_brk_filter_value_set
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_brkdt_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_enable
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_sub_sync_mode_set
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_primary_mode_select
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_immediately_set
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_buffer_enable
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_channel_value_set
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_config
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_repetition_counter_set
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f2]"></a>wk_tmr3_init</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, at32a423_wk_config.o(.text.wk_tmr3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = wk_tmr3_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_encoder_mode_config
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_sub_sync_mode_set
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_primary_mode_select
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_input_channel_init
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_overflow_request_source_set
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[e9]"></a>wk_usart1_init</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, at32a423_wk_config.o(.text.wk_usart1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = wk_usart1_init &rArr; usart_init &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_mux_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usart_clock_select
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_control_set
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receiver_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmitter_enable
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_selection_config
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[1a0]"></a>wk_usb_app_init</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, usb_app.o(.text.wk_usb_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = wk_usb_app_init &rArr; usbd_init &rArr; usbd_core_init &rArr; usbd_fifo_alloc &rArr; usb_set_tx_fifo
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init
</UL>

<P><STRONG><a name="[162]"></a>wk_usb_app_task</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usb_app.o(.text.wk_usb_app_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvBytes
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_get_rxdata
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ea]"></a>wk_usb_otgfs1_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a423_wk_config.o(.text.wk_usb_otgfs1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = wk_usb_otgfs1_init &rArr; crm_usb_clock_source_select
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usb_clock_source_select
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[f4]"></a>wk_wdt_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, at32a423_wk_config.o(.text.wk_wdt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_wdt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_register_write_enable
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_divider_set
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_reload_value_set
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AT32A423_system_init
</UL>

<P><STRONG><a name="[133]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysBaseValueCal
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorSpeed
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorAngle
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_reset
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_init
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Para_derive
</UL>

<P><STRONG><a name="[1ec]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[132]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorSpeed
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorAngle
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
</UL>

<P><STRONG><a name="[1ef]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[1f4]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[15f]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysBaseValueCal
</UL>

<P><STRONG><a name="[1f2]"></a>_ddiv</STRONG> (Thumb, 560 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[240]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)

<P><STRONG><a name="[1f3]"></a>_dcmpeq</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dneq
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_deq
</UL>

<P><STRONG><a name="[241]"></a>__aeabi_cdcmpge</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dgeqf.o(x$fpl$dgeqf), UNUSED)

<P><STRONG><a name="[1f5]"></a>_dcmpge</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dgeqf.o(x$fpl$dgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dgeq
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dgr
</UL>

<P><STRONG><a name="[242]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)

<P><STRONG><a name="[1f6]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dls
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dleq
</UL>

<P><STRONG><a name="[243]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)

<P><STRONG><a name="[139]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysBaseValueCal
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_reset
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_init
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Para_derive
</UL>

<P><STRONG><a name="[1f7]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[1ee]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[1f1]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[244]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)

<P><STRONG><a name="[1f8]"></a>_dsub</STRONG> (Thumb, 472 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[130]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fnSysBaseValueCal
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorSpeed
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorAngle
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_reset
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vector_ctrl_init
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Para_derive
</UL>

<P><STRONG><a name="[1fa]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[245]"></a>__aeabi_dcmpeq</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[1fc]"></a>_deq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[1fd]"></a>_dneq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[246]"></a>__aeabi_dcmpgt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[1fe]"></a>_dgr</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
</UL>

<P><STRONG><a name="[13a]"></a>__aeabi_dcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OpenLoopFreqCtrl_Update
</UL>

<P><STRONG><a name="[1ff]"></a>_dgeq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
</UL>

<P><STRONG><a name="[247]"></a>__aeabi_dcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[200]"></a>_dleq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[131]"></a>__aeabi_dcmplt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmplt
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetRotorAngle
</UL>

<P><STRONG><a name="[201]"></a>_dls</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[1fb]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[a9]"></a>_fp_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[248]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[249]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1ed]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[91]"></a>AnoPTv8CmdFun_MotorStart</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorStart))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorStart)
</UL>
<P><STRONG><a name="[92]"></a>AnoPTv8CmdFun_MotorStop</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorStop))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorStop)
</UL>
<P><STRONG><a name="[8e]"></a>AnoPTv8CmdFun_MotorReset</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorReset))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorEStop)
</UL>
<P><STRONG><a name="[90]"></a>AnoPTv8CmdFun_MotorSelfTest</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorSelfTest)
</UL>
<P><STRONG><a name="[8f]"></a>AnoPTv8CmdFun_MotorGetFault</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorGetFault)
</UL>
<P><STRONG><a name="[8d]"></a>AnoPTv8CmdFun_MotorClearFault</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorClearFault)
</UL>
<P><STRONG><a name="[93]"></a>AnoPTv8CmdFun_WaveCtrl</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = AnoPTv8CmdFun_WaveCtrl &rArr; AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoWaveCtrl)
</UL>
<P><STRONG><a name="[94]"></a>AnoPTv8CmdFun_WaveStopAll</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = AnoPTv8CmdFun_WaveStopAll &rArr; AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoWaveStopAll)
</UL>
<P><STRONG><a name="[17]"></a>class_init_handler</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, cdc_class.o(.text.class_init_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = class_init_handler &rArr; usbd_ept_open &rArr; usb_ept_open
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[18]"></a>class_clear_handler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, cdc_class.o(.text.class_clear_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = class_clear_handler &rArr; usbd_ept_close &rArr; usb_ept_close
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_close
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[19]"></a>class_setup_handler</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, cdc_class.o(.text.class_setup_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = class_setup_handler &rArr; usbd_ctrl_send &rArr; usbd_ept_send &rArr; usb_write_packet
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_recv
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1a]"></a>class_ept0_tx_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_ept0_tx_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1b]"></a>class_ept0_rx_handler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, cdc_class.o(.text.class_ept0_rx_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = class_ept0_rx_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_recv_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1c]"></a>class_in_handler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, cdc_class.o(.text.class_in_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = class_in_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_flush_tx_fifo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1d]"></a>class_out_handler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, cdc_class.o(.text.class_out_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = class_out_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_recv_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1e]"></a>class_sof_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_sof_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1f]"></a>class_event_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_event_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[20]"></a>get_device_descriptor</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_descriptor))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[21]"></a>get_device_qualifier</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_qualifier))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[22]"></a>get_device_configuration</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_configuration))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[23]"></a>get_device_other_speed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_other_speed))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[24]"></a>get_device_lang_id</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_lang_id))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[25]"></a>get_device_manufacturer_string</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_manufacturer_string))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[26]"></a>get_device_product_string</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_product_string))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[27]"></a>get_device_serial_string</STRONG> (Thumb, 362 bytes, Stack size 16 bytes, cdc_desc.o(.text.get_device_serial_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = get_device_serial_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[28]"></a>get_device_interface_string</STRONG> (Thumb, 154 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_interface_string))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[29]"></a>get_device_config_string</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_config_string))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[8a]"></a>Task_500us</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sysfsm.o(.text.Task_500us))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.Timer_Tasks_Init)
</UL>
<P><STRONG><a name="[8b]"></a>Task_1ms</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sysfsm.o(.text.Task_1ms))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.Timer_Tasks_Init)
</UL>
<P><STRONG><a name="[8c]"></a>Task_1s</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sysfsm.o(.text.Task_1s))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.Timer_Tasks_Init)
</UL>
<P><STRONG><a name="[82]"></a>State_PowerOn_Handler</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, sysfsm.o(.text.State_PowerOn_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[83]"></a>State_InitExternal_Handler</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, sysfsm.o(.text.State_InitExternal_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = State_InitExternal_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[84]"></a>State_SelfTest_Handler</STRONG> (Thumb, 246 bytes, Stack size 96 bytes, sysfsm.o(.text.State_SelfTest_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = State_SelfTest_Handler &rArr; SelfTest_Manager &rArr; ClockSelfTest &rArr; crm_clocks_freq_get &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelfTest_Manager
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[85]"></a>State_Hold_Handler</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, sysfsm.o(.text.State_Hold_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[86]"></a>State_Ready_Handler</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, sysfsm.o(.text.State_Ready_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[87]"></a>State_Running_Handler</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, sysfsm.o(.text.State_Running_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[88]"></a>State_Fault_Handler</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, sysfsm.o(.text.State_Fault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[89]"></a>State_Shutdown_Handler</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, sysfsm.o(.text.State_Shutdown_Handler))
<BR>[Address Reference Count : 1]<UL><LI> sysfsm.o(.text.System_StatusUpdate)
</UL>
<P><STRONG><a name="[1f9]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
</UL>

<P><STRONG><a name="[1f0]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
