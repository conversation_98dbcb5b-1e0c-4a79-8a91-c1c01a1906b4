/**
 * @file fast_trig_lookup.c
 * @brief Fast trigonometric functions using lookup table with linear interpolation
 *
 * This module provides high-performance sine and cosine calculations using a
 * pre-computed lookup table with linear interpolation for improved accuracy.
 * The functions accept radian angles as input and are completely hardware-agnostic.
 *
 * Features:
 * - Hardware-independent implementation
 * - Linear interpolation for enhanced precision
 * - Optimized combined sin/cos calculation
 * - Input range: any float radian value (automatically normalized)
 *
 * <AUTHOR> and refactored
 * @date 2025
 */

#include "fast_trig_lookup.h"
#include <math.h>

// Pre-computed sine lookup table covering 0 to 2π
const float g_sin_table[SIN_TABLE_SIZE] = {
    0.00000000f, 0.00153398f, 0.00306796f, 0.00460193f, 0.00613588f, 0.00766983f, 0.00920375f, 0.01073766f,
    0.01227154f, 0.01380539f, 0.01533921f, 0.01687299f, 0.01840673f, 0.01994043f, 0.02147408f, 0.02300768f,
    0.02454123f, 0.02607472f, 0.02760815f, 0.02914151f, 0.03067480f, 0.03220803f, 0.03374117f, 0.03527424f,
    0.03680722f, 0.03834012f, 0.03987293f, 0.04140564f, 0.04293826f, 0.04447077f, 0.04600318f, 0.04753548f,
    0.04906767f, 0.05059975f, 0.05213170f, 0.05366354f, 0.05519524f, 0.05672682f, 0.05825826f, 0.05978957f,
    0.06132074f, 0.06285176f, 0.06438263f, 0.06591335f, 0.06744392f, 0.06897433f, 0.07050457f, 0.07203465f,
    0.07356456f, 0.07509430f, 0.07662386f, 0.07815324f, 0.07968244f, 0.08121145f, 0.08274026f, 0.08426889f,
    0.08579731f, 0.08732554f, 0.08885355f, 0.09038136f, 0.09190896f, 0.09343634f, 0.09496350f, 0.09649043f,
    0.09801714f, 0.09954362f, 0.10106986f, 0.10259587f, 0.10412163f, 0.10564715f, 0.10717242f, 0.10869744f,
    0.11022221f, 0.11174671f, 0.11327095f, 0.11479493f, 0.11631863f, 0.11784206f, 0.11936521f, 0.12088809f,
    0.12241068f, 0.12393298f, 0.12545498f, 0.12697670f, 0.12849811f, 0.13001922f, 0.13154003f, 0.13306053f,
    0.13458071f, 0.13610058f, 0.13762012f, 0.13913934f, 0.14065824f, 0.14217680f, 0.14369503f, 0.14521292f,
    0.14673047f, 0.14824768f, 0.14976453f, 0.15128104f, 0.15279719f, 0.15431297f, 0.15582840f, 0.15734346f,
    0.15885814f, 0.16037246f, 0.16188639f, 0.16339995f, 0.16491312f, 0.16642590f, 0.16793829f, 0.16945029f,
    0.17096189f, 0.17247308f, 0.17398387f, 0.17549425f, 0.17700422f, 0.17851377f, 0.18002290f, 0.18153161f,
    0.18303989f, 0.18454774f, 0.18605515f, 0.18756213f, 0.18906866f, 0.19057475f, 0.19208040f, 0.19358559f,
    0.19509032f, 0.19659460f, 0.19809841f, 0.19960176f, 0.20110463f, 0.20260704f, 0.20410897f, 0.20561041f,
    0.20711138f, 0.20861185f, 0.21011184f, 0.21161133f, 0.21311032f, 0.21460881f, 0.21610680f, 0.21760427f,
    0.21910124f, 0.22059769f, 0.22209362f, 0.22358903f, 0.22508391f, 0.22657826f, 0.22807208f, 0.22956537f,
    0.23105811f, 0.23255031f, 0.23404196f, 0.23553306f, 0.23702361f, 0.23851359f, 0.24000302f, 0.24149189f,
    0.24298018f, 0.24446790f, 0.24595505f, 0.24744162f, 0.24892761f, 0.25041301f, 0.25189782f, 0.25338204f,
    0.25486566f, 0.25634868f, 0.25783110f, 0.25931292f, 0.26079412f, 0.26227471f, 0.26375468f, 0.26523403f,
    0.26671276f, 0.26819086f, 0.26966833f, 0.27114516f, 0.27262136f, 0.27409691f, 0.27557182f, 0.27704608f,
    0.27851969f, 0.27999264f, 0.28146494f, 0.28293657f, 0.28440754f, 0.28587783f, 0.28734746f, 0.28881641f,
    0.29028468f, 0.29175226f, 0.29321916f, 0.29468537f, 0.29615089f, 0.29761571f, 0.29907983f, 0.30054324f,
    0.30200595f, 0.30346795f, 0.30492923f, 0.30638980f, 0.30784964f, 0.30930876f, 0.31076715f, 0.31222481f,
    0.31368174f, 0.31513793f, 0.31659338f, 0.31804808f, 0.31950203f, 0.32095523f, 0.32240768f, 0.32385937f,
    0.32531029f, 0.32676045f, 0.32820984f, 0.32965846f, 0.33110631f, 0.33255337f, 0.33399965f, 0.33544515f,
    0.33688985f, 0.33833377f, 0.33977688f, 0.34121920f, 0.34266072f, 0.34410143f, 0.34554132f, 0.34698041f,
    0.34841868f, 0.34985613f, 0.35129276f, 0.35272856f, 0.35416353f, 0.35559766f, 0.35703096f, 0.35846342f,
    0.35989504f, 0.36132581f, 0.36275572f, 0.36418479f, 0.36561300f, 0.36704035f, 0.36846683f, 0.36989245f,
    0.37131719f, 0.37274107f, 0.37416406f, 0.37558618f, 0.37700741f, 0.37842775f, 0.37984721f, 0.38126577f,
    0.38268343f, 0.38410020f, 0.38551605f, 0.38693101f, 0.38834505f, 0.38975817f, 0.39117038f, 0.39258167f,
    0.39399204f, 0.39540148f, 0.39680999f, 0.39821756f, 0.39962420f, 0.40102990f, 0.40243465f, 0.40383846f,
    0.40524131f, 0.40664322f, 0.40804416f, 0.40944415f, 0.41084317f, 0.41224123f, 0.41363831f, 0.41503442f,
    0.41642956f, 0.41782372f, 0.41921689f, 0.42060907f, 0.42200027f, 0.42339047f, 0.42477968f, 0.42616789f,
    0.42755509f, 0.42894129f, 0.43032648f, 0.43171066f, 0.43309382f, 0.43447596f, 0.43585708f, 0.43723717f,
    0.43861624f, 0.43999427f, 0.44137127f, 0.44274723f, 0.44412214f, 0.44549602f, 0.44686884f, 0.44824061f,
    0.44961133f, 0.45098099f, 0.45234959f, 0.45371712f, 0.45508359f, 0.45644898f, 0.45781330f, 0.45917655f,
    0.46053871f, 0.46189979f, 0.46325978f, 0.46461869f, 0.46597650f, 0.46733321f, 0.46868882f, 0.47004333f,
    0.47139674f, 0.47274903f, 0.47410021f, 0.47545028f, 0.47679923f, 0.47814706f, 0.47949376f, 0.48083933f,
    0.48218377f, 0.48352708f, 0.48486925f, 0.48621028f, 0.48755016f, 0.48888890f, 0.49022648f, 0.49156292f,
    0.49289819f, 0.49423231f, 0.49556526f, 0.49689705f, 0.49822767f, 0.49955711f, 0.50088538f, 0.50221247f,
    0.50353838f, 0.50486311f, 0.50618665f, 0.50750899f, 0.50883014f, 0.51015010f, 0.51146885f, 0.51278640f,
    0.51410274f, 0.51541788f, 0.51673180f, 0.51804450f, 0.51935599f, 0.52066625f, 0.52197529f, 0.52328310f,
    0.52458968f, 0.52589503f, 0.52719913f, 0.52850200f, 0.52980362f, 0.53110400f, 0.53240313f, 0.53370100f,
    0.53499762f, 0.53629298f, 0.53758708f, 0.53887991f, 0.54017147f, 0.54146177f, 0.54275078f, 0.54403853f,
    0.54532499f, 0.54661017f, 0.54789406f, 0.54917666f, 0.55045797f, 0.55173799f, 0.55301671f, 0.55429412f,
    0.55557023f, 0.55684504f, 0.55811853f, 0.55939071f, 0.56066158f, 0.56193112f, 0.56319934f, 0.56446624f,
    0.56573181f, 0.56699605f, 0.56825895f, 0.56952052f, 0.57078075f, 0.57203963f, 0.57329717f, 0.57455336f,
    0.57580819f, 0.57706167f, 0.57831380f, 0.57956456f, 0.58081396f, 0.58206199f, 0.58330865f, 0.58455394f,
    0.58579786f, 0.58704039f, 0.58828155f, 0.58952132f, 0.59075970f, 0.59199669f, 0.59323230f, 0.59446650f,
    0.59569930f, 0.59693071f, 0.59816071f, 0.59938930f, 0.60061648f, 0.60184225f, 0.60306660f, 0.60428953f,
    0.60551104f, 0.60673113f, 0.60794978f, 0.60916701f, 0.61038281f, 0.61159716f, 0.61281008f, 0.61402156f,
    0.61523159f, 0.61644017f, 0.61764731f, 0.61885299f, 0.62005721f, 0.62125998f, 0.62246128f, 0.62366112f,
    0.62485949f, 0.62605639f, 0.62725182f, 0.62844577f, 0.62963824f, 0.63082923f, 0.63201874f, 0.63320676f,
    0.63439328f, 0.63557832f, 0.63676186f, 0.63794390f, 0.63912444f, 0.64030348f, 0.64148101f, 0.64265703f,
    0.64383154f, 0.64500454f, 0.64617601f, 0.64734597f, 0.64851440f, 0.64968131f, 0.65084668f, 0.65201053f,
    0.65317284f, 0.65433362f, 0.65549285f, 0.65665055f, 0.65780669f, 0.65896129f, 0.66011434f, 0.66126584f,
    0.66241578f, 0.66356416f, 0.66471098f, 0.66585623f, 0.66699992f, 0.66814204f, 0.66928259f, 0.67042156f,
    0.67155895f, 0.67269477f, 0.67382900f, 0.67496165f, 0.67609270f, 0.67722217f, 0.67835004f, 0.67947632f,
    0.68060100f, 0.68172407f, 0.68284555f, 0.68396541f, 0.68508367f, 0.68620031f, 0.68731534f, 0.68842875f,
    0.68954054f, 0.69065071f, 0.69175926f, 0.69286617f, 0.69397146f, 0.69507511f, 0.69617713f, 0.69727751f,
    0.69837625f, 0.69947334f, 0.70056879f, 0.70166259f, 0.70275474f, 0.70384524f, 0.70493408f, 0.70602126f,
    0.70710678f, 0.70819064f, 0.70927283f, 0.71035335f, 0.71143220f, 0.71250937f, 0.71358487f, 0.71465869f,
    0.71573083f, 0.71680128f, 0.71787005f, 0.71893712f, 0.72000251f, 0.72106620f, 0.72212819f, 0.72318849f,
    0.72424708f, 0.72530397f, 0.72635916f, 0.72741263f, 0.72846439f, 0.72951444f, 0.73056277f, 0.73160938f,
    0.73265427f, 0.73369744f, 0.73473888f, 0.73577859f, 0.73681657f, 0.73785281f, 0.73888732f, 0.73992010f,
    0.74095113f, 0.74198041f, 0.74300795f, 0.74403374f, 0.74505779f, 0.74608007f, 0.74710061f, 0.74811938f,
    0.74913639f, 0.75015165f, 0.75116513f, 0.75217685f, 0.75318680f, 0.75419498f, 0.75520138f, 0.75620600f,
    0.75720885f, 0.75820991f, 0.75920919f, 0.76020668f, 0.76120239f, 0.76219630f, 0.76318842f, 0.76417874f,
    0.76516727f, 0.76615399f, 0.76713891f, 0.76812203f, 0.76910334f, 0.77008284f, 0.77106052f, 0.77203640f,
    0.77301045f, 0.77398269f, 0.77495311f, 0.77592170f, 0.77688847f, 0.77785340f, 0.77881651f, 0.77977779f,
    0.78073723f, 0.78169483f, 0.78265060f, 0.78360452f, 0.78455660f, 0.78550683f, 0.78645521f, 0.78740175f,
    0.78834643f, 0.78928925f, 0.79023022f, 0.79116933f, 0.79210658f, 0.79304196f, 0.79397548f, 0.79490713f,
    0.79583690f, 0.79676481f, 0.79769084f, 0.79861499f, 0.79953727f, 0.80045766f, 0.80137617f, 0.80229280f,
    0.80320753f, 0.80412038f, 0.80503133f, 0.80594039f, 0.80684755f, 0.80775282f, 0.80865618f, 0.80955764f,
    0.81045720f, 0.81135485f, 0.81225059f, 0.81314441f, 0.81403633f, 0.81492633f, 0.81581441f, 0.81670057f,
    0.81758481f, 0.81846713f, 0.81934752f, 0.82022598f, 0.82110251f, 0.82197712f, 0.82284978f, 0.82372051f,
    0.82458930f, 0.82545615f, 0.82632106f, 0.82718403f, 0.82804505f, 0.82890411f, 0.82976123f, 0.83061640f,
    0.83146961f, 0.83232087f, 0.83317016f, 0.83401750f, 0.83486287f, 0.83570628f, 0.83654773f, 0.83738720f,
    0.83822471f, 0.83906024f, 0.83989379f, 0.84072537f, 0.84155498f, 0.84238260f, 0.84320824f, 0.84403190f,
    0.84485357f, 0.84567325f, 0.84649094f, 0.84730664f, 0.84812034f, 0.84893206f, 0.84974177f, 0.85054948f,
    0.85135519f, 0.85215890f, 0.85296060f, 0.85376030f, 0.85455799f, 0.85535366f, 0.85614733f, 0.85693898f,
    0.85772861f, 0.85851622f, 0.85930182f, 0.86008539f, 0.86086694f, 0.86164646f, 0.86242396f, 0.86319942f,
    0.86397286f, 0.86474426f, 0.86551362f, 0.86628095f, 0.86704625f, 0.86780950f, 0.86857071f, 0.86932987f,
    0.87008699f, 0.87084206f, 0.87159509f, 0.87234606f, 0.87309498f, 0.87384184f, 0.87458665f, 0.87532940f,
    0.87607009f, 0.87680872f, 0.87754529f, 0.87827979f, 0.87901223f, 0.87974259f, 0.88047089f, 0.88119711f,
    0.88192126f, 0.88264334f, 0.88336334f, 0.88408126f, 0.88479710f, 0.88551086f, 0.88622253f, 0.88693212f,
    0.88763962f, 0.88834503f, 0.88904836f, 0.88974959f, 0.89044872f, 0.89114576f, 0.89184071f, 0.89253356f,
    0.89322430f, 0.89391295f, 0.89459949f, 0.89528392f, 0.89596625f, 0.89664647f, 0.89732458f, 0.89800058f,
    0.89867447f, 0.89934624f, 0.90001589f, 0.90068343f, 0.90134885f, 0.90201214f, 0.90267332f, 0.90333237f,
    0.90398929f, 0.90464409f, 0.90529676f, 0.90594730f, 0.90659570f, 0.90724198f, 0.90788612f, 0.90852812f,
    0.90916798f, 0.90980571f, 0.91044129f, 0.91107473f, 0.91170603f, 0.91233518f, 0.91296219f, 0.91358705f,
    0.91420976f, 0.91483031f, 0.91544872f, 0.91606497f, 0.91667906f, 0.91729100f, 0.91790078f, 0.91850839f,
    0.91911385f, 0.91971715f, 0.92031828f, 0.92091724f, 0.92151404f, 0.92210867f, 0.92270113f, 0.92329142f,
    0.92387953f, 0.92446547f, 0.92504924f, 0.92563083f, 0.92621024f, 0.92678747f, 0.92736253f, 0.92793539f,
    0.92850608f, 0.92907458f, 0.92964090f, 0.93020502f, 0.93076696f, 0.93132671f, 0.93188427f, 0.93243963f,
    0.93299280f, 0.93354377f, 0.93409255f, 0.93463913f, 0.93518351f, 0.93572569f, 0.93626567f, 0.93680344f,
    0.93733901f, 0.93787238f, 0.93840353f, 0.93893248f, 0.93945922f, 0.93998375f, 0.94050607f, 0.94102618f,
    0.94154407f, 0.94205974f, 0.94257320f, 0.94308444f, 0.94359346f, 0.94410026f, 0.94460484f, 0.94510719f,
    0.94560733f, 0.94610523f, 0.94660091f, 0.94709437f, 0.94758559f, 0.94807459f, 0.94856135f, 0.94904588f,
    0.94952818f, 0.95000825f, 0.95048607f, 0.95096167f, 0.95143502f, 0.95190614f, 0.95237501f, 0.95284165f,
    0.95330604f, 0.95376819f, 0.95422810f, 0.95468575f, 0.95514117f, 0.95559433f, 0.95604525f, 0.95649392f,
    0.95694034f, 0.95738450f, 0.95782641f, 0.95826607f, 0.95870347f, 0.95913862f, 0.95957151f, 0.96000215f,
    0.96043052f, 0.96085663f, 0.96128049f, 0.96170208f, 0.96212140f, 0.96253847f, 0.96295327f, 0.96336580f,
    0.96377607f, 0.96418406f, 0.96458979f, 0.96499325f, 0.96539444f, 0.96579336f, 0.96619000f, 0.96658437f,
    0.96697647f, 0.96736629f, 0.96775384f, 0.96813910f, 0.96852209f, 0.96890280f, 0.96928124f, 0.96965739f,
    0.97003125f, 0.97040284f, 0.97077214f, 0.97113916f, 0.97150389f, 0.97186634f, 0.97222650f, 0.97258437f,
    0.97293995f, 0.97329325f, 0.97364425f, 0.97399296f, 0.97433938f, 0.97468351f, 0.97502535f, 0.97536489f,
    0.97570213f, 0.97603708f, 0.97636973f, 0.97670009f, 0.97702814f, 0.97735390f, 0.97767736f, 0.97799851f,
    0.97831737f, 0.97863392f, 0.97894818f, 0.97926012f, 0.97956977f, 0.97987710f, 0.98018214f, 0.98048486f,
    0.98078528f, 0.98108339f, 0.98137919f, 0.98167269f, 0.98196387f, 0.98225274f, 0.98253930f, 0.98282355f,
    0.98310549f, 0.98338511f, 0.98366242f, 0.98393741f, 0.98421009f, 0.98448046f, 0.98474850f, 0.98501423f,
    0.98527764f, 0.98553874f, 0.98579751f, 0.98605396f, 0.98630810f, 0.98655991f, 0.98680940f, 0.98705657f,
    0.98730142f, 0.98754394f, 0.98778414f, 0.98802202f, 0.98825757f, 0.98849079f, 0.98872169f, 0.98895026f,
    0.98917651f, 0.98940043f, 0.98962202f, 0.98984128f, 0.99005821f, 0.99027281f, 0.99048508f, 0.99069503f,
    0.99090264f, 0.99110791f, 0.99131086f, 0.99151147f, 0.99170975f, 0.99190570f, 0.99209931f, 0.99229059f,
    0.99247953f, 0.99266614f, 0.99285041f, 0.99303235f, 0.99321195f, 0.99338921f, 0.99356414f, 0.99373672f,
    0.99390697f, 0.99407488f, 0.99424045f, 0.99440368f, 0.99456457f, 0.99472312f, 0.99487933f, 0.99503320f,
    0.99518473f, 0.99533391f, 0.99548076f, 0.99562526f, 0.99576741f, 0.99590723f, 0.99604470f, 0.99617983f,
    0.99631261f, 0.99644305f, 0.99657115f, 0.99669690f, 0.99682030f, 0.99694136f, 0.99706007f, 0.99717644f,
    0.99729046f, 0.99740213f, 0.99751146f, 0.99761844f, 0.99772307f, 0.99782535f, 0.99792529f, 0.99802287f,
    0.99811811f, 0.99821100f, 0.99830154f, 0.99838974f, 0.99847558f, 0.99855907f, 0.99864022f, 0.99871901f,
    0.99879546f, 0.99886955f, 0.99894129f, 0.99901069f, 0.99907773f, 0.99914242f, 0.99920476f, 0.99926475f,
    0.99932238f, 0.99937767f, 0.99943060f, 0.99948119f, 0.99952942f, 0.99957530f, 0.99961882f, 0.99966000f,
    0.99969882f, 0.99973529f, 0.99976941f, 0.99980117f, 0.99983058f, 0.99985764f, 0.99988235f, 0.99990470f,
    0.99992470f, 0.99994235f, 0.99995764f, 0.99997059f, 0.99998118f, 0.99998941f, 0.99999529f, 0.99999882f,
    1.00000000f, 0.99999882f, 0.99999529f, 0.99998941f, 0.99998118f, 0.99997059f, 0.99995764f, 0.99994235f,
    0.99992470f, 0.99990470f, 0.99988235f, 0.99985764f, 0.99983058f, 0.99980117f, 0.99976941f, 0.99973529f,
    0.99969882f, 0.99966000f, 0.99961882f, 0.99957530f, 0.99952942f, 0.99948119f, 0.99943060f, 0.99937767f,
    0.99932238f, 0.99926475f, 0.99920476f, 0.99914242f, 0.99907773f, 0.99901069f, 0.99894129f, 0.99886955f,
    0.99879546f, 0.99871901f, 0.99864022f, 0.99855907f, 0.99847558f, 0.99838974f, 0.99830154f, 0.99821100f,
    0.99811811f, 0.99802287f, 0.99792529f, 0.99782535f, 0.99772307f, 0.99761844f, 0.99751146f, 0.99740213f,
    0.99729046f, 0.99717644f, 0.99706007f, 0.99694136f, 0.99682030f, 0.99669690f, 0.99657115f, 0.99644305f,
    0.99631261f, 0.99617983f, 0.99604470f, 0.99590723f, 0.99576741f, 0.99562526f, 0.99548076f, 0.99533391f,
    0.99518473f, 0.99503320f, 0.99487933f, 0.99472312f, 0.99456457f, 0.99440368f, 0.99424045f, 0.99407488f,
    0.99390697f, 0.99373672f, 0.99356414f, 0.99338921f, 0.99321195f, 0.99303235f, 0.99285041f, 0.99266614f,
    0.99247953f, 0.99229059f, 0.99209931f, 0.99190570f, 0.99170975f, 0.99151147f, 0.99131086f, 0.99110791f,
    0.99090264f, 0.99069503f, 0.99048508f, 0.99027281f, 0.99005821f, 0.98984128f, 0.98962202f, 0.98940043f,
    0.98917651f, 0.98895026f, 0.98872169f, 0.98849079f, 0.98825757f, 0.98802202f, 0.98778414f, 0.98754394f,
    0.98730142f, 0.98705657f, 0.98680940f, 0.98655991f, 0.98630810f, 0.98605396f, 0.98579751f, 0.98553874f,
    0.98527764f, 0.98501423f, 0.98474850f, 0.98448046f, 0.98421009f, 0.98393741f, 0.98366242f, 0.98338511f,
    0.98310549f, 0.98282355f, 0.98253930f, 0.98225274f, 0.98196387f, 0.98167269f, 0.98137919f, 0.98108339f,
    0.98078528f, 0.98048486f, 0.98018214f, 0.97987710f, 0.97956977f, 0.97926012f, 0.97894818f, 0.97863392f,
    0.97831737f, 0.97799851f, 0.97767736f, 0.97735390f, 0.97702814f, 0.97670009f, 0.97636973f, 0.97603708f,
    0.97570213f, 0.97536489f, 0.97502535f, 0.97468351f, 0.97433938f, 0.97399296f, 0.97364425f, 0.97329325f,
    0.97293995f, 0.97258437f, 0.97222650f, 0.97186634f, 0.97150389f, 0.97113916f, 0.97077214f, 0.97040284f,
    0.97003125f, 0.96965739f, 0.96928124f, 0.96890280f, 0.96852209f, 0.96813910f, 0.96775384f, 0.96736629f,
    0.96697647f, 0.96658437f, 0.96619000f, 0.96579336f, 0.96539444f, 0.96499325f, 0.96458979f, 0.96418406f,
    0.96377607f, 0.96336580f, 0.96295327f, 0.96253847f, 0.96212140f, 0.96170208f, 0.96128049f, 0.96085663f,
    0.96043052f, 0.96000215f, 0.95957151f, 0.95913862f, 0.95870347f, 0.95826607f, 0.95782641f, 0.95738450f,
    0.95694034f, 0.95649392f, 0.95604525f, 0.95559433f, 0.95514117f, 0.95468575f, 0.95422810f, 0.95376819f,
    0.95330604f, 0.95284165f, 0.95237501f, 0.95190614f, 0.95143502f, 0.95096167f, 0.95048607f, 0.95000825f,
    0.94952818f, 0.94904588f, 0.94856135f, 0.94807459f, 0.94758559f, 0.94709437f, 0.94660091f, 0.94610523f,
    0.94560733f, 0.94510719f, 0.94460484f, 0.94410026f, 0.94359346f, 0.94308444f, 0.94257320f, 0.94205974f,
    0.94154407f, 0.94102618f, 0.94050607f, 0.93998375f, 0.93945922f, 0.93893248f, 0.93840353f, 0.93787238f,
    0.93733901f, 0.93680344f, 0.93626567f, 0.93572569f, 0.93518351f, 0.93463913f, 0.93409255f, 0.93354377f,
    0.93299280f, 0.93243963f, 0.93188427f, 0.93132671f, 0.93076696f, 0.93020502f, 0.92964090f, 0.92907458f,
    0.92850608f, 0.92793539f, 0.92736253f, 0.92678747f, 0.92621024f, 0.92563083f, 0.92504924f, 0.92446547f,
    0.92387953f, 0.92329142f, 0.92270113f, 0.92210867f, 0.92151404f, 0.92091724f, 0.92031828f, 0.91971715f,
    0.91911385f, 0.91850839f, 0.91790078f, 0.91729100f, 0.91667906f, 0.91606497f, 0.91544872f, 0.91483031f,
    0.91420976f, 0.91358705f, 0.91296219f, 0.91233518f, 0.91170603f, 0.91107473f, 0.91044129f, 0.90980571f,
    0.90916798f, 0.90852812f, 0.90788612f, 0.90724198f, 0.90659570f, 0.90594730f, 0.90529676f, 0.90464409f,
    0.90398929f, 0.90333237f, 0.90267332f, 0.90201214f, 0.90134885f, 0.90068343f, 0.90001589f, 0.89934624f,
    0.89867447f, 0.89800058f, 0.89732458f, 0.89664647f, 0.89596625f, 0.89528392f, 0.89459949f, 0.89391295f,
    0.89322430f, 0.89253356f, 0.89184071f, 0.89114576f, 0.89044872f, 0.88974959f, 0.88904836f, 0.88834503f,
    0.88763962f, 0.88693212f, 0.88622253f, 0.88551086f, 0.88479710f, 0.88408126f, 0.88336334f, 0.88264334f,
    0.88192126f, 0.88119711f, 0.88047089f, 0.87974259f, 0.87901223f, 0.87827979f, 0.87754529f, 0.87680872f,
    0.87607009f, 0.87532940f, 0.87458665f, 0.87384184f, 0.87309498f, 0.87234606f, 0.87159509f, 0.87084206f,
    0.87008699f, 0.86932987f, 0.86857071f, 0.86780950f, 0.86704625f, 0.86628095f, 0.86551362f, 0.86474426f,
    0.86397286f, 0.86319942f, 0.86242396f, 0.86164646f, 0.86086694f, 0.86008539f, 0.85930182f, 0.85851622f,
    0.85772861f, 0.85693898f, 0.85614733f, 0.85535366f, 0.85455799f, 0.85376030f, 0.85296060f, 0.85215890f,
    0.85135519f, 0.85054948f, 0.84974177f, 0.84893206f, 0.84812034f, 0.84730664f, 0.84649094f, 0.84567325f,
    0.84485357f, 0.84403190f, 0.84320824f, 0.84238260f, 0.84155498f, 0.84072537f, 0.83989379f, 0.83906024f,
    0.83822471f, 0.83738720f, 0.83654773f, 0.83570628f, 0.83486287f, 0.83401750f, 0.83317016f, 0.83232087f,
    0.83146961f, 0.83061640f, 0.82976123f, 0.82890411f, 0.82804505f, 0.82718403f, 0.82632106f, 0.82545615f,
    0.82458930f, 0.82372051f, 0.82284978f, 0.82197712f, 0.82110251f, 0.82022598f, 0.81934752f, 0.81846713f,
    0.81758481f, 0.81670057f, 0.81581441f, 0.81492633f, 0.81403633f, 0.81314441f, 0.81225059f, 0.81135485f,
    0.81045720f, 0.80955764f, 0.80865618f, 0.80775282f, 0.80684755f, 0.80594039f, 0.80503133f, 0.80412038f,
    0.80320753f, 0.80229280f, 0.80137617f, 0.80045766f, 0.79953727f, 0.79861499f, 0.79769084f, 0.79676481f,
    0.79583690f, 0.79490713f, 0.79397548f, 0.79304196f, 0.79210658f, 0.79116933f, 0.79023022f, 0.78928925f,
    0.78834643f, 0.78740175f, 0.78645521f, 0.78550683f, 0.78455660f, 0.78360452f, 0.78265060f, 0.78169483f,
    0.78073723f, 0.77977779f, 0.77881651f, 0.77785340f, 0.77688847f, 0.77592170f, 0.77495311f, 0.77398269f,
    0.77301045f, 0.77203640f, 0.77106052f, 0.77008284f, 0.76910334f, 0.76812203f, 0.76713891f, 0.76615399f,
    0.76516727f, 0.76417874f, 0.76318842f, 0.76219630f, 0.76120239f, 0.76020668f, 0.75920919f, 0.75820991f,
    0.75720885f, 0.75620600f, 0.75520138f, 0.75419498f, 0.75318680f, 0.75217685f, 0.75116513f, 0.75015165f,
    0.74913639f, 0.74811938f, 0.74710061f, 0.74608007f, 0.74505779f, 0.74403374f, 0.74300795f, 0.74198041f,
    0.74095113f, 0.73992010f, 0.73888732f, 0.73785281f, 0.73681657f, 0.73577859f, 0.73473888f, 0.73369744f,
    0.73265427f, 0.73160938f, 0.73056277f, 0.72951444f, 0.72846439f, 0.72741263f, 0.72635916f, 0.72530397f,
    0.72424708f, 0.72318849f, 0.72212819f, 0.72106620f, 0.72000251f, 0.71893712f, 0.71787005f, 0.71680128f,
    0.71573083f, 0.71465869f, 0.71358487f, 0.71250937f, 0.71143220f, 0.71035335f, 0.70927283f, 0.70819064f,
    0.70710678f, 0.70602126f, 0.70493408f, 0.70384524f, 0.70275474f, 0.70166259f, 0.70056879f, 0.69947334f,
    0.69837625f, 0.69727751f, 0.69617713f, 0.69507511f, 0.69397146f, 0.69286617f, 0.69175926f, 0.69065071f,
    0.68954054f, 0.68842875f, 0.68731534f, 0.68620031f, 0.68508367f, 0.68396541f, 0.68284555f, 0.68172407f,
    0.68060100f, 0.67947632f, 0.67835004f, 0.67722217f, 0.67609270f, 0.67496165f, 0.67382900f, 0.67269477f,
    0.67155895f, 0.67042156f, 0.66928259f, 0.66814204f, 0.66699992f, 0.66585623f, 0.66471098f, 0.66356416f,
    0.66241578f, 0.66126584f, 0.66011434f, 0.65896129f, 0.65780669f, 0.65665055f, 0.65549285f, 0.65433362f,
    0.65317284f, 0.65201053f, 0.65084668f, 0.64968131f, 0.64851440f, 0.64734597f, 0.64617601f, 0.64500454f,
    0.64383154f, 0.64265703f, 0.64148101f, 0.64030348f, 0.63912444f, 0.63794390f, 0.63676186f, 0.63557832f,
    0.63439328f, 0.63320676f, 0.63201874f, 0.63082923f, 0.62963824f, 0.62844577f, 0.62725182f, 0.62605639f,
    0.62485949f, 0.62366112f, 0.62246128f, 0.62125998f, 0.62005721f, 0.61885299f, 0.61764731f, 0.61644017f,
    0.61523159f, 0.61402156f, 0.61281008f, 0.61159716f, 0.61038281f, 0.60916701f, 0.60794978f, 0.60673113f,
    0.60551104f, 0.60428953f, 0.60306660f, 0.60184225f, 0.60061648f, 0.59938930f, 0.59816071f, 0.59693071f,
    0.59569930f, 0.59446650f, 0.59323230f, 0.59199669f, 0.59075970f, 0.58952132f, 0.58828155f, 0.58704039f,
    0.58579786f, 0.58455394f, 0.58330865f, 0.58206199f, 0.58081396f, 0.57956456f, 0.57831380f, 0.57706167f,
    0.57580819f, 0.57455336f, 0.57329717f, 0.57203963f, 0.57078075f, 0.56952052f, 0.56825895f, 0.56699605f,
    0.56573181f, 0.56446624f, 0.56319934f, 0.56193112f, 0.56066158f, 0.55939071f, 0.55811853f, 0.55684504f,
    0.55557023f, 0.55429412f, 0.55301671f, 0.55173799f, 0.55045797f, 0.54917666f, 0.54789406f, 0.54661017f,
    0.54532499f, 0.54403853f, 0.54275078f, 0.54146177f, 0.54017147f, 0.53887991f, 0.53758708f, 0.53629298f,
    0.53499762f, 0.53370100f, 0.53240313f, 0.53110400f, 0.52980362f, 0.52850200f, 0.52719913f, 0.52589503f,
    0.52458968f, 0.52328310f, 0.52197529f, 0.52066625f, 0.51935599f, 0.51804450f, 0.51673180f, 0.51541788f,
    0.51410274f, 0.51278640f, 0.51146885f, 0.51015010f, 0.50883014f, 0.50750899f, 0.50618665f, 0.50486311f,
    0.50353838f, 0.50221247f, 0.50088538f, 0.49955711f, 0.49822767f, 0.49689705f, 0.49556526f, 0.49423231f,
    0.49289819f, 0.49156292f, 0.49022648f, 0.48888890f, 0.48755016f, 0.48621028f, 0.48486925f, 0.48352708f,
    0.48218377f, 0.48083933f, 0.47949376f, 0.47814706f, 0.47679923f, 0.47545028f, 0.47410021f, 0.47274903f,
    0.47139674f, 0.47004333f, 0.46868882f, 0.46733321f, 0.46597650f, 0.46461869f, 0.46325978f, 0.46189979f,
    0.46053871f, 0.45917655f, 0.45781330f, 0.45644898f, 0.45508359f, 0.45371712f, 0.45234959f, 0.45098099f,
    0.44961133f, 0.44824061f, 0.44686884f, 0.44549602f, 0.44412214f, 0.44274723f, 0.44137127f, 0.43999427f,
    0.43861624f, 0.43723717f, 0.43585708f, 0.43447596f, 0.43309382f, 0.43171066f, 0.43032648f, 0.42894129f,
    0.42755509f, 0.42616789f, 0.42477968f, 0.42339047f, 0.42200027f, 0.42060907f, 0.41921689f, 0.41782372f,
    0.41642956f, 0.41503442f, 0.41363831f, 0.41224123f, 0.41084317f, 0.40944415f, 0.40804416f, 0.40664322f,
    0.40524131f, 0.40383846f, 0.40243465f, 0.40102990f, 0.39962420f, 0.39821756f, 0.39680999f, 0.39540148f,
    0.39399204f, 0.39258167f, 0.39117038f, 0.38975817f, 0.38834505f, 0.38693101f, 0.38551605f, 0.38410020f,
    0.38268343f, 0.38126577f, 0.37984721f, 0.37842775f, 0.37700741f, 0.37558618f, 0.37416406f, 0.37274107f,
    0.37131719f, 0.36989245f, 0.36846683f, 0.36704035f, 0.36561300f, 0.36418479f, 0.36275572f, 0.36132581f,
    0.35989504f, 0.35846342f, 0.35703096f, 0.35559766f, 0.35416353f, 0.35272856f, 0.35129276f, 0.34985613f,
    0.34841868f, 0.34698041f, 0.34554132f, 0.34410143f, 0.34266072f, 0.34121920f, 0.33977688f, 0.33833377f,
    0.33688985f, 0.33544515f, 0.33399965f, 0.33255337f, 0.33110631f, 0.32965846f, 0.32820984f, 0.32676045f,
    0.32531029f, 0.32385937f, 0.32240768f, 0.32095523f, 0.31950203f, 0.31804808f, 0.31659338f, 0.31513793f,
    0.31368174f, 0.31222481f, 0.31076715f, 0.30930876f, 0.30784964f, 0.30638980f, 0.30492923f, 0.30346795f,
    0.30200595f, 0.30054324f, 0.29907983f, 0.29761571f, 0.29615089f, 0.29468537f, 0.29321916f, 0.29175226f,
    0.29028468f, 0.28881641f, 0.28734746f, 0.28587783f, 0.28440754f, 0.28293657f, 0.28146494f, 0.27999264f,
    0.27851969f, 0.27704608f, 0.27557182f, 0.27409691f, 0.27262136f, 0.27114516f, 0.26966833f, 0.26819086f,
    0.26671276f, 0.26523403f, 0.26375468f, 0.26227471f, 0.26079412f, 0.25931292f, 0.25783110f, 0.25634868f,
    0.25486566f, 0.25338204f, 0.25189782f, 0.25041301f, 0.24892761f, 0.24744162f, 0.24595505f, 0.24446790f,
    0.24298018f, 0.24149189f, 0.24000302f, 0.23851359f, 0.23702361f, 0.23553306f, 0.23404196f, 0.23255031f,
    0.23105811f, 0.22956537f, 0.22807208f, 0.22657826f, 0.22508391f, 0.22358903f, 0.22209362f, 0.22059769f,
    0.21910124f, 0.21760427f, 0.21610680f, 0.21460881f, 0.21311032f, 0.21161133f, 0.21011184f, 0.20861185f,
    0.20711138f, 0.20561041f, 0.20410897f, 0.20260704f, 0.20110463f, 0.19960176f, 0.19809841f, 0.19659460f,
    0.19509032f, 0.19358559f, 0.19208040f, 0.19057475f, 0.18906866f, 0.18756213f, 0.18605515f, 0.18454774f,
    0.18303989f, 0.18153161f, 0.18002290f, 0.17851377f, 0.17700422f, 0.17549425f, 0.17398387f, 0.17247308f,
    0.17096189f, 0.16945029f, 0.16793829f, 0.16642590f, 0.16491312f, 0.16339995f, 0.16188639f, 0.16037246f,
    0.15885814f, 0.15734346f, 0.15582840f, 0.15431297f, 0.15279719f, 0.15128104f, 0.14976453f, 0.14824768f,
    0.14673047f, 0.14521292f, 0.14369503f, 0.14217680f, 0.14065824f, 0.13913934f, 0.13762012f, 0.13610058f,
    0.13458071f, 0.13306053f, 0.13154003f, 0.13001922f, 0.12849811f, 0.12697670f, 0.12545498f, 0.12393298f,
    0.12241068f, 0.12088809f, 0.11936521f, 0.11784206f, 0.11631863f, 0.11479493f, 0.11327095f, 0.11174671f,
    0.11022221f, 0.10869744f, 0.10717242f, 0.10564715f, 0.10412163f, 0.10259587f, 0.10106986f, 0.09954362f,
    0.09801714f, 0.09649043f, 0.09496350f, 0.09343634f, 0.09190896f, 0.09038136f, 0.08885355f, 0.08732554f,
    0.08579731f, 0.08426889f, 0.08274026f, 0.08121145f, 0.07968244f, 0.07815324f, 0.07662386f, 0.07509430f,
    0.07356456f, 0.07203465f, 0.07050457f, 0.06897433f, 0.06744392f, 0.06591335f, 0.06438263f, 0.06285176f,
    0.06132074f, 0.05978957f, 0.05825826f, 0.05672682f, 0.05519524f, 0.05366354f, 0.05213170f, 0.05059975f,
    0.04906767f, 0.04753548f, 0.04600318f, 0.04447077f, 0.04293826f, 0.04140564f, 0.03987293f, 0.03834012f,
    0.03680722f, 0.03527424f, 0.03374117f, 0.03220803f, 0.03067480f, 0.02914151f, 0.02760815f, 0.02607472f,
    0.02454123f, 0.02300768f, 0.02147408f, 0.01994043f, 0.01840673f, 0.01687299f, 0.01533921f, 0.01380539f,
    0.01227154f, 0.01073766f, 0.00920375f, 0.00766983f, 0.00613588f, 0.00460193f, 0.00306796f, 0.00153398f,
    0.00000000f, -0.00153398f, -0.00306796f, -0.00460193f, -0.00613588f, -0.00766983f, -0.00920375f, -0.01073766f,
    -0.01227154f, -0.01380539f, -0.01533921f, -0.01687299f, -0.01840673f, -0.01994043f, -0.02147408f, -0.02300768f,
    -0.02454123f, -0.02607472f, -0.02760815f, -0.02914151f, -0.03067480f, -0.03220803f, -0.03374117f, -0.03527424f,
    -0.03680722f, -0.03834012f, -0.03987293f, -0.04140564f, -0.04293826f, -0.04447077f, -0.04600318f, -0.04753548f,
    -0.04906767f, -0.05059975f, -0.05213170f, -0.05366354f, -0.05519524f, -0.05672682f, -0.05825826f, -0.05978957f,
    -0.06132074f, -0.06285176f, -0.06438263f, -0.06591335f, -0.06744392f, -0.06897433f, -0.07050457f, -0.07203465f,
    -0.07356456f, -0.07509430f, -0.07662386f, -0.07815324f, -0.07968244f, -0.08121145f, -0.08274026f, -0.08426889f,
    -0.08579731f, -0.08732554f, -0.08885355f, -0.09038136f, -0.09190896f, -0.09343634f, -0.09496350f, -0.09649043f,
    -0.09801714f, -0.09954362f, -0.10106986f, -0.10259587f, -0.10412163f, -0.10564715f, -0.10717242f, -0.10869744f,
    -0.11022221f, -0.11174671f, -0.11327095f, -0.11479493f, -0.11631863f, -0.11784206f, -0.11936521f, -0.12088809f,
    -0.12241068f, -0.12393298f, -0.12545498f, -0.12697670f, -0.12849811f, -0.13001922f, -0.13154003f, -0.13306053f,
    -0.13458071f, -0.13610058f, -0.13762012f, -0.13913934f, -0.14065824f, -0.14217680f, -0.14369503f, -0.14521292f,
    -0.14673047f, -0.14824768f, -0.14976453f, -0.15128104f, -0.15279719f, -0.15431297f, -0.15582840f, -0.15734346f,
    -0.15885814f, -0.16037246f, -0.16188639f, -0.16339995f, -0.16491312f, -0.16642590f, -0.16793829f, -0.16945029f,
    -0.17096189f, -0.17247308f, -0.17398387f, -0.17549425f, -0.17700422f, -0.17851377f, -0.18002290f, -0.18153161f,
    -0.18303989f, -0.18454774f, -0.18605515f, -0.18756213f, -0.18906866f, -0.19057475f, -0.19208040f, -0.19358559f,
    -0.19509032f, -0.19659460f, -0.19809841f, -0.19960176f, -0.20110463f, -0.20260704f, -0.20410897f, -0.20561041f,
    -0.20711138f, -0.20861185f, -0.21011184f, -0.21161133f, -0.21311032f, -0.21460881f, -0.21610680f, -0.21760427f,
    -0.21910124f, -0.22059769f, -0.22209362f, -0.22358903f, -0.22508391f, -0.22657826f, -0.22807208f, -0.22956537f,
    -0.23105811f, -0.23255031f, -0.23404196f, -0.23553306f, -0.23702361f, -0.23851359f, -0.24000302f, -0.24149189f,
    -0.24298018f, -0.24446790f, -0.24595505f, -0.24744162f, -0.24892761f, -0.25041301f, -0.25189782f, -0.25338204f,
    -0.25486566f, -0.25634868f, -0.25783110f, -0.25931292f, -0.26079412f, -0.26227471f, -0.26375468f, -0.26523403f,
    -0.26671276f, -0.26819086f, -0.26966833f, -0.27114516f, -0.27262136f, -0.27409691f, -0.27557182f, -0.27704608f,
    -0.27851969f, -0.27999264f, -0.28146494f, -0.28293657f, -0.28440754f, -0.28587783f, -0.28734746f, -0.28881641f,
    -0.29028468f, -0.29175226f, -0.29321916f, -0.29468537f, -0.29615089f, -0.29761571f, -0.29907983f, -0.30054324f,
    -0.30200595f, -0.30346795f, -0.30492923f, -0.30638980f, -0.30784964f, -0.30930876f, -0.31076715f, -0.31222481f,
    -0.31368174f, -0.31513793f, -0.31659338f, -0.31804808f, -0.31950203f, -0.32095523f, -0.32240768f, -0.32385937f,
    -0.32531029f, -0.32676045f, -0.32820984f, -0.32965846f, -0.33110631f, -0.33255337f, -0.33399965f, -0.33544515f,
    -0.33688985f, -0.33833377f, -0.33977688f, -0.34121920f, -0.34266072f, -0.34410143f, -0.34554132f, -0.34698041f,
    -0.34841868f, -0.34985613f, -0.35129276f, -0.35272856f, -0.35416353f, -0.35559766f, -0.35703096f, -0.35846342f,
    -0.35989504f, -0.36132581f, -0.36275572f, -0.36418479f, -0.36561300f, -0.36704035f, -0.36846683f, -0.36989245f,
    -0.37131719f, -0.37274107f, -0.37416406f, -0.37558618f, -0.37700741f, -0.37842775f, -0.37984721f, -0.38126577f,
    -0.38268343f, -0.38410020f, -0.38551605f, -0.38693101f, -0.38834505f, -0.38975817f, -0.39117038f, -0.39258167f,
    -0.39399204f, -0.39540148f, -0.39680999f, -0.39821756f, -0.39962420f, -0.40102990f, -0.40243465f, -0.40383846f,
    -0.40524131f, -0.40664322f, -0.40804416f, -0.40944415f, -0.41084317f, -0.41224123f, -0.41363831f, -0.41503442f,
    -0.41642956f, -0.41782372f, -0.41921689f, -0.42060907f, -0.42200027f, -0.42339047f, -0.42477968f, -0.42616789f,
    -0.42755509f, -0.42894129f, -0.43032648f, -0.43171066f, -0.43309382f, -0.43447596f, -0.43585708f, -0.43723717f,
    -0.43861624f, -0.43999427f, -0.44137127f, -0.44274723f, -0.44412214f, -0.44549602f, -0.44686884f, -0.44824061f,
    -0.44961133f, -0.45098099f, -0.45234959f, -0.45371712f, -0.45508359f, -0.45644898f, -0.45781330f, -0.45917655f,
    -0.46053871f, -0.46189979f, -0.46325978f, -0.46461869f, -0.46597650f, -0.46733321f, -0.46868882f, -0.47004333f,
    -0.47139674f, -0.47274903f, -0.47410021f, -0.47545028f, -0.47679923f, -0.47814706f, -0.47949376f, -0.48083933f,
    -0.48218377f, -0.48352708f, -0.48486925f, -0.48621028f, -0.48755016f, -0.48888890f, -0.49022648f, -0.49156292f,
    -0.49289819f, -0.49423231f, -0.49556526f, -0.49689705f, -0.49822767f, -0.49955711f, -0.50088538f, -0.50221247f,
    -0.50353838f, -0.50486311f, -0.50618665f, -0.50750899f, -0.50883014f, -0.51015010f, -0.51146885f, -0.51278640f,
    -0.51410274f, -0.51541788f, -0.51673180f, -0.51804450f, -0.51935599f, -0.52066625f, -0.52197529f, -0.52328310f,
    -0.52458968f, -0.52589503f, -0.52719913f, -0.52850200f, -0.52980362f, -0.53110400f, -0.53240313f, -0.53370100f,
    -0.53499762f, -0.53629298f, -0.53758708f, -0.53887991f, -0.54017147f, -0.54146177f, -0.54275078f, -0.54403853f,
    -0.54532499f, -0.54661017f, -0.54789406f, -0.54917666f, -0.55045797f, -0.55173799f, -0.55301671f, -0.55429412f,
    -0.55557023f, -0.55684504f, -0.55811853f, -0.55939071f, -0.56066158f, -0.56193112f, -0.56319934f, -0.56446624f,
    -0.56573181f, -0.56699605f, -0.56825895f, -0.56952052f, -0.57078075f, -0.57203963f, -0.57329717f, -0.57455336f,
    -0.57580819f, -0.57706167f, -0.57831380f, -0.57956456f, -0.58081396f, -0.58206199f, -0.58330865f, -0.58455394f,
    -0.58579786f, -0.58704039f, -0.58828155f, -0.58952132f, -0.59075970f, -0.59199669f, -0.59323230f, -0.59446650f,
    -0.59569930f, -0.59693071f, -0.59816071f, -0.59938930f, -0.60061648f, -0.60184225f, -0.60306660f, -0.60428953f,
    -0.60551104f, -0.60673113f, -0.60794978f, -0.60916701f, -0.61038281f, -0.61159716f, -0.61281008f, -0.61402156f,
    -0.61523159f, -0.61644017f, -0.61764731f, -0.61885299f, -0.62005721f, -0.62125998f, -0.62246128f, -0.62366112f,
    -0.62485949f, -0.62605639f, -0.62725182f, -0.62844577f, -0.62963824f, -0.63082923f, -0.63201874f, -0.63320676f,
    -0.63439328f, -0.63557832f, -0.63676186f, -0.63794390f, -0.63912444f, -0.64030348f, -0.64148101f, -0.64265703f,
    -0.64383154f, -0.64500454f, -0.64617601f, -0.64734597f, -0.64851440f, -0.64968131f, -0.65084668f, -0.65201053f,
    -0.65317284f, -0.65433362f, -0.65549285f, -0.65665055f, -0.65780669f, -0.65896129f, -0.66011434f, -0.66126584f,
    -0.66241578f, -0.66356416f, -0.66471098f, -0.66585623f, -0.66699992f, -0.66814204f, -0.66928259f, -0.67042156f,
    -0.67155895f, -0.67269477f, -0.67382900f, -0.67496165f, -0.67609270f, -0.67722217f, -0.67835004f, -0.67947632f,
    -0.68060100f, -0.68172407f, -0.68284555f, -0.68396541f, -0.68508367f, -0.68620031f, -0.68731534f, -0.68842875f,
    -0.68954054f, -0.69065071f, -0.69175926f, -0.69286617f, -0.69397146f, -0.69507511f, -0.69617713f, -0.69727751f,
    -0.69837625f, -0.69947334f, -0.70056879f, -0.70166259f, -0.70275474f, -0.70384524f, -0.70493408f, -0.70602126f,
    -0.70710678f, -0.70819064f, -0.70927283f, -0.71035335f, -0.71143220f, -0.71250937f, -0.71358487f, -0.71465869f,
    -0.71573083f, -0.71680128f, -0.71787005f, -0.71893712f, -0.72000251f, -0.72106620f, -0.72212819f, -0.72318849f,
    -0.72424708f, -0.72530397f, -0.72635916f, -0.72741263f, -0.72846439f, -0.72951444f, -0.73056277f, -0.73160938f,
    -0.73265427f, -0.73369744f, -0.73473888f, -0.73577859f, -0.73681657f, -0.73785281f, -0.73888732f, -0.73992010f,
    -0.74095113f, -0.74198041f, -0.74300795f, -0.74403374f, -0.74505779f, -0.74608007f, -0.74710061f, -0.74811938f,
    -0.74913639f, -0.75015165f, -0.75116513f, -0.75217685f, -0.75318680f, -0.75419498f, -0.75520138f, -0.75620600f,
    -0.75720885f, -0.75820991f, -0.75920919f, -0.76020668f, -0.76120239f, -0.76219630f, -0.76318842f, -0.76417874f,
    -0.76516727f, -0.76615399f, -0.76713891f, -0.76812203f, -0.76910334f, -0.77008284f, -0.77106052f, -0.77203640f,
    -0.77301045f, -0.77398269f, -0.77495311f, -0.77592170f, -0.77688847f, -0.77785340f, -0.77881651f, -0.77977779f,
    -0.78073723f, -0.78169483f, -0.78265060f, -0.78360452f, -0.78455660f, -0.78550683f, -0.78645521f, -0.78740175f,
    -0.78834643f, -0.78928925f, -0.79023022f, -0.79116933f, -0.79210658f, -0.79304196f, -0.79397548f, -0.79490713f,
    -0.79583690f, -0.79676481f, -0.79769084f, -0.79861499f, -0.79953727f, -0.80045766f, -0.80137617f, -0.80229280f,
    -0.80320753f, -0.80412038f, -0.80503133f, -0.80594039f, -0.80684755f, -0.80775282f, -0.80865618f, -0.80955764f,
    -0.81045720f, -0.81135485f, -0.81225059f, -0.81314441f, -0.81403633f, -0.81492633f, -0.81581441f, -0.81670057f,
    -0.81758481f, -0.81846713f, -0.81934752f, -0.82022598f, -0.82110251f, -0.82197712f, -0.82284978f, -0.82372051f,
    -0.82458930f, -0.82545615f, -0.82632106f, -0.82718403f, -0.82804505f, -0.82890411f, -0.82976123f, -0.83061640f,
    -0.83146961f, -0.83232087f, -0.83317016f, -0.83401750f, -0.83486287f, -0.83570628f, -0.83654773f, -0.83738720f,
    -0.83822471f, -0.83906024f, -0.83989379f, -0.84072537f, -0.84155498f, -0.84238260f, -0.84320824f, -0.84403190f,
    -0.84485357f, -0.84567325f, -0.84649094f, -0.84730664f, -0.84812034f, -0.84893206f, -0.84974177f, -0.85054948f,
    -0.85135519f, -0.85215890f, -0.85296060f, -0.85376030f, -0.85455799f, -0.85535366f, -0.85614733f, -0.85693898f,
    -0.85772861f, -0.85851622f, -0.85930182f, -0.86008539f, -0.86086694f, -0.86164646f, -0.86242396f, -0.86319942f,
    -0.86397286f, -0.86474426f, -0.86551362f, -0.86628095f, -0.86704625f, -0.86780950f, -0.86857071f, -0.86932987f,
    -0.87008699f, -0.87084206f, -0.87159509f, -0.87234606f, -0.87309498f, -0.87384184f, -0.87458665f, -0.87532940f,
    -0.87607009f, -0.87680872f, -0.87754529f, -0.87827979f, -0.87901223f, -0.87974259f, -0.88047089f, -0.88119711f,
    -0.88192126f, -0.88264334f, -0.88336334f, -0.88408126f, -0.88479710f, -0.88551086f, -0.88622253f, -0.88693212f,
    -0.88763962f, -0.88834503f, -0.88904836f, -0.88974959f, -0.89044872f, -0.89114576f, -0.89184071f, -0.89253356f,
    -0.89322430f, -0.89391295f, -0.89459949f, -0.89528392f, -0.89596625f, -0.89664647f, -0.89732458f, -0.89800058f,
    -0.89867447f, -0.89934624f, -0.90001589f, -0.90068343f, -0.90134885f, -0.90201214f, -0.90267332f, -0.90333237f,
    -0.90398929f, -0.90464409f, -0.90529676f, -0.90594730f, -0.90659570f, -0.90724198f, -0.90788612f, -0.90852812f,
    -0.90916798f, -0.90980571f, -0.91044129f, -0.91107473f, -0.91170603f, -0.91233518f, -0.91296219f, -0.91358705f,
    -0.91420976f, -0.91483031f, -0.91544872f, -0.91606497f, -0.91667906f, -0.91729100f, -0.91790078f, -0.91850839f,
    -0.91911385f, -0.91971715f, -0.92031828f, -0.92091724f, -0.92151404f, -0.92210867f, -0.92270113f, -0.92329142f,
    -0.92387953f, -0.92446547f, -0.92504924f, -0.92563083f, -0.92621024f, -0.92678747f, -0.92736253f, -0.92793539f,
    -0.92850608f, -0.92907458f, -0.92964090f, -0.93020502f, -0.93076696f, -0.93132671f, -0.93188427f, -0.93243963f,
    -0.93299280f, -0.93354377f, -0.93409255f, -0.93463913f, -0.93518351f, -0.93572569f, -0.93626567f, -0.93680344f,
    -0.93733901f, -0.93787238f, -0.93840353f, -0.93893248f, -0.93945922f, -0.93998375f, -0.94050607f, -0.94102618f,
    -0.94154407f, -0.94205974f, -0.94257320f, -0.94308444f, -0.94359346f, -0.94410026f, -0.94460484f, -0.94510719f,
    -0.94560733f, -0.94610523f, -0.94660091f, -0.94709437f, -0.94758559f, -0.94807459f, -0.94856135f, -0.94904588f,
    -0.94952818f, -0.95000825f, -0.95048607f, -0.95096167f, -0.95143502f, -0.95190614f, -0.95237501f, -0.95284165f,
    -0.95330604f, -0.95376819f, -0.95422810f, -0.95468575f, -0.95514117f, -0.95559433f, -0.95604525f, -0.95649392f,
    -0.95694034f, -0.95738450f, -0.95782641f, -0.95826607f, -0.95870347f, -0.95913862f, -0.95957151f, -0.96000215f,
    -0.96043052f, -0.96085663f, -0.96128049f, -0.96170208f, -0.96212140f, -0.96253847f, -0.96295327f, -0.96336580f,
    -0.96377607f, -0.96418406f, -0.96458979f, -0.96499325f, -0.96539444f, -0.96579336f, -0.96619000f, -0.96658437f,
    -0.96697647f, -0.96736629f, -0.96775384f, -0.96813910f, -0.96852209f, -0.96890280f, -0.96928124f, -0.96965739f,
    -0.97003125f, -0.97040284f, -0.97077214f, -0.97113916f, -0.97150389f, -0.97186634f, -0.97222650f, -0.97258437f,
    -0.97293995f, -0.97329325f, -0.97364425f, -0.97399296f, -0.97433938f, -0.97468351f, -0.97502535f, -0.97536489f,
    -0.97570213f, -0.97603708f, -0.97636973f, -0.97670009f, -0.97702814f, -0.97735390f, -0.97767736f, -0.97799851f,
    -0.97831737f, -0.97863392f, -0.97894818f, -0.97926012f, -0.97956977f, -0.97987710f, -0.98018214f, -0.98048486f,
    -0.98078528f, -0.98108339f, -0.98137919f, -0.98167269f, -0.98196387f, -0.98225274f, -0.98253930f, -0.98282355f,
    -0.98310549f, -0.98338511f, -0.98366242f, -0.98393741f, -0.98421009f, -0.98448046f, -0.98474850f, -0.98501423f,
    -0.98527764f, -0.98553874f, -0.98579751f, -0.98605396f, -0.98630810f, -0.98655991f, -0.98680940f, -0.98705657f,
    -0.98730142f, -0.98754394f, -0.98778414f, -0.98802202f, -0.98825757f, -0.98849079f, -0.98872169f, -0.98895026f,
    -0.98917651f, -0.98940043f, -0.98962202f, -0.98984128f, -0.99005821f, -0.99027281f, -0.99048508f, -0.99069503f,
    -0.99090264f, -0.99110791f, -0.99131086f, -0.99151147f, -0.99170975f, -0.99190570f, -0.99209931f, -0.99229059f,
    -0.99247953f, -0.99266614f, -0.99285041f, -0.99303235f, -0.99321195f, -0.99338921f, -0.99356414f, -0.99373672f,
    -0.99390697f, -0.99407488f, -0.99424045f, -0.99440368f, -0.99456457f, -0.99472312f, -0.99487933f, -0.99503320f,
    -0.99518473f, -0.99533391f, -0.99548076f, -0.99562526f, -0.99576741f, -0.99590723f, -0.99604470f, -0.99617983f,
    -0.99631261f, -0.99644305f, -0.99657115f, -0.99669690f, -0.99682030f, -0.99694136f, -0.99706007f, -0.99717644f,
    -0.99729046f, -0.99740213f, -0.99751146f, -0.99761844f, -0.99772307f, -0.99782535f, -0.99792529f, -0.99802287f,
    -0.99811811f, -0.99821100f, -0.99830154f, -0.99838974f, -0.99847558f, -0.99855907f, -0.99864022f, -0.99871901f,
    -0.99879546f, -0.99886955f, -0.99894129f, -0.99901069f, -0.99907773f, -0.99914242f, -0.99920476f, -0.99926475f,
    -0.99932238f, -0.99937767f, -0.99943060f, -0.99948119f, -0.99952942f, -0.99957530f, -0.99961882f, -0.99966000f,
    -0.99969882f, -0.99973529f, -0.99976941f, -0.99980117f, -0.99983058f, -0.99985764f, -0.99988235f, -0.99990470f,
    -0.99992470f, -0.99994235f, -0.99995764f, -0.99997059f, -0.99998118f, -0.99998941f, -0.99999529f, -0.99999882f,
    -1.00000000f, -0.99999882f, -0.99999529f, -0.99998941f, -0.99998118f, -0.99997059f, -0.99995764f, -0.99994235f,
    -0.99992470f, -0.99990470f, -0.99988235f, -0.99985764f, -0.99983058f, -0.99980117f, -0.99976941f, -0.99973529f,
    -0.99969882f, -0.99966000f, -0.99961882f, -0.99957530f, -0.99952942f, -0.99948119f, -0.99943060f, -0.99937767f,
    -0.99932238f, -0.99926475f, -0.99920476f, -0.99914242f, -0.99907773f, -0.99901069f, -0.99894129f, -0.99886955f,
    -0.99879546f, -0.99871901f, -0.99864022f, -0.99855907f, -0.99847558f, -0.99838974f, -0.99830154f, -0.99821100f,
    -0.99811811f, -0.99802287f, -0.99792529f, -0.99782535f, -0.99772307f, -0.99761844f, -0.99751146f, -0.99740213f,
    -0.99729046f, -0.99717644f, -0.99706007f, -0.99694136f, -0.99682030f, -0.99669690f, -0.99657115f, -0.99644305f,
    -0.99631261f, -0.99617983f, -0.99604470f, -0.99590723f, -0.99576741f, -0.99562526f, -0.99548076f, -0.99533391f,
    -0.99518473f, -0.99503320f, -0.99487933f, -0.99472312f, -0.99456457f, -0.99440368f, -0.99424045f, -0.99407488f,
    -0.99390697f, -0.99373672f, -0.99356414f, -0.99338921f, -0.99321195f, -0.99303235f, -0.99285041f, -0.99266614f,
    -0.99247953f, -0.99229059f, -0.99209931f, -0.99190570f, -0.99170975f, -0.99151147f, -0.99131086f, -0.99110791f,
    -0.99090264f, -0.99069503f, -0.99048508f, -0.99027281f, -0.99005821f, -0.98984128f, -0.98962202f, -0.98940043f,
    -0.98917651f, -0.98895026f, -0.98872169f, -0.98849079f, -0.98825757f, -0.98802202f, -0.98778414f, -0.98754394f,
    -0.98730142f, -0.98705657f, -0.98680940f, -0.98655991f, -0.98630810f, -0.98605396f, -0.98579751f, -0.98553874f,
    -0.98527764f, -0.98501423f, -0.98474850f, -0.98448046f, -0.98421009f, -0.98393741f, -0.98366242f, -0.98338511f,
    -0.98310549f, -0.98282355f, -0.98253930f, -0.98225274f, -0.98196387f, -0.98167269f, -0.98137919f, -0.98108339f,
    -0.98078528f, -0.98048486f, -0.98018214f, -0.97987710f, -0.97956977f, -0.97926012f, -0.97894818f, -0.97863392f,
    -0.97831737f, -0.97799851f, -0.97767736f, -0.97735390f, -0.97702814f, -0.97670009f, -0.97636973f, -0.97603708f,
    -0.97570213f, -0.97536489f, -0.97502535f, -0.97468351f, -0.97433938f, -0.97399296f, -0.97364425f, -0.97329325f,
    -0.97293995f, -0.97258437f, -0.97222650f, -0.97186634f, -0.97150389f, -0.97113916f, -0.97077214f, -0.97040284f,
    -0.97003125f, -0.96965739f, -0.96928124f, -0.96890280f, -0.96852209f, -0.96813910f, -0.96775384f, -0.96736629f,
    -0.96697647f, -0.96658437f, -0.96619000f, -0.96579336f, -0.96539444f, -0.96499325f, -0.96458979f, -0.96418406f,
    -0.96377607f, -0.96336580f, -0.96295327f, -0.96253847f, -0.96212140f, -0.96170208f, -0.96128049f, -0.96085663f,
    -0.96043052f, -0.96000215f, -0.95957151f, -0.95913862f, -0.95870347f, -0.95826607f, -0.95782641f, -0.95738450f,
    -0.95694034f, -0.95649392f, -0.95604525f, -0.95559433f, -0.95514117f, -0.95468575f, -0.95422810f, -0.95376819f,
    -0.95330604f, -0.95284165f, -0.95237501f, -0.95190614f, -0.95143502f, -0.95096167f, -0.95048607f, -0.95000825f,
    -0.94952818f, -0.94904588f, -0.94856135f, -0.94807459f, -0.94758559f, -0.94709437f, -0.94660091f, -0.94610523f,
    -0.94560733f, -0.94510719f, -0.94460484f, -0.94410026f, -0.94359346f, -0.94308444f, -0.94257320f, -0.94205974f,
    -0.94154407f, -0.94102618f, -0.94050607f, -0.93998375f, -0.93945922f, -0.93893248f, -0.93840353f, -0.93787238f,
    -0.93733901f, -0.93680344f, -0.93626567f, -0.93572569f, -0.93518351f, -0.93463913f, -0.93409255f, -0.93354377f,
    -0.93299280f, -0.93243963f, -0.93188427f, -0.93132671f, -0.93076696f, -0.93020502f, -0.92964090f, -0.92907458f,
    -0.92850608f, -0.92793539f, -0.92736253f, -0.92678747f, -0.92621024f, -0.92563083f, -0.92504924f, -0.92446547f,
    -0.92387953f, -0.92329142f, -0.92270113f, -0.92210867f, -0.92151404f, -0.92091724f, -0.92031828f, -0.91971715f,
    -0.91911385f, -0.91850839f, -0.91790078f, -0.91729100f, -0.91667906f, -0.91606497f, -0.91544872f, -0.91483031f,
    -0.91420976f, -0.91358705f, -0.91296219f, -0.91233518f, -0.91170603f, -0.91107473f, -0.91044129f, -0.90980571f,
    -0.90916798f, -0.90852812f, -0.90788612f, -0.90724198f, -0.90659570f, -0.90594730f, -0.90529676f, -0.90464409f,
    -0.90398929f, -0.90333237f, -0.90267332f, -0.90201214f, -0.90134885f, -0.90068343f, -0.90001589f, -0.89934624f,
    -0.89867447f, -0.89800058f, -0.89732458f, -0.89664647f, -0.89596625f, -0.89528392f, -0.89459949f, -0.89391295f,
    -0.89322430f, -0.89253356f, -0.89184071f, -0.89114576f, -0.89044872f, -0.88974959f, -0.88904836f, -0.88834503f,
    -0.88763962f, -0.88693212f, -0.88622253f, -0.88551086f, -0.88479710f, -0.88408126f, -0.88336334f, -0.88264334f,
    -0.88192126f, -0.88119711f, -0.88047089f, -0.87974259f, -0.87901223f, -0.87827979f, -0.87754529f, -0.87680872f,
    -0.87607009f, -0.87532940f, -0.87458665f, -0.87384184f, -0.87309498f, -0.87234606f, -0.87159509f, -0.87084206f,
    -0.87008699f, -0.86932987f, -0.86857071f, -0.86780950f, -0.86704625f, -0.86628095f, -0.86551362f, -0.86474426f,
    -0.86397286f, -0.86319942f, -0.86242396f, -0.86164646f, -0.86086694f, -0.86008539f, -0.85930182f, -0.85851622f,
    -0.85772861f, -0.85693898f, -0.85614733f, -0.85535366f, -0.85455799f, -0.85376030f, -0.85296060f, -0.85215890f,
    -0.85135519f, -0.85054948f, -0.84974177f, -0.84893206f, -0.84812034f, -0.84730664f, -0.84649094f, -0.84567325f,
    -0.84485357f, -0.84403190f, -0.84320824f, -0.84238260f, -0.84155498f, -0.84072537f, -0.83989379f, -0.83906024f,
    -0.83822471f, -0.83738720f, -0.83654773f, -0.83570628f, -0.83486287f, -0.83401750f, -0.83317016f, -0.83232087f,
    -0.83146961f, -0.83061640f, -0.82976123f, -0.82890411f, -0.82804505f, -0.82718403f, -0.82632106f, -0.82545615f,
    -0.82458930f, -0.82372051f, -0.82284978f, -0.82197712f, -0.82110251f, -0.82022598f, -0.81934752f, -0.81846713f,
    -0.81758481f, -0.81670057f, -0.81581441f, -0.81492633f, -0.81403633f, -0.81314441f, -0.81225059f, -0.81135485f,
    -0.81045720f, -0.80955764f, -0.80865618f, -0.80775282f, -0.80684755f, -0.80594039f, -0.80503133f, -0.80412038f,
    -0.80320753f, -0.80229280f, -0.80137617f, -0.80045766f, -0.79953727f, -0.79861499f, -0.79769084f, -0.79676481f,
    -0.79583690f, -0.79490713f, -0.79397548f, -0.79304196f, -0.79210658f, -0.79116933f, -0.79023022f, -0.78928925f,
    -0.78834643f, -0.78740175f, -0.78645521f, -0.78550683f, -0.78455660f, -0.78360452f, -0.78265060f, -0.78169483f,
    -0.78073723f, -0.77977779f, -0.77881651f, -0.77785340f, -0.77688847f, -0.77592170f, -0.77495311f, -0.77398269f,
    -0.77301045f, -0.77203640f, -0.77106052f, -0.77008284f, -0.76910334f, -0.76812203f, -0.76713891f, -0.76615399f,
    -0.76516727f, -0.76417874f, -0.76318842f, -0.76219630f, -0.76120239f, -0.76020668f, -0.75920919f, -0.75820991f,
    -0.75720885f, -0.75620600f, -0.75520138f, -0.75419498f, -0.75318680f, -0.75217685f, -0.75116513f, -0.75015165f,
    -0.74913639f, -0.74811938f, -0.74710061f, -0.74608007f, -0.74505779f, -0.74403374f, -0.74300795f, -0.74198041f,
    -0.74095113f, -0.73992010f, -0.73888732f, -0.73785281f, -0.73681657f, -0.73577859f, -0.73473888f, -0.73369744f,
    -0.73265427f, -0.73160938f, -0.73056277f, -0.72951444f, -0.72846439f, -0.72741263f, -0.72635916f, -0.72530397f,
    -0.72424708f, -0.72318849f, -0.72212819f, -0.72106620f, -0.72000251f, -0.71893712f, -0.71787005f, -0.71680128f,
    -0.71573083f, -0.71465869f, -0.71358487f, -0.71250937f, -0.71143220f, -0.71035335f, -0.70927283f, -0.70819064f,
    -0.70710678f, -0.70602126f, -0.70493408f, -0.70384524f, -0.70275474f, -0.70166259f, -0.70056879f, -0.69947334f,
    -0.69837625f, -0.69727751f, -0.69617713f, -0.69507511f, -0.69397146f, -0.69286617f, -0.69175926f, -0.69065071f,
    -0.68954054f, -0.68842875f, -0.68731534f, -0.68620031f, -0.68508367f, -0.68396541f, -0.68284555f, -0.68172407f,
    -0.68060100f, -0.67947632f, -0.67835004f, -0.67722217f, -0.67609270f, -0.67496165f, -0.67382900f, -0.67269477f,
    -0.67155895f, -0.67042156f, -0.66928259f, -0.66814204f, -0.66699992f, -0.66585623f, -0.66471098f, -0.66356416f,
    -0.66241578f, -0.66126584f, -0.66011434f, -0.65896129f, -0.65780669f, -0.65665055f, -0.65549285f, -0.65433362f,
    -0.65317284f, -0.65201053f, -0.65084668f, -0.64968131f, -0.64851440f, -0.64734597f, -0.64617601f, -0.64500454f,
    -0.64383154f, -0.64265703f, -0.64148101f, -0.64030348f, -0.63912444f, -0.63794390f, -0.63676186f, -0.63557832f,
    -0.63439328f, -0.63320676f, -0.63201874f, -0.63082923f, -0.62963824f, -0.62844577f, -0.62725182f, -0.62605639f,
    -0.62485949f, -0.62366112f, -0.62246128f, -0.62125998f, -0.62005721f, -0.61885299f, -0.61764731f, -0.61644017f,
    -0.61523159f, -0.61402156f, -0.61281008f, -0.61159716f, -0.61038281f, -0.60916701f, -0.60794978f, -0.60673113f,
    -0.60551104f, -0.60428953f, -0.60306660f, -0.60184225f, -0.60061648f, -0.59938930f, -0.59816071f, -0.59693071f,
    -0.59569930f, -0.59446650f, -0.59323230f, -0.59199669f, -0.59075970f, -0.58952132f, -0.58828155f, -0.58704039f,
    -0.58579786f, -0.58455394f, -0.58330865f, -0.58206199f, -0.58081396f, -0.57956456f, -0.57831380f, -0.57706167f,
    -0.57580819f, -0.57455336f, -0.57329717f, -0.57203963f, -0.57078075f, -0.56952052f, -0.56825895f, -0.56699605f,
    -0.56573181f, -0.56446624f, -0.56319934f, -0.56193112f, -0.56066158f, -0.55939071f, -0.55811853f, -0.55684504f,
    -0.55557023f, -0.55429412f, -0.55301671f, -0.55173799f, -0.55045797f, -0.54917666f, -0.54789406f, -0.54661017f,
    -0.54532499f, -0.54403853f, -0.54275078f, -0.54146177f, -0.54017147f, -0.53887991f, -0.53758708f, -0.53629298f,
    -0.53499762f, -0.53370100f, -0.53240313f, -0.53110400f, -0.52980362f, -0.52850200f, -0.52719913f, -0.52589503f,
    -0.52458968f, -0.52328310f, -0.52197529f, -0.52066625f, -0.51935599f, -0.51804450f, -0.51673180f, -0.51541788f,
    -0.51410274f, -0.51278640f, -0.51146885f, -0.51015010f, -0.50883014f, -0.50750899f, -0.50618665f, -0.50486311f,
    -0.50353838f, -0.50221247f, -0.50088538f, -0.49955711f, -0.49822767f, -0.49689705f, -0.49556526f, -0.49423231f,
    -0.49289819f, -0.49156292f, -0.49022648f, -0.48888890f, -0.48755016f, -0.48621028f, -0.48486925f, -0.48352708f,
    -0.48218377f, -0.48083933f, -0.47949376f, -0.47814706f, -0.47679923f, -0.47545028f, -0.47410021f, -0.47274903f,
    -0.47139674f, -0.47004333f, -0.46868882f, -0.46733321f, -0.46597650f, -0.46461869f, -0.46325978f, -0.46189979f,
    -0.46053871f, -0.45917655f, -0.45781330f, -0.45644898f, -0.45508359f, -0.45371712f, -0.45234959f, -0.45098099f,
    -0.44961133f, -0.44824061f, -0.44686884f, -0.44549602f, -0.44412214f, -0.44274723f, -0.44137127f, -0.43999427f,
    -0.43861624f, -0.43723717f, -0.43585708f, -0.43447596f, -0.43309382f, -0.43171066f, -0.43032648f, -0.42894129f,
    -0.42755509f, -0.42616789f, -0.42477968f, -0.42339047f, -0.42200027f, -0.42060907f, -0.41921689f, -0.41782372f,
    -0.41642956f, -0.41503442f, -0.41363831f, -0.41224123f, -0.41084317f, -0.40944415f, -0.40804416f, -0.40664322f,
    -0.40524131f, -0.40383846f, -0.40243465f, -0.40102990f, -0.39962420f, -0.39821756f, -0.39680999f, -0.39540148f,
    -0.39399204f, -0.39258167f, -0.39117038f, -0.38975817f, -0.38834505f, -0.38693101f, -0.38551605f, -0.38410020f,
    -0.38268343f, -0.38126577f, -0.37984721f, -0.37842775f, -0.37700741f, -0.37558618f, -0.37416406f, -0.37274107f,
    -0.37131719f, -0.36989245f, -0.36846683f, -0.36704035f, -0.36561300f, -0.36418479f, -0.36275572f, -0.36132581f,
    -0.35989504f, -0.35846342f, -0.35703096f, -0.35559766f, -0.35416353f, -0.35272856f, -0.35129276f, -0.34985613f,
    -0.34841868f, -0.34698041f, -0.34554132f, -0.34410143f, -0.34266072f, -0.34121920f, -0.33977688f, -0.33833377f,
    -0.33688985f, -0.33544515f, -0.33399965f, -0.33255337f, -0.33110631f, -0.32965846f, -0.32820984f, -0.32676045f,
    -0.32531029f, -0.32385937f, -0.32240768f, -0.32095523f, -0.31950203f, -0.31804808f, -0.31659338f, -0.31513793f,
    -0.31368174f, -0.31222481f, -0.31076715f, -0.30930876f, -0.30784964f, -0.30638980f, -0.30492923f, -0.30346795f,
    -0.30200595f, -0.30054324f, -0.29907983f, -0.29761571f, -0.29615089f, -0.29468537f, -0.29321916f, -0.29175226f,
    -0.29028468f, -0.28881641f, -0.28734746f, -0.28587783f, -0.28440754f, -0.28293657f, -0.28146494f, -0.27999264f,
    -0.27851969f, -0.27704608f, -0.27557182f, -0.27409691f, -0.27262136f, -0.27114516f, -0.26966833f, -0.26819086f,
    -0.26671276f, -0.26523403f, -0.26375468f, -0.26227471f, -0.26079412f, -0.25931292f, -0.25783110f, -0.25634868f,
    -0.25486566f, -0.25338204f, -0.25189782f, -0.25041301f, -0.24892761f, -0.24744162f, -0.24595505f, -0.24446790f,
    -0.24298018f, -0.24149189f, -0.24000302f, -0.23851359f, -0.23702361f, -0.23553306f, -0.23404196f, -0.23255031f,
    -0.23105811f, -0.22956537f, -0.22807208f, -0.22657826f, -0.22508391f, -0.22358903f, -0.22209362f, -0.22059769f,
    -0.21910124f, -0.21760427f, -0.21610680f, -0.21460881f, -0.21311032f, -0.21161133f, -0.21011184f, -0.20861185f,
    -0.20711138f, -0.20561041f, -0.20410897f, -0.20260704f, -0.20110463f, -0.19960176f, -0.19809841f, -0.19659460f,
    -0.19509032f, -0.19358559f, -0.19208040f, -0.19057475f, -0.18906866f, -0.18756213f, -0.18605515f, -0.18454774f,
    -0.18303989f, -0.18153161f, -0.18002290f, -0.17851377f, -0.17700422f, -0.17549425f, -0.17398387f, -0.17247308f,
    -0.17096189f, -0.16945029f, -0.16793829f, -0.16642590f, -0.16491312f, -0.16339995f, -0.16188639f, -0.16037246f,
    -0.15885814f, -0.15734346f, -0.15582840f, -0.15431297f, -0.15279719f, -0.15128104f, -0.14976453f, -0.14824768f,
    -0.14673047f, -0.14521292f, -0.14369503f, -0.14217680f, -0.14065824f, -0.13913934f, -0.13762012f, -0.13610058f,
    -0.13458071f, -0.13306053f, -0.13154003f, -0.13001922f, -0.12849811f, -0.12697670f, -0.12545498f, -0.12393298f,
    -0.12241068f, -0.12088809f, -0.11936521f, -0.11784206f, -0.11631863f, -0.11479493f, -0.11327095f, -0.11174671f,
    -0.11022221f, -0.10869744f, -0.10717242f, -0.10564715f, -0.10412163f, -0.10259587f, -0.10106986f, -0.09954362f,
    -0.09801714f, -0.09649043f, -0.09496350f, -0.09343634f, -0.09190896f, -0.09038136f, -0.08885355f, -0.08732554f,
    -0.08579731f, -0.08426889f, -0.08274026f, -0.08121145f, -0.07968244f, -0.07815324f, -0.07662386f, -0.07509430f,
    -0.07356456f, -0.07203465f, -0.07050457f, -0.06897433f, -0.06744392f, -0.06591335f, -0.06438263f, -0.06285176f,
    -0.06132074f, -0.05978957f, -0.05825826f, -0.05672682f, -0.05519524f, -0.05366354f, -0.05213170f, -0.05059975f,
    -0.04906767f, -0.04753548f, -0.04600318f, -0.04447077f, -0.04293826f, -0.04140564f, -0.03987293f, -0.03834012f,
    -0.03680722f, -0.03527424f, -0.03374117f, -0.03220803f, -0.03067480f, -0.02914151f, -0.02760815f, -0.02607472f,
    -0.02454123f, -0.02300768f, -0.02147408f, -0.01994043f, -0.01840673f, -0.01687299f, -0.01533921f, -0.01380539f,
    -0.01227154f, -0.01073766f, -0.00920375f, -0.00766983f, -0.00613588f, -0.00460193f, -0.00306796f, -0.00153398f,
};

/*============================ 内部辅助函数 ============================*/

/**
 * @brief 对增量式更新的角度进行快速标准化
 * @param angle 输入角度（弧度）
 * @return 标准化后的角度
 */
static inline float normalize_angle(float angle) {

    if (angle >= TWO_PI) {
        angle -= TWO_PI;
    } 
    else if (angle < 0.0f) {
        angle += TWO_PI;
    }
    return angle;
}

/**
 * @brief 线性插值函数
 * @param val1 第一个值
 * @param val2 第二个值
 * @param fraction 插值权重 [0.0, 1.0]
 * @return 插值结果
 */
static inline float linear_interpolate(float val1, float val2, float fraction) {
    return val1 + fraction * (val2 - val1);
}

/*============================ 公共API函数 ============================*/

/**
 * @brief Fast sine calculation using lookup table with linear interpolation
 */
float fast_sin(float angle) {
    // 标准化角度到 [0, 2π) 范围
    angle = normalize_angle(angle);

    // 将角度转换为浮点型表格索引
    float index_float = angle * TABLE_SCALE;

    // 分离整数部分和小数部分
    uint32_t index_int = (uint32_t)index_float;
    float fraction = index_float - (float)index_int;

    // 确保索引在有效范围内
    index_int &= SIN_TABLE_MASK;
    uint32_t next_index = (index_int + 1) & SIN_TABLE_MASK;

    // 获取相邻的两个正弦值
    float val1 = g_sin_table[index_int];
    float val2 = g_sin_table[next_index];

    // 线性插值
    return linear_interpolate(val1, val2, fraction);
}

/**
 * @brief Fast cosine calculation using lookup table with linear interpolation
 */
float fast_cos(float angle) {
    // cos(x) = sin(x + π/2)
    // 在查找表中，π/2 对应 SIN_TABLE_COS_OFFSET 个索引
    return fast_sin(angle + (TWO_PI * SIN_TABLE_COS_OFFSET / SIN_TABLE_SIZE));
}

/**
 * @brief Fast combined sine and cosine calculation
 */
void fast_sincos(float angle, float* sin_val, float* cos_val) {
    // 参数检查
    if (sin_val == NULL || cos_val == NULL) {
        return;
    }

    // 标准化角度到 [0, 2π) 范围
    angle = normalize_angle(angle);

    // 将角度转换为浮点型表格索引
    float index_float = angle * TABLE_SCALE;

    // 分离整数部分和小数部分
    uint32_t index_int = (uint32_t)index_float;
    float fraction = index_float - (float)index_int;

    // 确保索引在有效范围内
    index_int &= SIN_TABLE_MASK;
    uint32_t next_index = (index_int + 1) & SIN_TABLE_MASK;

    // 计算sin值
    float sin_val1 = g_sin_table[index_int];
    float sin_val2 = g_sin_table[next_index];
    *sin_val = linear_interpolate(sin_val1, sin_val2, fraction);

    // 计算cos值 - 使用相位偏移
    uint32_t cos_index = (index_int + SIN_TABLE_COS_OFFSET) & SIN_TABLE_MASK;
    uint32_t cos_next_index = (cos_index + 1) & SIN_TABLE_MASK;

    float cos_val1 = g_sin_table[cos_index];
    float cos_val2 = g_sin_table[cos_next_index];
    *cos_val = linear_interpolate(cos_val1, cos_val2, fraction);
}

/*============================ 兼容性函数 ============================*/
/*
 * 这些函数提供与旧版encoder_sin_lookup模块的兼容性
 * 支持直接使用编码器计数值进行查表
 */

/**
 * @brief 兼容性函数：从编码器值获取sin值
 * @param encoder_value 编码器计数值 (0 到 SIN_TABLE_SIZE-1)
 * @return 对应的sin值
 */
float get_sin_from_encoder(uint16_t encoder_value) {
    // 确保索引在有效范围内
    uint32_t index = encoder_value & SIN_TABLE_MASK;
    return g_sin_table[index];
}

/**
 * @brief 兼容性函数：从编码器值获取cos值
 * @param encoder_value 编码器计数值 (0 到 SIN_TABLE_SIZE-1)
 * @return 对应的cos值
 */
float get_cos_from_encoder(uint16_t encoder_value) {
    // cos(x) = sin(x + π/2)，在查找表中对应偏移 SIN_TABLE_COS_OFFSET
    uint32_t cos_index = (encoder_value + SIN_TABLE_COS_OFFSET) & SIN_TABLE_MASK;
    return g_sin_table[cos_index];
}
