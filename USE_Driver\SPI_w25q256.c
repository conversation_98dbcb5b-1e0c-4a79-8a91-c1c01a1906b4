#include "SPI_w25q256.h"

/* 定义一个扇区大小的缓冲区,暂存数据 */
uint8_t spiflash_sector_buf[SPIF_SECTOR_SIZE];

/**
  * @brief  SPI Flash初始化配置
  */
void spiflash_init(void)
{

    gpio_init_type gpio_init_struct;
    spi_init_type spi_init_struct;

    gpio_default_para_init(&gpio_init_struct);
    spi_default_para_init(&spi_init_struct);

    /* configure the SCK pin */
    gpio_pin_mux_config(GPIOC, GPIO_PINS_SOURCE10, GPIO_MUX_6);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_10;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOC, &gpio_init_struct);

    /* configure the MISO pin */
    gpio_pin_mux_config(GPIOC, GPIO_PINS_SOURCE11, GPIO_MUX_6);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_11;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOC, &gpio_init_struct);

    /* configure the MOSI pin */
    gpio_pin_mux_config(GPIOC, GPIO_PINS_SOURCE12, GPIO_MUX_6);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_12;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOC, &gpio_init_struct);

    /* configure param */
    spi_init_struct.transmission_mode = SPI_TRANSMIT_FULL_DUPLEX;
    spi_init_struct.master_slave_mode = SPI_MODE_MASTER;
    spi_init_struct.frame_bit_num = SPI_FRAME_8BIT;
    spi_init_struct.first_bit_transmission = SPI_FIRST_BIT_MSB;
    spi_init_struct.mclk_freq_division = SPI_MCLK_DIV_3;
    spi_init_struct.clock_polarity = SPI_CLOCK_POLARITY_HIGH;
    spi_init_struct.clock_phase = SPI_CLOCK_PHASE_2EDGE;
    spi_init_struct.cs_mode_selection = SPI_CS_SOFTWARE_MODE;
    spi_init(SPI3, &spi_init_struct);

    spi_enable(SPI3, TRUE);

    // 检查并设置4字节地址模式
    if(!spiflash_check_4byte_mode()) {
        // 如果不是4字节地址模式，则使能4字节地址模式
        spiflash_enable_4byte_addr();
        
        // 再次检查确认
        if(!spiflash_check_4byte_mode()) {
            // 设置失败
        }
    }
}

/**
  * @brief  向Flash写入数据
  * @note   支持跨页写入,自动处理页对齐
  * @param  pbuffer: 要写入的数据缓冲区指针
  * @param  write_addr: 写入的目标地址
  * @param  length: 要写入的数据长度
  * @retval none
  */
void spiflash_write(uint8_t *pbuffer, uint32_t write_addr, uint32_t length)
{
  uint32_t sector_pos;      // 扇区地址
  uint16_t sector_offset;   // 扇区内偏移地址
  uint16_t sector_remain;   // 扇区剩余空间大小
  uint16_t index;           // 循环计数器
  uint8_t *spiflash_buf;    // 指向扇区缓冲区的指针
  spiflash_buf = spiflash_sector_buf;

  /* 计算扇区地址 */
  sector_pos = write_addr / SPIF_SECTOR_SIZE;

  /* 计算扇区内偏移地址 */
  sector_offset = write_addr % SPIF_SECTOR_SIZE;

  /* 计算扇区剩余空间大小 */
  sector_remain = SPIF_SECTOR_SIZE - sector_offset;
  if(length <= sector_remain)
  {
    /* 如果写入长度小于扇区剩余空间,则直接写入 */
    sector_remain = length;
  }
  while(1)
  {
    /* 读取整个扇区数据到缓冲区 */
    spiflash_read(spiflash_buf, sector_pos * SPIF_SECTOR_SIZE, SPIF_SECTOR_SIZE);

    /* 检查要写入区域是否需要擦除 */
    for(index = 0; index < sector_remain; index++)
    {
      if(spiflash_buf[sector_offset + index] != 0xFF)
      {
        /* 如果有非0xFF的数据,说明需要擦除 */
        break;
      }
    }
    if(index < sector_remain)
    {
      /* 擦除扇区 */
      spiflash_sector_erase(sector_pos);

      /* 将新数据拷贝到扇区缓冲区 */
      for(index = 0; index < sector_remain; index++)
      {
        spiflash_buf[index + sector_offset] = pbuffer[index];
      }
      /* 将整个扇区写回Flash */
      spiflash_write_nocheck(spiflash_buf, sector_pos * SPIF_SECTOR_SIZE, SPIF_SECTOR_SIZE);
    }
    else
    {
      /* 无需擦除,直接写入数据 */
      spiflash_write_nocheck(pbuffer, write_addr, sector_remain);
    }
    if(length == sector_remain)
    {
      /* 写入完成,退出循环 */
      break;
    }
    else
    {
      /* 继续写入剩余数据 */
      sector_pos++;        // 下一个扇区
      sector_offset = 0;   // 偏移地址归零

      pbuffer += sector_remain;         // 数据指针偏移
      write_addr += sector_remain;      // 写入地址偏移
      length -= sector_remain;          // 剩余长度递减
      if(length > SPIF_SECTOR_SIZE)
      {
        /* 剩余数据大于一个扇区 */
        sector_remain = SPIF_SECTOR_SIZE;
      }
      else
      {
        /* 剩余数据小于一个扇区 */
        sector_remain = length;
      }
    }
  }
}

/**
  * @brief  从Flash读取数据
  * @note   支持任意地址读取任意长度数据，使用4字节地址模式
  * @param  pbuffer: 读取数据存放的缓冲区指针
  * @param  read_addr: 读取的起始地址
  * @param  length: 要读取的数据长度
  * @retval none
  */
void spiflash_read(uint8_t *pbuffer, uint32_t read_addr, uint32_t length)
{
    FLASH_CS_LOW();                                    // 片选使能
    spi_byte_write(SPIF_READDATA);                     // 发送读命令
    spi_byte_write((uint8_t)((read_addr) >> 24));      // 发送32位地址
    spi_byte_write((uint8_t)((read_addr) >> 16));      
    spi_byte_write((uint8_t)((read_addr) >> 8));
    spi_byte_write((uint8_t)read_addr);
    spi_bytes_read(pbuffer, length);                    // 读取数据
    FLASH_CS_HIGH();                                    // 片选禁用
}

/**
  * @brief  擦除指定扇区
  * @note   将整个扇区数据擦除为0xFF，使用4字节地址模式
  * @param  erase_addr: 要擦除的扇区地址
  * @retval none
  */
void spiflash_sector_erase(uint32_t erase_addr)
{
    erase_addr *= SPIF_SECTOR_SIZE;                    // 将扇区号转换为字节地址
    spiflash_write_enable();                           // 使能写操作
    spiflash_wait_busy();                              // 等待空闲
    
    FLASH_CS_LOW();                                    // 片选使能
    spi_byte_write(SPIF_SECTORERASE);                 // 发送扇区擦除命令
    spi_byte_write((uint8_t)((erase_addr) >> 24));    // 发送32位地址
    spi_byte_write((uint8_t)((erase_addr) >> 16));
    spi_byte_write((uint8_t)((erase_addr) >> 8));
    spi_byte_write((uint8_t)erase_addr);
    FLASH_CS_HIGH();                                   // 片选禁用
    
    //spiflash_wait_busy();                             // 等待擦除完成
}

/**
  * @brief  写入数据(无需检查擦除)
  * @note   写入前需确保目标区域已被擦除
  * @param  pbuffer: 要写入的数据缓冲区指针
  * @param  write_addr: 写入的目标地址
  * @param  length: 要写入的数据长度
  * @retval none
  */
void spiflash_write_nocheck(uint8_t *pbuffer, uint32_t write_addr, uint32_t length)
{
  uint16_t page_remain;    // 页内剩余空间

  /* 计算页内剩余空间 */
  page_remain = SPIF_PAGE_SIZE - write_addr % SPIF_PAGE_SIZE;
  if(length <= page_remain)
  {
    /* 不跨页 */
    page_remain = length;
  }
  while(1)
  {
    /* 写入一页数据 */
    spiflash_page_write(pbuffer, write_addr, page_remain);
    if(length == page_remain)
    {
      /* 写入完成 */
      break;
    }
    else
    {
      /* 继续写入剩余数据 */
      pbuffer += page_remain;
      write_addr += page_remain;
      length -= page_remain;
      if(length > SPIF_PAGE_SIZE)
      {
        /* 剩余数据大于一页 */
        page_remain = SPIF_PAGE_SIZE;
      }
      else
      {
        /* 剩余数据小于一页 */
        page_remain = length;
      }
    }
  }
}

/**
  * @brief  页编程(写入一页数据)
  * @note   每次最多写入一页(256字节)，使用4字节地址模式
  * @param  pbuffer: 要写入的数据缓冲区指针
  * @param  write_addr: 写入的目标地址
  * @param  length: 要写入的数据长度
  * @retval none
  */
void spiflash_page_write(uint8_t *pbuffer, uint32_t write_addr, uint32_t length)
{
    spiflash_write_enable();                            // 写使能
    
    FLASH_CS_LOW();                                     // 片选使能
    spi_byte_write(SPIF_PAGEPROGRAM);                  // 发送页编程命令
    spi_byte_write((uint8_t)((write_addr) >> 24));     // 发送32位地址
    spi_byte_write((uint8_t)((write_addr) >> 16));
    spi_byte_write((uint8_t)((write_addr) >> 8));
    spi_byte_write((uint8_t)write_addr);
    spi_bytes_write(pbuffer, length);                   // 写入数据
    FLASH_CS_HIGH();                                    // 片选禁用
    
    //spiflash_wait_busy();                              // 等待写入完成
}

/**
  * @brief  连续写入数据
  * @note   通过SPI接口连续写入多个字节
  * @param  pbuffer: 要写入的数据缓冲区指针
  * @param  length: 要写入的数据长度
  * @retval none
  */
void spi_bytes_write(uint8_t *pbuffer, uint32_t length)
{
  volatile uint8_t dummy_data;   // 用于接收无用数据的变量

#if defined(SPI_TRANS_DMA)
  /* 使用DMA方式传输 */
  dma_init_type dma_init_struct;
  
  /* 配置DMA接收通道 */
  dma_reset(DMA1_CHANNEL2);                                    // 复位DMA通道2
  dma_reset(DMA1_CHANNEL3);                                    // 复位DMA通道3
  dma_default_para_init(&dma_init_struct);                     // 初始化DMA参数结构体为默认值
  dma_init_struct.buffer_size = length;                        // 设置传输数据长度
  dma_init_struct.direction = DMA_DIR_PERIPHERAL_TO_MEMORY;    // 设置传输方向为外设到内存
  dma_init_struct.memory_base_addr = (uint32_t)&dummy_data;    // 设置内存地址为dummy_data变量地址
  dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;  // 设置内存数据宽度为字节
  dma_init_struct.memory_inc_enable = FALSE;                   // 禁止内存地址自增
  dma_init_struct.peripheral_base_addr = (uint32_t)(&SPI3->dt);    // 设置外设地址为SPI3数据寄存器
  dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;  // 设置外设数据宽度为字节
  dma_init_struct.peripheral_inc_enable = FALSE;               // 禁止外设地址自增
  dma_init_struct.priority = DMA_PRIORITY_VERY_HIGH;           // 设置通道优先级为最高
  dma_init_struct.loop_mode_enable = FALSE;                    // 禁止循环模式
  dma_init(DMA1_CHANNEL2, &dma_init_struct);                   // 初始化DMA通道2
  dmamux_enable(DMA1, TRUE);                                   // 使能DMA1复用器
  dmamux_init(DMA1MUX_CHANNEL2, DMAMUX_DMAREQ_ID_SPI3_RX);     // 配置DMA通道2复用为SPI3接收请求

  /* 配置DMA发送通道 */
  dma_init_struct.buffer_size = length;                        // 设置传输数据长度
  dma_init_struct.direction = DMA_DIR_MEMORY_TO_PERIPHERAL;    // 设置传输方向为内存到外设
  dma_init_struct.memory_base_addr = (uint32_t)pbuffer;        // 设置内存地址为数据缓冲区地址
  dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;  // 设置内存数据宽度为字节
  dma_init_struct.memory_inc_enable = TRUE;                    // 使能内存地址自增
  dma_init_struct.peripheral_base_addr = (uint32_t)(&SPI3->dt);    // 设置外设地址为SPI3数据寄存器
  dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;  // 设置外设数据宽度为字节
  dma_init_struct.peripheral_inc_enable = FALSE;               // 禁止外设地址自增
  dma_init_struct.priority = DMA_PRIORITY_VERY_HIGH;           // 设置通道优先级为最高
  dma_init_struct.loop_mode_enable = FALSE;                    // 禁止循环模式
  dma_init(DMA1_CHANNEL3, &dma_init_struct);                  // 初始化DMA通道3
  dmamux_init(DMA1MUX_CHANNEL3, DMAMUX_DMAREQ_ID_SPI3_TX);    // 配置DMA通道3复用为SPI3发送请求

  /* 使能SPI的DMA请求 */
  spi_i2s_dma_transmitter_enable(SPI3, TRUE);
  spi_i2s_dma_receiver_enable(SPI3, TRUE);

  /* 使能DMA通道 */
  dma_channel_enable(DMA1_CHANNEL2, TRUE);
  dma_channel_enable(DMA1_CHANNEL3, TRUE);

  /* 等待传输完成 */
  while(dma_flag_get(DMA1_FDT2_FLAG) == RESET);
  dma_flag_clear(DMA1_FDT2_FLAG);

  /* 关闭DMA通道 */
  dma_channel_enable(DMA1_CHANNEL2, FALSE);
  dma_channel_enable(DMA1_CHANNEL3, FALSE);

  /* 关闭SPI的DMA请求 */
  spi_i2s_dma_transmitter_enable(SPI3, FALSE);
  spi_i2s_dma_receiver_enable(SPI3, FALSE);
#else
  /* 使用轮询方式传输 */
  while(length--)
  {
    while(spi_i2s_flag_get(SPI3, SPI_I2S_TDBE_FLAG) == RESET);
    spi_i2s_data_transmit(SPI3, *pbuffer);
    while(spi_i2s_flag_get(SPI3, SPI_I2S_RDBF_FLAG) == RESET);
    dummy_data = spi_i2s_data_receive(SPI3);
    pbuffer++;
  }
#endif
}

/**
  * @brief  连续读取数据
  * @note   通过SPI接口连续读取多个字节
  * @param  pbuffer: 读取数据存放的缓冲区指针
  * @param  length: 要读取的数据长度
  * @retval none
  */
void spi_bytes_read(uint8_t *pbuffer, uint32_t length)
{
  uint8_t write_value = FLASH_SPI_DUMMY_BYTE;  // 发送的空字节

#if defined(SPI_TRANS_DMA)
  /* 使用DMA方式传输 */
  dma_init_type dma_init_struct;
  
  /* 配置DMA发送通道 */
  dma_reset(DMA1_CHANNEL2);
  dma_reset(DMA1_CHANNEL3);
  dma_default_para_init(&dma_init_struct);
  dma_init_struct.buffer_size = length;
  dma_init_struct.direction = DMA_DIR_MEMORY_TO_PERIPHERAL;
  dma_init_struct.memory_base_addr = (uint32_t)&write_value;
  dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;
  dma_init_struct.memory_inc_enable = FALSE;
  dma_init_struct.peripheral_base_addr = (uint32_t)(&SPI3->dt);
  dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;
  dma_init_struct.peripheral_inc_enable = FALSE;
  dma_init_struct.priority = DMA_PRIORITY_VERY_HIGH;
  dma_init_struct.loop_mode_enable = FALSE;
  dma_init(DMA1_CHANNEL3, &dma_init_struct);
  dmamux_enable(DMA1, TRUE);
  dmamux_init(DMA1MUX_CHANNEL3, DMAMUX_DMAREQ_ID_SPI3_TX);

  /* 配置DMA接收通道 */
  dma_init_struct.buffer_size = length;
  dma_init_struct.direction = DMA_DIR_PERIPHERAL_TO_MEMORY;
  dma_init_struct.memory_base_addr = (uint32_t)pbuffer;
  dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;
  dma_init_struct.memory_inc_enable = TRUE;
  dma_init_struct.peripheral_base_addr = (uint32_t)(&SPI3->dt);
  dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;
  dma_init_struct.peripheral_inc_enable = FALSE;
  dma_init_struct.priority = DMA_PRIORITY_VERY_HIGH;
  dma_init_struct.loop_mode_enable = FALSE;
  dma_init(DMA1_CHANNEL2, &dma_init_struct);
  dmamux_init(DMA1MUX_CHANNEL2, DMAMUX_DMAREQ_ID_SPI3_RX);

  /* 使能SPI的DMA请求 */
  spi_i2s_dma_transmitter_enable(SPI3, TRUE);
  spi_i2s_dma_receiver_enable(SPI3, TRUE);
  
  /* 使能DMA通道 */
  dma_channel_enable(DMA1_CHANNEL2, TRUE);
  dma_channel_enable(DMA1_CHANNEL3, TRUE);

  /* 等待传输完成 */
  while(dma_flag_get(DMA1_FDT2_FLAG) == RESET);
  dma_flag_clear(DMA1_FDT2_FLAG);

  /* 关闭DMA通道 */
  dma_channel_enable(DMA1_CHANNEL2, FALSE);
  dma_channel_enable(DMA1_CHANNEL3, FALSE);

  /* 关闭SPI的DMA请求 */
  spi_i2s_dma_transmitter_enable(SPI3, FALSE);
  spi_i2s_dma_receiver_enable(SPI3, FALSE);
#else
  /* 使用轮询方式传输 */
  while(length--)
  {
    while(spi_i2s_flag_get(SPI3, SPI_I2S_TDBE_FLAG) == RESET);
    spi_i2s_data_transmit(SPI3, write_value);
    while(spi_i2s_flag_get(SPI3, SPI_I2S_RDBF_FLAG) == RESET);
    *pbuffer = spi_i2s_data_receive(SPI3);
    pbuffer++;
  }
#endif
}

/**
  * @brief  等待Flash空闲
  * @note   通过读取状态寄存器判断Flash是否空闲
  * @param  none
  * @retval none
  */
void spiflash_wait_busy(void)
{
  while((spiflash_read_sr1() & 0x01) == 0x01);  // 等待BUSY位清零
}

/**
  * @brief  读取状态寄存器1
  * @note   读取Flash的状态寄存器1的值
  * @param  none
  * @retval 状态寄存器1的值
  */
uint8_t spiflash_read_sr1(void)
{
  uint8_t breadbyte = 0;
  FLASH_CS_LOW();
  spi_byte_write(SPIF_READSTATUSREG1);  // 发送读状态寄存器1命令
  breadbyte = (uint8_t)spi_byte_read();  // 读取状态值
  FLASH_CS_HIGH();
  return (breadbyte);
}

/**
  * @brief  使能写操作
  * @note   在进行写入或擦除操作前需要先使能写操作
  * @param  none
  * @retval none
  */
void spiflash_write_enable(void)
{
  FLASH_CS_LOW();
  spi_byte_write(SPIF_WRITEENABLE);  // 发送写使能命令
  FLASH_CS_HIGH();
}

/**
  * @brief  读取Flash ID
  * @note   读取Flash的制造商ID和设备ID
  * @param  none
  * @retval Flash ID(高8位为制造商ID,低8位为设备ID)
  */
uint16_t spiflash_read_id(void)
{
    uint16_t wreceivedata = 0;
    FLASH_CS_LOW();
    spi_byte_write(SPIF_MANUFACTDEVICEID);  // 发送读ID命令
    spi_byte_write(0x00);                   // 发送32位地址(全0)
    spi_byte_write(0x00);
    spi_byte_write(0x00);
    spi_byte_write(0x00);                   // 新增一个字节的地址
    wreceivedata |= spi_byte_read() << 8;   // 读取制造商ID
    wreceivedata |= spi_byte_read();        // 读取设备ID
    FLASH_CS_HIGH();
    return wreceivedata;
}

/**
  * @brief  SPI发送一个字节
  * @note   通过SPI接口发送一个字节并返回接收到的数据
  * @param  data: 要发送的数据
  * @retval 接收到的数据
  */
uint8_t spi_byte_write(uint8_t data)
{
  uint8_t brxbuff;
  spi_i2s_dma_transmitter_enable(SPI3, FALSE);  // 关闭DMA
  spi_i2s_dma_receiver_enable(SPI3, FALSE);
  spi_i2s_data_transmit(SPI3, data);            // 发送数据
  while(spi_i2s_flag_get(SPI3, SPI_I2S_RDBF_FLAG) == RESET);  // 等待接收完成
  brxbuff = spi_i2s_data_receive(SPI3);         // 读取接收到的数据
  while(spi_i2s_flag_get(SPI3, SPI_I2S_BF_FLAG) != RESET);    // 等待SPI空闲
  return brxbuff;
}

/**
  * @brief  SPI读取一个字节
  * @note   通过发送空字节读取一个字节数据
  * @param  none
  * @retval 读取到的数据
  */
uint8_t spi_byte_read(void)
{
  return (spi_byte_write(FLASH_SPI_DUMMY_BYTE));  // 发送空字节并返回接收到的数据
}

/**
  * @brief  使能4字节地址模式
  * @note   W25Q256需要使能4字节地址模式才能访问完整32MB地址空间
  * @param  none
  * @retval none
  */
void spiflash_enable_4byte_addr(void)
{
    // 等待Flash空闲
    spiflash_wait_busy();
    
    // 使能写操作
    spiflash_write_enable();
    
    // 发送使能4字节地址命令
    FLASH_CS_LOW();
    spi_byte_write(SPIF_ENABLE4BYTEADDR);  // 发送使能4字节地址命令
    FLASH_CS_HIGH();
    
    // 等待命令执行完成
    spiflash_wait_busy();
}

/**
  * @brief  读取状态寄存器3
  * @note   读取Flash的状态寄存器3的值
  * @param  none
  * @retval 状态寄存器3的值
  */
uint8_t spiflash_read_sr3(void)
{
    uint8_t status;
    FLASH_CS_LOW();
    spi_byte_write(SPIF_READSTATUSREG3);  // 发送读状态寄存器3命令
    status = spi_byte_read();             // 读取状态值
    FLASH_CS_HIGH();
    return status;
}

/**
  * @brief  检查4字节地址模式
  * @note   通过读取状态寄存器3的ADS位检查当前地址模式
  * @param  none
  * @retval 0: 3字节地址模式
  *         1: 4字节地址模式
  */
uint8_t spiflash_check_4byte_mode(void)
{
    uint8_t status;
    
    // 读取状态寄存器3
    status = spiflash_read_sr3();
    
    // 检查ADS位
    if(status & SPIF_SR3_ADS) {
        return 1;   // 4字节地址模式
    } else {
        return 0;   // 3字节地址模式
    }
}

