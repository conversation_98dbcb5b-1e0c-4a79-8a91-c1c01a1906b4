#include "ENC_Speed.h"
#include "math.h"
#include <stdlib.h> 

/*============================ 数学常量 ===========================*/
#ifndef PI
#define PI                    3.141592654f           // π常量
#endif
#define TWO_PI                6.283185307f           // 2π常量

/* 全局变量定义 */
EncoderHandler_t g_encoder;
static float speed_buffer[32];  // 滑动平均滤波缓冲区
uint32_t g_last_z_detect_time = 0; // Z相检测时间记录

uint16_t CalculateTimerCountFromSPI(uint16_t resolver_pos);
static float CalculateAngleDifference(float current_angle, float last_angle);
static void ProcessFhan(float target_speed);

/**
 * @brief 初始化卡尔曼滤波器
 * @param q_vel 速度过程噪声
 * @param q_acc 加速度过程噪声
 * @param r 测量噪声
 * @param p0 初始协方差
 */
void InitKalmanFilter(float q_vel, float q_acc, float r, float p0) {
    /* 初始化状态向量和协方差矩阵 */
    g_encoder.kalman.x[0] = 0.0f;    // 初始速度
    g_encoder.kalman.x[1] = 0.0f;    // 初始加速度
    
    g_encoder.kalman.p[0][0] = p0;   // 速度初始协方差
    g_encoder.kalman.p[0][1] = 0.0f;
    g_encoder.kalman.p[1][0] = 0.0f;
    g_encoder.kalman.p[1][1] = p0;   // 加速度初始协方差
    
    /* 设置噪声参数 */
    g_encoder.kalman.q[0] = q_vel;   // 速度过程噪声
    g_encoder.kalman.q[1] = q_acc;   // 加速度过程噪声
    g_encoder.kalman.r = r;          // 测量噪声
    
    /* 预计算噪声参数相关常量 */
    g_encoder.kalman.inv_r = (r > 0.0001f) ? (1.0f / r) : 1000.0f;
    
    /* 计算固定时间间隔相关常量 */
    g_encoder.kalman.dt = SPEED_LOOP_TIME;
    g_encoder.kalman.dt_sq = g_encoder.kalman.dt * g_encoder.kalman.dt;
    g_encoder.kalman.dt_half = g_encoder.kalman.dt * 0.5f;
    g_encoder.kalman.dt_sq_half = g_encoder.kalman.dt_sq * 0.5f;
    g_encoder.kalman.inv_dt = (g_encoder.kalman.dt > 0.0001f) ? (1.0f / g_encoder.kalman.dt) : 4000.0f;
    
    /* 初始化ARM DSP矩阵实例 */
    /* 状态转移矩阵 F = [1, dt; 0, 1] */
    g_encoder.kalman.F_data[0] = 1.0f;
    g_encoder.kalman.F_data[1] = g_encoder.kalman.dt;
    g_encoder.kalman.F_data[2] = 0.0f;
    g_encoder.kalman.F_data[3] = 1.0f;
    arm_mat_init_f32(&g_encoder.kalman.F_matrix, 2, 2, g_encoder.kalman.F_data);
    
    /* 过程噪声矩阵 Q = [q_vel, 0; 0, q_acc] */
    g_encoder.kalman.Q_data[0] = q_vel;
    g_encoder.kalman.Q_data[1] = 0.0f;
    g_encoder.kalman.Q_data[2] = 0.0f;
    g_encoder.kalman.Q_data[3] = q_acc;
    arm_mat_init_f32(&g_encoder.kalman.Q_matrix, 2, 2, g_encoder.kalman.Q_data);
    
    /* 观测矩阵 H = [1, 0] */
    g_encoder.kalman.H_data[0] = 1.0f;
    g_encoder.kalman.H_data[1] = 0.0f;
    arm_mat_init_f32(&g_encoder.kalman.H_matrix, 1, 2, g_encoder.kalman.H_data);
    
    /* 测量噪声矩阵 R = [r] */
    g_encoder.kalman.R_data[0] = r;
    arm_mat_init_f32(&g_encoder.kalman.R_matrix, 1, 1, g_encoder.kalman.R_data);
    
    /* 初始化协方差矩阵 */
    arm_mat_init_f32(&g_encoder.kalman.P_matrix, 2, 2, (float32_t*)g_encoder.kalman.p);
    
    /* 初始化卡尔曼增益矩阵 */
    arm_mat_init_f32(&g_encoder.kalman.K_matrix, 2, 1, g_encoder.kalman.k);
    
    /* 初始化临时矩阵 */
    arm_mat_init_f32(&g_encoder.kalman.temp1_matrix, 2, 2, g_encoder.kalman.temp1_data);
    arm_mat_init_f32(&g_encoder.kalman.temp2_matrix, 2, 2, g_encoder.kalman.temp2_data);
    arm_mat_init_f32(&g_encoder.kalman.temp3_matrix, 2, 1, g_encoder.kalman.temp3_data);
}

/**
 * @brief 设置卡尔曼滤波器参数
 * @param q_vel 速度过程噪声
 * @param q_acc 加速度过程噪声
 * @param r 测量噪声
 * @param p0 初始协方差
 */
void SetKalmanFilterParams(float q_vel, float q_acc, float r, float p0) {
    g_encoder.kalman.q[0] = q_vel;
    g_encoder.kalman.q[1] = q_acc;
    g_encoder.kalman.r = r;
    
    /* 重新计算预计算常量 */
    g_encoder.kalman.inv_r = (r > 0.0001f) ? (1.0f / r) : 1000.0f;
    
    /* 重新初始化协方差矩阵 */
    g_encoder.kalman.p[0][0] = p0;
    g_encoder.kalman.p[0][1] = 0.0f;
    g_encoder.kalman.p[1][0] = 0.0f;
    g_encoder.kalman.p[1][1] = p0;
}

/**
 * @brief 更新卡尔曼滤波器 - ARM DSP优化版本
 * @param measurement 速度测量值
 * @return 滤波后速度值
 */
static float UpdateKalmanFilter(float measurement) {
    float x_pred[2];
    float y, s;
    arm_status status;
    
    /* 预测步骤 */
    /* x_pred = F * x (2x2 * 2x1) */
    arm_matrix_instance_f32 x_vector;
    arm_matrix_instance_f32 x_pred_vector;
    arm_mat_init_f32(&x_vector, 2, 1, g_encoder.kalman.x);
    arm_mat_init_f32(&x_pred_vector, 2, 1, x_pred);
    
    status = arm_mat_mult_f32(&g_encoder.kalman.F_matrix, &x_vector, &x_pred_vector);
    if(status != ARM_MATH_SUCCESS) {
        /* 降级到手动计算 */
        x_pred[0] = g_encoder.kalman.x[0] + g_encoder.kalman.dt * g_encoder.kalman.x[1];
        x_pred[1] = g_encoder.kalman.x[1];
    }
    
    /* P_pred = F * P * F' + Q */
    /* temp1 = F * P */
    status = arm_mat_mult_f32(&g_encoder.kalman.F_matrix, &g_encoder.kalman.P_matrix, &g_encoder.kalman.temp1_matrix);
    if(status == ARM_MATH_SUCCESS) {
        /* temp2 = temp1 * F' (需要转置F) */
        arm_matrix_instance_f32 Ft_matrix;
        float Ft_data[4] = {1.0f, 0.0f, g_encoder.kalman.dt, 1.0f}; // F转置
        arm_mat_init_f32(&Ft_matrix, 2, 2, Ft_data);
        
        status = arm_mat_mult_f32(&g_encoder.kalman.temp1_matrix, &Ft_matrix, &g_encoder.kalman.temp2_matrix);
        if(status == ARM_MATH_SUCCESS) {
            /* P_pred = temp2 + Q */
            arm_mat_add_f32(&g_encoder.kalman.temp2_matrix, &g_encoder.kalman.Q_matrix, &g_encoder.kalman.P_matrix);
        }
    }
    
    /* 更新步骤 */
    /* y = measurement - H * x_pred */
    y = measurement - x_pred[0]; // H=[1,0], 所以H*x_pred = x_pred[0]
    
    /* S = H * P_pred * H' + R */
    s = g_encoder.kalman.p[0][0] + g_encoder.kalman.r; // 简化计算
    
    /* K = P_pred * H' / S */
    if(fabsf(s) > 0.0001f) {
        float inv_s = 1.0f / s;
        g_encoder.kalman.k[0] = g_encoder.kalman.p[0][0] * inv_s;
        g_encoder.kalman.k[1] = g_encoder.kalman.p[1][0] * inv_s;
    } else {
        g_encoder.kalman.k[0] = 0.0f;
        g_encoder.kalman.k[1] = 0.0f;
    }
    
    /* x = x_pred + K * y */
    g_encoder.kalman.x[0] = x_pred[0] + g_encoder.kalman.k[0] * y;
    g_encoder.kalman.x[1] = x_pred[1] + g_encoder.kalman.k[1] * y;
    
    /* P = (I - K * H) * P_pred */
    /* 简化计算，因为H=[1,0] */
    float k0 = g_encoder.kalman.k[0];
    float k1 = g_encoder.kalman.k[1];
    
    /* 更新协方差矩阵 */
    g_encoder.kalman.p[0][0] = (1.0f - k0) * g_encoder.kalman.p[0][0];
    g_encoder.kalman.p[0][1] = (1.0f - k0) * g_encoder.kalman.p[0][1];
    g_encoder.kalman.p[1][0] = g_encoder.kalman.p[1][0] - k1 * g_encoder.kalman.p[0][0];
    g_encoder.kalman.p[1][1] = g_encoder.kalman.p[1][1] - k1 * g_encoder.kalman.p[0][1];
    
    return g_encoder.kalman.x[0];
}

/**
 * @brief 初始化编码器速度模块
 */
void EncoderSpeed_Init(void)
{
    /* 初始化角度数据 */
    g_encoder.angle.elec_angle = 0.0f;
    g_encoder.angle.mech_angle = 0.0f;
    g_encoder.angle.mech_revolutions = 0;
    g_encoder.angle.total_mech_angle = 0.0f;
    
    /* 初始化速度数据 */
    g_encoder.speed.elec_speed_rad = 0.0f;
    g_encoder.speed.mech_speed_rad = 0.0f;
    g_encoder.speed.mech_speed_rps = 0.0f;
    g_encoder.speed.signed_speed_rps = 0.0f;
    g_encoder.speed.signed_speed_rpm = 0.0f;
    g_encoder.speed.last_mech_angle = 0.0f;
    g_encoder.speed.last_total_angle = 0.0f;
    
    /* 初始化方向 */
    g_encoder.direction = 0;
    g_encoder.angle_direction = 0;
    
    /* 初始化滤波器 */
    g_encoder.filter_type = FILTER_LOWPASS;  // 默认低通滤波
    g_encoder.lp_filter.alpha = DEFAULT_FILTER_ALPHA;
    g_encoder.lp_filter.one_minus_alpha = 1.0f - DEFAULT_FILTER_ALPHA;
    g_encoder.lp_filter.output = 0.0f;
    g_encoder.lp_filter.last_output = 0.0f;
    
    g_encoder.ma_filter.buffer = speed_buffer;
    g_encoder.ma_filter.size = DEFAULT_FILTER_SIZE;
    g_encoder.ma_filter.index = 0;
    g_encoder.ma_filter.sum = 0.0f;
    g_encoder.ma_filter.inv_size = 1.0f / DEFAULT_FILTER_SIZE;
    
    /* 初始化卡尔曼滤波器 */
    InitKalmanFilter(DEFAULT_KALMAN_Q_VEL, DEFAULT_KALMAN_Q_ACC, 
                     DEFAULT_KALMAN_R_MEASURE, DEFAULT_KALMAN_P0_INIT);
    
    /* 初始化FHAN路径规划滤波器 */
    InitFhanFilter(FHAN_DEFAULT_R0, FHAN_MAX_ACCEL_DEFAULT);
    
    /* 初始化速度PI控制器 */
    InitSpeedPI(0.2f, 0.001f, PI_DEFAULT_MAX_INTEGRAL, PI_DEFAULT_MAX_OUTPUT);
    
    /* 清零计数器 */
    g_encoder.z_counter = 0;
    
    /* 初始化Z相校正数据 */
    g_encoder.z_stable_counter = 0;
    g_encoder.z_expected_pos = Z_PHASE_EXPECTED_POS;
    g_encoder.z_position_error = 0;
    
    /* 预计算常量 */
    g_encoder.angle_diff_threshold = 0.01f;
    
    /* 读取SPI绝对位置并初始化TIM3 */
    g_encoder.resolver_pos = AD2S1210_CommRead();
    uint16_t initial_count = CalculateTimerCountFromSPI(g_encoder.resolver_pos);
    TMR3->cval = initial_count;
    
    /* 计算初始电角度 */
    float elec_offset = (float)(g_encoder.resolver_pos % RESOLVER_MAX_COUNT) * ANGLE_CONVERSION_FACTOR;
    if (elec_offset >= TWO_PI) {
        elec_offset -= TWO_PI;
    }
    
    g_encoder.angle.elec_angle = elec_offset;
    g_encoder.angle.mech_angle = elec_offset / MOTOR_POLE_PAIRS;
    g_encoder.speed.last_mech_angle = g_encoder.angle.mech_angle;
    g_encoder.speed.last_total_angle = g_encoder.angle.total_mech_angle;
}

/**
 * @brief 完整的速度环系统初始化
 * @param filter_type 速度滤波器类型选择
 * @param enable_fhan 是否启用FHAN路径规划 (1=启用, 0=禁用)
 * @param enable_feedforward 是否启用前馈补偿 (1=启用, 0=禁用)
 * @note 一次性完成所有速度环相关模块的初始化和配置
 */
void SpeedLoopSystem_Init(FilterType_t filter_type, uint8_t enable_fhan, uint8_t enable_feedforward)
{
    /* 1. 基础编码器模块初始化 */
    EncoderSpeed_Init();
    
    /* 2. 配置指定的滤波器类型 */
    SetSpeedFilterType(filter_type);
    
    /* 3. 根据滤波器类型设置合适的默认参数 */
    switch (filter_type) {
        case FILTER_LOWPASS:
            SetLowPassFilterCoeff(0.3f);  // 默认低通滤波系数
            break;
        case FILTER_MOVING_AVG:
            SetMovingAvgFilterSize(8);    // 默认8点滑动平均
            break;
        case FILTER_KALMAN:
            /* 卡尔曼滤波器已在EncoderSpeed_Init中初始化 */
            break;
        default:
            /* FILTER_NONE无需额外配置 */
            break;
    }
    
    /* 4. 配置FHAN路径规划 */
    if (enable_fhan) {
        EnableFhanFilter(1);  // 启用FHAN
    } else {
        EnableFhanFilter(0);  // 禁用FHAN
    }
    
    /* 5. 启用PI控制器 */
    EnableSpeedPI(1);
    
    /* 6. 配置前馈补偿 */
    if (enable_feedforward) {
        EnablePIFeedforward(1);  // 启用前馈
    } else {
        EnablePIFeedforward(0);  // 禁用前馈
    }
}

/**
 * @brief 重置编码器速度模块
 */
void EncoderSpeed_Reset(void)
{
    /* 停止定时器 */
    tmr_counter_enable(TMR3, FALSE);
    
    /* 重置角度和速度数据 */
    g_encoder.angle.mech_revolutions = 0;
    g_encoder.angle.total_mech_angle = 0.0f;
    
    g_encoder.speed.elec_speed_rad = 0.0f;
    g_encoder.speed.mech_speed_rad = 0.0f;
    g_encoder.speed.mech_speed_rps = 0.0f;
    g_encoder.speed.signed_speed_rps = 0.0f;
    g_encoder.speed.signed_speed_rpm = 0.0f;
    
    /* 重置方向 */
    g_encoder.direction = 0;
    g_encoder.angle_direction = 0;
    
    /* 清零计数器 */
    g_encoder.z_counter = 0;
    
    /* 重置Z相校正数据 */
    g_encoder.z_stable_counter = 0;
    g_encoder.z_position_error = 0;
    
    /* 重新启动定时器 */
    tmr_counter_enable(TMR3, TRUE);
    
    /* 重新初始化 */
    EncoderSpeed_Init();
}

/**
 * @brief 应用一阶低通滤波
 * @param filter 低通滤波器结构体
 * @param input 输入值
 * @return 滤波后输出值
 */
static float ApplyLowpassFilter(LowPassFilter_t *filter, float input)
{
    /* 一阶低通滤波公式: y(n) = α * x(n) + (1-α) * y(n-1) */
    /* 使用预计算的(1-alpha)值 */
    filter->output = filter->alpha * input + filter->one_minus_alpha * filter->last_output;
    filter->last_output = filter->output;
    return filter->output;
}

/**
 * @brief 应用滑动平均滤波
 * @param filter 滑动平均滤波器结构体
 * @param input 输入值
 * @return 滤波后输出值
 */
static float ApplyMovingAvgFilter(MovingAvgFilter_t *filter, float input)
{
    /* 减去即将被移出的数据 */
    filter->sum -= filter->buffer[filter->index];
    
    /* 添加新数据 */
    filter->buffer[filter->index] = input;
    filter->sum += input;
    
    /* 更新索引 */
    filter->index = (filter->index + 1) % filter->size;
    
    /* 返回平均值，使用预计算的1/size值避免除法 */
    return filter->sum * filter->inv_size;
}

/**
 * @brief 计算角度差值
 * @param current_angle 当前角度
 * @param last_angle 上次角度
 * @return 角度差值（-π到π）
 */
static float CalculateAngleDifference(float current_angle, float last_angle)
{
    float diff = current_angle - last_angle;
    
    /* 处理跨越2π边界的情况 */
    if (diff > PI) {
        diff -= TWO_PI;
    } else if (diff < -PI) {
        diff += TWO_PI;
    }
    
    return diff;
}

/**
 * @brief TIM3溢出中断回调函数
 * @note 在TIM3中断服务程序中调用，用于圈数跟踪备份机制
 */
void TIM3_OverflowCallback(void)
{
    /* 获取当前时间和方向 */
    static uint32_t last_z_time = 0;
    uint32_t current_time = g_encoder.z_counter; // 使用Z相计数器作为时间参考
    
    /* 
     * 仅当距离上次Z相触发超过一定时间时，才使用TIM3溢出作为备份
     * 这可以避免与Z相检测重复计数
     */
    if ((current_time - last_z_time) > 100) { // 100个周期为阈值
        uint8_t dir_flag = TMR3->ctrl1_bit.cnt_dir;
        if (dir_flag == 0) {
            /* 正转时，圈数增加 */
            g_encoder.angle.mech_revolutions++;
        } else {
            /* 反转时，圈数减少 */
            g_encoder.angle.mech_revolutions--;
        }
        
        /* 更新上次Z相时间 */
        last_z_time = current_time;
    }
}

/**
 * @brief Z脉冲检测回调函数
 * @note 外部Z相IO中断中调用，只用于记录圈数
 */
void ZPulseDetectedCallback(void)
{
    /* 检测到Z脉冲 */
    g_encoder.z_detected = 1;
    g_encoder.z_counter++;
    
    /* 读取当前计数值和方向 */
    uint16_t current_count = TMR3->cval;
    int8_t current_direction = (TMR3->ctrl1_bit.cnt_dir == 0) ? 1 : -1;
    
    /* 记录圈数逻辑 */
    static uint16_t last_z_count = 0;
    static int8_t last_direction = 0;
    
    if (last_direction != 0) { // 不是第一次触发Z相
        /* 处理正反转过零点 */
        if (current_direction > 0) { // 正转
            if (current_count < last_z_count && (last_z_count - current_count) > TIM3_COUNTER_PERIOD/2) {
                /* 正转跨零点(从高位到低位，计数器溢出) */
                g_encoder.angle.mech_revolutions++;
            }
        } else { // 反转
            if (current_count > last_z_count && (current_count - last_z_count) > TIM3_COUNTER_PERIOD/2) {
                /* 反转跨零点(从低位到高位，计数器下溢) */
                g_encoder.angle.mech_revolutions--;
            }
        }
    }
    
    /* 更新上次Z相位置和方向 */
    last_z_count = current_count;
    last_direction = current_direction;
    
    /* 更新全局Z相检测时间 */
    g_last_z_detect_time = g_encoder.z_counter;
}

/**
 * @brief 获取电角度（简化版本）
 * @return 电角度(0-2π)
 * @note 在20kHz电流环中断中调用，只负责角度计算，不包含速度计算
 */
float GetElectricalAngle_ENC()
{
    /* 从TIM3读取当前计数值及方向 */
    uint16_t tim_count = TMR3->cval;
    uint16_t dir_flag = TMR3->ctrl1_bit.cnt_dir;
    
    g_encoder.direction = dir_flag ? -1 : 1;
    
    /* 计算电角度和机械角度 */
    uint16_t elec_position = tim_count % COUNTS_PER_ELEC_REV;
    float normalized_angle = (float)elec_position * ANGLE_CONVERSION_FACTOR;
    
    /* 更新角度数据 */
    g_encoder.angle.elec_angle = normalized_angle;
    g_encoder.angle.mech_angle = (float)tim_count * MECHANICAL_ANGLE_FACTOR;
    g_encoder.angle.total_mech_angle = g_encoder.angle.mech_angle + g_encoder.angle.mech_revolutions * TWO_PI;

    return normalized_angle;
}

/**
 * @brief 获取带方向的转速(RPS)
 * @return 带方向的转速(rps)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPS(void)
{
    return g_encoder.speed.signed_speed_rps;
}

/**
 * @brief 获取带方向的转速(RPM)
 * @return 带方向的转速(rpm)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPM(void)
{
    return g_encoder.speed.signed_speed_rpm;
}

/**
 * @brief 设置速度滤波器类型
 * @param type 滤波器类型
 */
void SetSpeedFilterType(FilterType_t type)
{
    g_encoder.filter_type = type;
}

/**
 * @brief 设置低通滤波器系数
 * @param alpha 滤波系数(0-1)，值越小滤波效果越强
 */
void SetLowPassFilterCoeff(float alpha)
{
    if (alpha < 0.0f) alpha = 0.0f;
    if (alpha > 1.0f) alpha = 1.0f;
    
    g_encoder.lp_filter.alpha = alpha;
    g_encoder.lp_filter.one_minus_alpha = 1.0f - alpha;  // 预计算(1-alpha)值
}

/**
 * @brief 设置滑动平均滤波器大小
 * @param size 滤波器大小
 */
void SetMovingAvgFilterSize(uint16_t size)
{
    /* 限制滤波器大小不超过缓冲区容量 */
    if (size > sizeof(speed_buffer) / sizeof(speed_buffer[0])) {
        size = sizeof(speed_buffer) / sizeof(speed_buffer[0]);
    }
    if (size < 1) size = 1;
    
    /* 重置滤波器 */
    g_encoder.ma_filter.size = size;
    g_encoder.ma_filter.index = 0;
    g_encoder.ma_filter.sum = 0.0f;
    g_encoder.ma_filter.inv_size = 1.0f / (float)size;  // 预计算1/size，避免除法
    
    /* 清空缓冲区 */
    for (uint16_t i = 0; i < size; i++) {
        g_encoder.ma_filter.buffer[i] = 0.0f;
    }
}

/**
 * @brief 根据SPI读取的旋变位置计算TIM3计数器值
 * @param resolver_pos 旋变绝对位置
 * @return TIM3计数器值
 */
uint16_t CalculateTimerCountFromSPI(uint16_t resolver_pos)
{
    /* 计算相对于零位的电角度偏移 */
    int32_t elec_offset;
    
    /* 计算方向偏移量 */
    int32_t dir = resolver_pos - ALIGN_POSITION;
    
    /* 计算电角度偏移 */
    elec_offset = dir + (dir < 0) * MECH_CYCLE_COUNT;
    
    /* 确保在有效范围内 */
    elec_offset = elec_offset % MECH_CYCLE_COUNT;
    
    return (uint16_t)elec_offset;
}

/**
 * @brief 获取TIM3编码器当前计数方向
 * @return 方向: 1=正转, -1=反转
 * @note 直接读取TIM3的cnt_dir标志位
 */
static int8_t GetTimerDirection(void)
{
    /* 读取TIM3的方向标志位 */
    uint16_t dir_flag = TMR3->ctrl1_bit.cnt_dir;
    
    /* cnt_dir=0表示向上计数(正转)，cnt_dir=1表示向下计数(反转) */
    return dir_flag ? -1 : 1;
}

/**
 * @brief 4KHz速度计算函数
 * @note 在250us速度环定时器中调用
 */
void CalculateSpeed_4KHz(void)
{
        /* 计算角度差值（考虑方向） */
        float angle_diff = g_encoder.angle.total_mech_angle - g_encoder.speed.last_total_angle;
        
        /* 根据角度差确定方向 */
        const float threshold = g_encoder.angle_diff_threshold;
        
        if (angle_diff > threshold) {
            g_encoder.angle_direction = 1;  // 正转
        } else if (angle_diff < -threshold) {
            g_encoder.angle_direction = -1; // 反转
        } else {
            g_encoder.angle_direction = 0;  // 静止
        }
        
    /* 计算转速 */
    float mech_speed_rps = angle_diff * SPEED_CALC_COEFF_RPS;
    float mech_speed_rpm = angle_diff * SPEED_CALC_COEFF_RPM;
        
        /* 结合TIM3方向和角度差方向确定最终方向 */
        int8_t final_direction = g_encoder.direction;
        if (g_encoder.angle_direction != 0) {
            final_direction = g_encoder.angle_direction;
        }
        
        /* 计算带方向的转速 */
        float signed_speed_rps = mech_speed_rps * final_direction;
    float signed_speed_rpm = mech_speed_rpm * final_direction;
        
    /* 根据滤波器类型应用滤波 */
            switch (g_encoder.filter_type) {
                case FILTER_LOWPASS:
            g_encoder.speed.signed_speed_rps = g_encoder.lp_filter.alpha * signed_speed_rps + 
                                        g_encoder.lp_filter.one_minus_alpha * g_encoder.lp_filter.last_output;
            g_encoder.lp_filter.last_output = g_encoder.speed.signed_speed_rps;
            g_encoder.speed.signed_speed_rpm = g_encoder.speed.signed_speed_rps * RPS_TO_RPM;
                    break;
                    
                case FILTER_MOVING_AVG:
            g_encoder.speed.signed_speed_rps = ApplyMovingAvgFilter(&g_encoder.ma_filter, signed_speed_rps);
            g_encoder.speed.signed_speed_rpm = g_encoder.speed.signed_speed_rps * RPS_TO_RPM;
            break;
            
        case FILTER_KALMAN:
            g_encoder.speed.signed_speed_rps = UpdateKalmanFilter(signed_speed_rps);
            g_encoder.speed.signed_speed_rpm = g_encoder.speed.signed_speed_rps * RPS_TO_RPM;
                    break;
                    
                default: /* FILTER_NONE */
            g_encoder.speed.signed_speed_rps = signed_speed_rps;
            g_encoder.speed.signed_speed_rpm = signed_speed_rpm;
                    break;
            }
            
    /* 更新速度相关变量 */
    g_encoder.speed.mech_speed_rps = fabsf(g_encoder.speed.signed_speed_rps);
    g_encoder.speed.mech_speed_rad = g_encoder.speed.signed_speed_rps * TWO_PI;
    g_encoder.speed.elec_speed_rad = g_encoder.speed.mech_speed_rad * MOTOR_POLE_PAIRS;
    
    /* FHAN路径规划处理 */
    ProcessFhan(g_encoder.speed.signed_speed_rps);
        
        /* 更新角度历史数据 */
        g_encoder.speed.last_mech_angle = g_encoder.angle.mech_angle;
        g_encoder.speed.last_total_angle = g_encoder.angle.total_mech_angle;
}

/*============================ FHAN路径规划函数实现 ==================*/

/**
 * @brief 符号函数
 * @param x 输入值
 * @return 1.0f(x>0), -1.0f(x<0), 0.0f(x=0)
 */
static float sign_func(float x)
{
    if (x > 0.0f) return 1.0f;
    else if (x < 0.0f) return -1.0f;
    else return 0.0f;
}

/**
 * @brief 带饱和特性的FHAN函数(避免开根号运算)
 * @param x1 位置状态
 * @param x2 速度状态  
 * @param v 目标输入
 * @param r0 速度因子
 * @param h0 步长
 * @param max_accel 最大加速度限制
 * @return 最优控制量
 * @note 基于韩京清的改进算法，避免开根号运算，增加饱和限制
 */
static float fhan_saturated(float x1, float x2, float v, float r0, float h0, float max_accel)
{
    /* 预计算常量 */
    const float h0_sq = h0 * h0;
    const float d = r0 * h0_sq;  // 切换函数的参数
    
    /* 状态误差 */
    float e1 = x1 - v;           // 位置误差
    float e2 = x2;               // 速度误差（目标速度变化率为0）
    
    /* 快速非线性跟踪函数 */
    float y = e1 + h0 * e2;      // 预测位置误差
    float a0 = fabsf(y);         // |y|
    
    float u;
    if (a0 <= d) {
        /* 线性区域：避免开根号，使用线性逼近 */
        u = -r0 * (e1 + h0 * e2) / d;
    } else {
        /* 非线性区域：使用符号函数 */
        u = -r0 * sign_func(y);
    }
    
    /* 饱和限制 */
    if (u > max_accel) {
        u = max_accel;
    } else if (u < -max_accel) {
        u = -max_accel;
    }
    
    return u;
}

/**
 * @brief 初始化FHAN滤波器
 * @param r0 速度因子，建议值：根据系统惯量调整(50-200)
 * @param max_accel 最大加速度限制(RPS/s)
 * @note 根据电机测试数据，建议r0=50，max_accel=200
 */
void InitFhanFilter(float r0, float max_accel)
{
    g_encoder.fhan.x1 = 0.0f;               // 初始位置状态
    g_encoder.fhan.x2 = 0.0f;               // 初始速度状态
    g_encoder.fhan.r0 = r0;                 // 速度因子
    g_encoder.fhan.h0 = FHAN_DEFAULT_H0;    // 步长(250us)
    g_encoder.fhan.max_accel = max_accel;   // 最大加速度限制
    g_encoder.fhan.enabled = 0;             // 默认禁用，需要手动启用
    
    /* 预计算常量 */
    g_encoder.fhan.h0_sq = g_encoder.fhan.h0 * g_encoder.fhan.h0;
    g_encoder.fhan.r0_h0 = g_encoder.fhan.r0 * g_encoder.fhan.h0;
}

/**
 * @brief 设置FHAN参数
 * @param r0 速度因子
 * @param max_accel 最大加速度限制
 */
void SetFhanParams(float r0, float max_accel)
{
    g_encoder.fhan.r0 = r0;
    g_encoder.fhan.max_accel = max_accel;
    
    /* 重新计算预计算常量 */
    g_encoder.fhan.r0_h0 = g_encoder.fhan.r0 * g_encoder.fhan.h0;
}

/**
 * @brief 使能/禁用FHAN滤波器
 * @param enable 1=启用FHAN路径规划，0=直通滤波后速度
 */
void EnableFhanFilter(uint8_t enable)
{
    if (enable && !g_encoder.fhan.enabled) {
        /* 启用时，用当前速度初始化FHAN状态 */
        g_encoder.fhan.x1 = g_encoder.speed.signed_speed_rps;
        g_encoder.fhan.x2 = 0.0f;  // 初始加速度为0
    }
    g_encoder.fhan.enabled = enable;
}

/**
 * @brief 检查FHAN是否启用
 * @return 1=已启用，0=未启用
 */
uint8_t IsFhanEnabled(void)
{
    return g_encoder.fhan.enabled;
}

/**
 * @brief FHAN路径规划处理函数
 * @param target_speed 目标速度(RPS)，来自速度滤波器输出
 * @note 在CalculateSpeed_4KHz函数中调用，4KHz执行
 */
static void ProcessFhan(float target_speed)
{
    if (!g_encoder.fhan.enabled) {
        /* 未启用时直通 */
        g_encoder.fhan.x1 = target_speed;
        g_encoder.fhan.x2 = 0.0f;
        return;
    }
    
    /* 执行FHAN算法 */
    float u = fhan_saturated(g_encoder.fhan.x1, g_encoder.fhan.x2, target_speed,
                           g_encoder.fhan.r0, g_encoder.fhan.h0, g_encoder.fhan.max_accel);
    
    /* 更新状态 */
    g_encoder.fhan.x1 += g_encoder.fhan.h0 * g_encoder.fhan.x2;  // 位置积分
    g_encoder.fhan.x2 += g_encoder.fhan.h0 * u;                 // 速度积分
}

/**
 * @brief 获取FHAN滤波后的速度指令(RPS)
 * @return FHAN输出的平滑速度指令，用于PI控制器
 */
float GetFhanSpeed_RPS(void)
{
    return g_encoder.fhan.x1;
}

/**
 * @brief 获取FHAN输出的加速度(RPS/s)
 * @return FHAN输出的加速度，可用于前馈补偿
 * @note 加速度前馈计算：I_ff = (J * accel) / Kt
 *       其中 J=8.113kg·m², Kt≈0.38Nm/A
 *       前馈电流 ≈ accel * 21.35 (A)
 */
float GetFhanAcceleration_RPS(void)
{
    return g_encoder.fhan.x2;
}

/*============================ PI控制器函数实现 ======================*/

/**
 * @brief 初始化速度PI控制器
 * @param kp 比例增益
 * @param ki 积分增益 (连续域)
 * @param max_integral 积分限幅
 * @param max_output 输出限幅
 * @note 使用用户指定的调试参数：Kp=0.2, Ki=0.001
 */
void InitSpeedPI(float kp, float ki, float max_integral, float max_output)
{
    g_encoder.speed_pi.kp = kp;
    g_encoder.speed_pi.ki = ki;                        
    g_encoder.speed_pi.ki_ts = ki * SPEED_LOOP_TIME;   // 预计算Ki_discrete
    g_encoder.speed_pi.integral_sum = 0.0f;            // 积分累加和 Σe(i)
    g_encoder.speed_pi.max_integral_sum = (g_encoder.speed_pi.ki_ts > 0.0001f) ? 
                                         (max_integral / g_encoder.speed_pi.ki_ts) : 
                                         1000000.0f;   // 预计算积分累加和限幅
    g_encoder.speed_pi.max_output = max_output;
    g_encoder.speed_pi.leak_rate = PI_INTEGRAL_LEAK_RATE;
    g_encoder.speed_pi.output = 0.0f;
    g_encoder.speed_pi.feedforward = 0.0f;
    g_encoder.speed_pi.total_output = 0.0f;
    g_encoder.speed_pi.enabled = 0;        
    g_encoder.speed_pi.reset_integral = 0;
    g_encoder.speed_pi.ff_enabled = 1;     
}

/**
 * @brief 设置速度PI控制器参数
 * @param kp 比例增益
 * @param ki 积分增益
 * @param max_integral 积分限幅
 * @param max_output 输出限幅
 */
void SetSpeedPIParams(float kp, float ki, float max_integral, float max_output)
{
    g_encoder.speed_pi.kp = kp;
    g_encoder.speed_pi.ki = ki;
    g_encoder.speed_pi.ki_ts = ki * SPEED_LOOP_TIME;  // 重新计算Ki*Ts
    g_encoder.speed_pi.max_integral_sum = (g_encoder.speed_pi.ki_ts > 0.0001f) ? 
                                         (max_integral / g_encoder.speed_pi.ki_ts) : 
                                         1000000.0f;
    g_encoder.speed_pi.max_output = max_output;
}

/**
 * @brief 使能/禁用速度PI控制器
 * @param enable 1=启用PI控制器，0=禁用
 */
void EnableSpeedPI(uint8_t enable)
{
    if (enable && !g_encoder.speed_pi.enabled) {
        /* 启用时清零积分项 */
        g_encoder.speed_pi.integral_sum = 0.0f;
        g_encoder.speed_pi.output = 0.0f;
    }
    g_encoder.speed_pi.enabled = enable;
}

/**
 * @brief 使能/禁用前馈补偿
 * @param enable 1=启用前馈，0=禁用前馈
 */
void EnablePIFeedforward(uint8_t enable)
{
    g_encoder.speed_pi.ff_enabled = enable;
}

/**
 * @brief 重置速度PI积分项
 */
void ResetSpeedPIIntegral(void)
{
    g_encoder.speed_pi.integral_sum = 0.0f;
    g_encoder.speed_pi.reset_integral = 1;
}

/**
 * @brief 检查速度PI是否启用
 * @return 1=已启用，0=未启用
 */
uint8_t IsSpeedPIEnabled(void)
{
    return g_encoder.speed_pi.enabled;
}

/**
 * @brief 速度PI控制器核心算法 - 优化版本
 * @param speed_target 目标速度(RPS)
 * @param speed_feedback 反馈速度(RPS)
 * @note 4KHz执行，优化的离散PI：u(k) = Kp×e(k) + Ki_ts×Σe(i)
 */
static void ProcessSpeedPI(float speed_target, float speed_feedback)
{
    /* 快速退出检查 */
    if (!g_encoder.speed_pi.enabled) {
        g_encoder.speed_pi.output = 0.0f;
        g_encoder.speed_pi.total_output = 0.0f;
        return;
    }
    
    /* 速度误差 */
    const float error = speed_target - speed_feedback;
    
    /* 积分重置检查 */
    if (g_encoder.speed_pi.reset_integral) {
        g_encoder.speed_pi.integral_sum = 0.0f;
        g_encoder.speed_pi.reset_integral = 0;
    } else {
        /* 积分更新：Σe(i) = leak_rate×Σe(i-1) + e(k) */
        g_encoder.speed_pi.integral_sum = g_encoder.speed_pi.integral_sum * g_encoder.speed_pi.leak_rate + error;
        
        /* 积分限幅（使用预计算的限幅值） */
        const float max_sum = g_encoder.speed_pi.max_integral_sum;
        if (g_encoder.speed_pi.integral_sum > max_sum) {
            g_encoder.speed_pi.integral_sum = max_sum;
        } else if (g_encoder.speed_pi.integral_sum < -max_sum) {
            g_encoder.speed_pi.integral_sum = -max_sum;
        }
    }
    
    /* PI输出：u(k) = Kp×e(k) + Ki_ts×Σe(i) */
    g_encoder.speed_pi.output = g_encoder.speed_pi.kp * error + 
                               g_encoder.speed_pi.ki_ts * g_encoder.speed_pi.integral_sum;
    
    /* 前馈补偿 */
    g_encoder.speed_pi.feedforward = (g_encoder.speed_pi.ff_enabled && g_encoder.fhan.enabled) ? 
                                    (g_encoder.fhan.x2 * FEEDFORWARD_COEFF) : 0.0f;
    
    /* 总输出 */
    g_encoder.speed_pi.total_output = g_encoder.speed_pi.output + g_encoder.speed_pi.feedforward;
    
    /* 输出限幅（同时限制PI和总输出） */
    const float max_out = g_encoder.speed_pi.max_output;
    if (g_encoder.speed_pi.output > max_out) {
        g_encoder.speed_pi.output = max_out;
    } else if (g_encoder.speed_pi.output < -max_out) {
        g_encoder.speed_pi.output = -max_out;
    }
    
    if (g_encoder.speed_pi.total_output > max_out) {
        g_encoder.speed_pi.total_output = max_out;
    } else if (g_encoder.speed_pi.total_output < -max_out) {
        g_encoder.speed_pi.total_output = -max_out;
    }
}

/**
 * @brief 获取速度PI控制器输出(电流指令A)
 * @param speed_target 目标速度(RPS) - 可以来自FHAN输出或直接给定
 * @return PI控制器输出的电流指令(A)
 */
float GetSpeedPIOutput(float speed_target)
{
    /* 执行PI控制算法 */
    float speed_feedback;
    if (g_encoder.fhan.enabled) {
        /* 使用FHAN输出作为反馈 */
        speed_feedback = g_encoder.fhan.x1;
    } else {
        /* 直接使用滤波后的速度反馈 */
        speed_feedback = g_encoder.speed.signed_speed_rps;
    }
    
    ProcessSpeedPI(speed_target, speed_feedback);
    
    return g_encoder.speed_pi.output;
}

/**
 * @brief 获取速度PI总输出(包含前馈)
 * @return 总电流指令(A) = PI输出 + 前馈补偿
 */
float GetSpeedPITotalOutput(void)
{
    return g_encoder.speed_pi.total_output;
}

/*============================ 速度环中断回调函数 ==================*/

/**
 * @brief 250us速度环中断回调函数 - 完整流程
 * @param speed_target 目标速度指令(RPS)
 * @return 电流指令输出(A)，包含PI控制和前馈补偿
 * @note 完整的4KHz速度环流程：
 *       1. 角度更新（通过GetElectricalAngle_ENC已在20KHz中完成）
 *       2. 速度计算和卡尔曼滤波
 *       3. FHAN路径规划
 *       4. PI控制器输出
 *       执行时间优化设计，适合250us中断调用
 */
float SpeedLoop_4KHz_Callback(float speed_target)
{
    /* 
     * 步骤1：速度计算和滤波
     * 复用CalculateSpeed_4KHz函数，它包含：
     * - 角度差计算
     * - 速度计算（4KHz系数）
     * - 卡尔曼滤波
     * - FHAN路径规划
     */
    CalculateSpeed_4KHz();
    
    /* 
     * 步骤2：PI控制器处理
     * 根据FHAN是否启用选择不同的控制策略
     */
    float current_output;
    
    if (g_encoder.fhan.enabled) {
        /* FHAN启用：使用FHAN输出作为PI输入和反馈 */
        float fhan_speed_cmd = g_encoder.fhan.x1;  // FHAN平滑后的速度指令
        ProcessSpeedPI(speed_target, fhan_speed_cmd);
        current_output = g_encoder.speed_pi.total_output;  // PI + 前馈
    } else {
        /* FHAN禁用：直接PI控制 */
        float filtered_feedback = g_encoder.speed.signed_speed_rps;  // 卡尔曼滤波后的反馈
        ProcessSpeedPI(speed_target, filtered_feedback);
        current_output = g_encoder.speed_pi.output;  // 仅PI输出，无前馈
    }
    
    return current_output;
}

/**
 * @brief 简化版速度环回调函数（可选）
 * @param speed_target 目标速度指令(RPS)
 * @return 电流指令输出(A)
 * @note 更简洁的版本，适合对执行时间有极严格要求的场合
 */
float SpeedLoop_4KHz_Fast(float speed_target)
{
    /* 快速版本：直接执行核心计算 */
    CalculateSpeed_4KHz();  // 完整的速度计算和滤波流程
    
    /* 直接获取PI输出，使用预设的反馈源 */
    return GetSpeedPIOutput(speed_target);
}

/**
 * @brief 获取速度环输出的Iq电流指令
 * @return Iq电流指令(A)，用于电流环控制
 * @note 等效于GetSpeedPITotalOutput()，提供更明确的接口名称
 */
float GetIqCurrentCommand(void)
{
    return g_encoder.speed_pi.total_output;
}

