/**********************************************************
 * @file     sysTypeDef.h
 * @brief    系统类型定义头文件
 * <AUTHOR>
 * @date     2024-01-06
 * @version  V1.0.0
 * @note     包含系统相关的数据类型定义
***********************************************************/

#ifndef _SYS_TYPE_DEF_H
#define _SYS_TYPE_DEF_H

#include "at32a423.h"

/* 用户布尔类型定义 */
typedef enum {
    RET_ERROR   = 0,    // 错误/失败
    RET_OK      = 1,    // 正常/成功
} ret_t;

typedef enum {
    CHECK_FAIL  = 0,    // 检查失败
    CHECK_PASS  = 1,    // 检查通过
} check_t;

typedef enum {
    INIT_FAIL   = 0,    // 初始化失败
    INIT_OK     = 1,    // 初始化成功
} init_t;

typedef enum {
    DISABLE     = 0,    // 禁用/关闭
    ENABLE      = 1,    // 使能/开启
} enable_t;

/* 通用布尔类型及值定义 */
typedef uint8_t bool_t;
#define false   0
#define true    1
#define FALSE   0
#define TRUE    1

/* 错误代码定义 */
#define ERROR_NONE           0x00000000  // 无错误
#define ERROR_INIT_FAILED    0x00000001  // 初始化失败
#define ERROR_OVERCURRENT    0x00000002  // 过流故障
#define ERROR_OVERVOLTAGE    0x00000004  // 过压故障
#define ERROR_OVERTEMP       0x00000008  // 过温故障
#define ERROR_PHASE_FAULT    0x00000010  // 相线故障


//#define USB_PRINTF_ENABLE      TRUE             // 是否启用USB打印 1启用 0禁用

/****************************告警级别定义 START*********************************/
typedef enum {
    WARN_LEVEL_NONE = 0,     // 无告警
    WARN_LEVEL_1 = 1,        // 一级告警
    WARN_LEVEL_2 = 2,        // 二级告警
    WARN_LEVEL_3 = 3         // 三级告警
} WarnLevel_TypeDef;

/* 保护参数结构体定义 */
typedef struct {
    float threshold;    // 阈值
    uint32_t delay_ms;  // 延时时间(ms)
} ProtectParam_TypeDef;

/* 保护标志位定义 */
typedef enum {
    PROTECT_FLAG_COUNTING  = 0x01,   // 正在计时
    PROTECT_FLAG_TRIGGERED = 0x02,   // 已触发保护
    PROTECT_FLAG_CHECKED   = 0x04,   // 已检查过
    PROTECT_FLAG_LATCHED   = 0x08,   // 锁定状态
    PROTECT_FLAG_RESET     = 0x10    // 复位请求
} Protection_Flags;


/****************************告警级别定义 END***********************************/

/* 电源状态结构体 */
typedef union {
    uint8_t all;                    // 所有状态位
    struct {
        uint8_t power_28v_ok : 1;      // 28V控制电源正常
        uint8_t power_700v_ok : 1;     // 700V高压电源正常
        uint8_t power_stable : 1;      // 电源稳定状态
        uint8_t reserved : 5;          // 保留位
    } bits;
} PowerStatus_TypeDef;

/* 系统工作状态结构体 */
typedef union {
    uint8_t all;                      // 所有状态位
    struct {
        uint8_t sys_init : 1;          // 系统初始化完成
        uint8_t self_test : 1;         // 自检进行中
        uint8_t self_test_ok : 1;      // 自检通过
        uint8_t sys_ready : 1;         // 系统就绪
        uint8_t sys_running : 1;       // 系统运行中
        uint8_t sys_fault : 1;         // 系统故障
        uint8_t sys_stop : 1;          // 系统停机
        uint8_t reserved : 1;          // 保留位
    } bits;
} WorkStatus_TypeDef;

// /* 电机工作状态结构体 */
// typedef union {
//     uint16_t all;                    // 所有状态位
//     struct {
//         uint16_t accelerating : 1;      // 加速状态
//         uint16_t decelerating : 1;      // 减速状态
//         uint16_t power_surge : 1;       // 功率突变
//         uint16_t power_limit_1 : 1;     // 一级功率限制标志
//         uint16_t power_limit_2 : 1;     // 二级功率限制标志
//         uint16_t power_limit_3 : 1;     // 三级功率限制标志
//         uint16_t normal_stop : 1;       // 正常停机标识
//         uint16_t emergency_stop : 1;    // 急停标识
//         uint16_t soft_accel : 1;        // 缓加速标志
//         uint16_t soft_decel : 1;        // 缓减速标志
//         uint16_t reserved : 6;          // 保留位
//     } bits;
// } MotorStatus_TypeDef;

/****************************系统告警状态定义 START****************************/

typedef struct {
    /* 告警级别 - 直接成员访问，不使用level嵌套 */
    WarnLevel_TypeDef current;          // 母线过流告警级别
    WarnLevel_TypeDef bus_voltage;      // 母线过压告警级别
    WarnLevel_TypeDef phase_voltage;    // 相电压过压告警级别
    WarnLevel_TypeDef bus_undervol;     // 母线欠压告警级别
    WarnLevel_TypeDef phase_undervol;   // 相电压欠压告警级别
    WarnLevel_TypeDef cap_temp;         // 电容过温告警级别
    WarnLevel_TypeDef motor_temp;       // 电机过温告警级别
    WarnLevel_TypeDef driver_temp;      // 驱动过温告警级别
    WarnLevel_TypeDef v28_overvol;      // 28V过压告警级别
    WarnLevel_TypeDef v28_undervol;     // 28V欠压告警级别
    WarnLevel_TypeDef ntc6_temp;        // NTC6过温告警级别
    WarnLevel_TypeDef ntc7_temp;        // NTC7过温告警级别
    WarnLevel_TypeDef overspeed;        // 超速告警级别
    WarnLevel_TypeDef mcu_fault;        // 光耦故障告警级别
    uint8_t reserved[2];                // 保留字节，保持16字节对齐
    uint32_t all[4];                    // 4×4=16字节，确保覆盖所有告警字段，用于整体清零
} SystemWarn_TypeDef;

#define SYSTEM_WARN_INIT { \
    .current = WARN_LEVEL_NONE,         \
    .bus_voltage = WARN_LEVEL_NONE,     \
    .phase_voltage = WARN_LEVEL_NONE,   \
    .bus_undervol = WARN_LEVEL_NONE,    \
    .phase_undervol = WARN_LEVEL_NONE,  \
    .cap_temp = WARN_LEVEL_NONE,        \
    .motor_temp = WARN_LEVEL_NONE,      \
    .driver_temp = WARN_LEVEL_NONE,     \
    .v28_overvol = WARN_LEVEL_NONE,     \
    .v28_undervol = WARN_LEVEL_NONE,    \
    .ntc6_temp = WARN_LEVEL_NONE,       \
    .ntc7_temp = WARN_LEVEL_NONE,       \
    .overspeed = WARN_LEVEL_NONE,       \
    .mcu_fault = WARN_LEVEL_NONE,       \
    .reserved = {0, 0},                 \
    .all = {0, 0, 0, 0}                 /* 用于整体清零 */ \
}
/****************************系统告警状态定义 END******************************/

/****************************系统状态位图定义 START******************************/
typedef struct {
    /* 系统标志和状态 */
    struct {
        uint8_t is_warning : 1;         // 是否处于告警状态
        uint8_t is_falut:1;             //是否处于故障状态
        uint8_t is_power_limited : 1;   // 是否处于功率限制
        uint8_t is_speed_limited : 1;   // 是否处于速度限制
        uint8_t is_current_limited : 1; // 是否处于电流限制
        uint8_t reserved : 3;           // 保留位
    } flags;
    
    /* 实时限制系数 */
    float iq_limit_coeff;               // 电流限制系数(0.0-1.0)
    float speed_limit_coeff;            // 速度限制系数(0.0-1.0)
    float power_limit_coeff;            // 功率限制系数(0.0-1.0)
    
    /* 故障源和时间跟踪 */
    WarnLevel_TypeDef warn_level;       // 当前最高告警等级
    uint32_t fault_start_time;          // 故障开始时间(ms)
    uint32_t fault_duration;            // 故障持续时间(ms)
    uint8_t source_type;                // 故障源类型 (0=无, 1=电流, 2=电压, 3=温度, 4=IO)
    
    /* 状态位图 - 32位整数用于位图表示 */
    union {
        uint32_t all;                   // 状态位图
        struct {
            /* 低15位 - 告警状态位图 (bit 0-14) */
            uint32_t warn_current        : 1;  // [0] 电流告警
            uint32_t warn_bus_overvol    : 1;  // [1] 母线过压告警
            uint32_t warn_bus_undervol   : 1;  // [2] 母线欠压告警
            uint32_t warn_phase_voltage  : 1;  // [3] 相电压告警
            uint32_t warn_cap_temp       : 1;  // [4] 电容温度告警
            uint32_t warn_motor_temp     : 1;  // [5] 电机温度告警
            uint32_t warn_driver_temp    : 1;  // [6] 驱动温度告警
            uint32_t warn_v28_overvol    : 1;  // [7] 28V过压告警
            uint32_t warn_v28_undervol   : 1;  // [8] 28V欠压告警
            uint32_t warn_phase_unbalance: 1;  // [9] 三相不平衡告警
            uint32_t warn_overspeed      : 1;  // [10] 超速告警
            uint32_t warn_mcu_fault      : 1;  // [11] 旋变故障告警
            uint32_t warn_comm           : 1;  // [12] 通信告警
            uint32_t warn_hall           : 1;  // [13] 霍尔传感器告警
            uint32_t warn_encoder        : 1;  // [14] 编码器告警
            
            /* 高15位 - 故障状态位图 (bit 15-29) */
            uint32_t fault_current       : 1;  // [15] 电流故障
            uint32_t fault_bus_overvol   : 1;  // [16] 母线过压故障
            uint32_t fault_bus_undervol  : 1;  // [17] 母线欠压故障
            uint32_t fault_phase_voltage : 1;  // [18] 相电压故障
            uint32_t fault_cap_temp      : 1;  // [19] 电容温度故障
            uint32_t fault_motor_temp    : 1;  // [20] 电机温度故障
            uint32_t fault_driver_temp   : 1;  // [21] 驱动温度故障
            uint32_t fault_v28_overvol   : 1;  // [22] 28V过压故障
            uint32_t fault_v28_undervol  : 1;  // [23] 28V欠压故障
            uint32_t fault_phase_unbalance: 1; // [24] 三相不平衡故障
            uint32_t fault_overspeed     : 1;  // [25] 超速故障
            uint32_t fault_mcu           : 1;  // [26] 旋变硬件故障
            uint32_t fault_comm          : 1;  // [27] 通信故障
            uint32_t fault_hall          : 1;  // [28] 霍尔传感器故障
            uint32_t fault_encoder       : 1;  // [29] 编码器故障
            
            /* 最高2位 - 系统状态 (bit 30-31) */
            uint32_t sys_state           : 2;  // [30-31] 系统状态: 00=正常, 01=告警, 10=故障, 11=紧急停机
        } bits;
    } fault_map;
} SystemFlags_TypeDef;

#define SYSTEM_FLAGS_INIT { \
    .flags = {0, 0, 0, 0, 0, {0, 0, 0}}, \
    .iq_limit_coeff = 0.0f, \
    .speed_limit_coeff = 0.0f, \
    .power_limit_coeff = 0.0f, \
    .warn_level = WARN_LEVEL_NONE, \
    .fault_start_time = 0, \
    .fault_duration = 0, \
    .source_type = 0, \
    .fault_map = {0} \
}

/****************************系统状态位图定义 END********************************/

/****************************故障标志定义 START********************************/
/* 告警对应的故障标志结构体 - 改为独立变量避免位域地址问题 */
typedef struct {
    uint8_t current;        // 母线过流故障
    uint8_t bus_voltage;    // 母线过压故障
    uint8_t bus_undervol;   // 母线欠压故障
    uint8_t phase_voltage;  // 相电压过压故障
    uint8_t phase_undervol; // 相电压欠压故障
    uint8_t v28_overvol;    // 28V过压故障
    uint8_t v28_undervol;   // 28V欠压故障
    uint8_t cap_temp;       // 电容过温故障
    uint8_t motor_temp;     // 电机过温故障
    uint8_t driver_temp;    // 碳化硅过温故障
    uint8_t phase_fault;    // 相线故障（三相不平衡）
    uint8_t overspeed;      // 超速故障
    uint8_t reserved[4];    // 保留位，保持结构体大小为16字节对齐
} WarnFault_TypeDef;

/* 初始化宏定义 */
#define WARN_FAULT_INIT {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {0, 0, 0, 0}}

//故障检查掩码 - 需要根据新结构调整使用方式
#define WARN_FAULT_MASK 0x07FF  //  0000 0111 1111 1111
#define SYS_FAULT_MASK 0x001F  // 0000 0000 0001 1111
//故障忽略掩码
#define WARN_IGNORE_MASK 0x0000  // 0000 0000 0000 0000

/* 系统级故障标志结构体 */
typedef union {
    uint16_t all;                    // 所有系统级故障位
    struct {
        uint16_t self_test : 1;      // 自检故障
        uint16_t io_fault : 1;       // IO硬件故障 
        uint16_t comm : 1;           // 通信故障
        uint16_t driver : 1;         // 驱动器故障
        uint16_t extreme : 1;        // 极端故障(如硬件损坏)
        uint16_t reserved : 11;      // 保留位
    } bits;
} SysFault_TypeDef;
/****************************故障标志定义 END**********************************/

/****************************系统命令定义 START********************************/
typedef union {
    uint16_t all;                    // 所有命令位
    struct {
        uint8_t motor_start : 1;      // 电机启动命令
        uint8_t motor_stop : 1;       // 电机停止命令
        uint8_t motor_reset : 1;      // 电机复位命令
        uint8_t motor_emergency_stop : 1; // 电机急停命令
        uint8_t motor_selftest : 1;   // 电机自检命令
        uint8_t motor_status_query : 1; // 电机状态查询命令
        uint8_t motor_clear_fault : 1; // 清除故障命令
        uint8_t reserved : 1;        // 保留位
    } bits;
} SystemCmd_TypeDef;

/****************************系统命令定义 END**********************************/

/****************************系统自检定义 START********************************/
// 自检结果枚举
typedef enum {
    SELF_TEST_PASS = 0,      // 自检通过
    SELF_TEST_FAIL = 1,      // 自检失败
    SELF_TEST_TIMEOUT = 2    // 自检超时
} SelfTestResult;
/****************************系统自检定义 END********************************/


#define ADC_CHANNEL_NUM       18  // ADC所有通道数量
#define ADC_TEMP_CHANNEL_NUM  9   // 实际使用ADC温度通道数量
/* ADC抢占通道数量 */
#define ADC_PREEMPT_CHANNEL_NUM  3  // 抢占通道数量
/* ADC通道序列定义 */
#define ADC_REGULAR_CHANNEL_NUM  15  // 实际使用的普通通道数量
/* ADC普通通道映射 */
#define ADC_CH_TEMP_CAP1      0   // IN0  - PA0  - 电容温度1
#define ADC_CH_UV             1   // IN1  - PA1  - UV线电压
#define ADC_CH_UW             2   // IN2  - PA2  - UW线电压
#define ADC_CH_TEMP_OLL       3   // IN3  - PA3  - OLL温度
#define ADC_CH_TEMP_CAP2      4   // IN4  - PA4  - 电容温度2
#define ADC_CH_TEMP_MOT1      5   // IN5  - PA5  - 电机温度1
#define ADC_CH_TEMP_MOT2      6   // IN6  - PA6  - 电机温度2
#define ADC_CH_700V           7   // IN7  - PA7  - 母线电压
#define ADC_CH_TEMP_PT100_6   21  // IN21 - PB10 - NTC温度6
#define ADC_CH_TEMP_PT1000_7  22  // IN22 - PB11 - NTC温度7
#define ADC_CH_TEMP_BMF_U     23  // IN23 - PB12 - U相温度
#define ADC_CH_TEMP_BMF_V     24  // IN24 - PB13 - V相温度
#define ADC_CH_TEMP_BMF_W     25  // IN25 - PB14 - W相温度
#define ADC_CH_VCC28V         26  // IN26 - PB15 - 28V电压
#define ADC_CH_VCC28V2        20  // IN20 - PB15 - 28V电压

/* ADC通道序列映射表 - 实际转换顺序 */
typedef enum {
    ADC_SEQ_TEMP_CAP1  = 0,   // 序列0 - 电容温度1 
    ADC_SEQ_UV         = 1,   // 序列1 - UV线电压
    ADC_SEQ_UW         = 2,   // 序列2 - UW线电压
    ADC_SEQ_TEMP_OLL   = 3,   // 序列3 - OLL温度
    ADC_SEQ_TEMP_CAP2  = 4,   // 序列4 - 电容温度2
    ADC_SEQ_TEMP_MOT1  = 5,   // 序列5 - 电机温度1
    ADC_SEQ_TEMP_MOT2  = 6,   // 序列6 - 电机温度2
    ADC_SEQ_700V       = 7,   // 序列7 - 母线电压
    ADC_SEQ_TEMP_PT100_6 = 8, // 序列8 - NTC温度6
    ADC_SEQ_TEMP_PT1000_7 = 9,// 序列9 - NTC温度7
    ADC_SEQ_TEMP_BMF_U = 10,  // 序列10 - U相温度
    ADC_SEQ_TEMP_BMF_V = 11,  // 序列11 - V相温度
    ADC_SEQ_TEMP_BMF_W = 12,  // 序列12 - W相温度
    ADC_SEQ_VCC28V     = 13,  // 序列13 - 28V电压
    ADC_SEQ_VCC28V2    = 14,  // 序列14 - 28V电压2
} ADC_Sequence_t;

/****************************外部变量声明 START*******************************/
// 定义全局告警状态变量
extern const uint8_t ADC_CHANNEL_MAP[ADC_REGULAR_CHANNEL_NUM];

/****************************外部变量声明 END*********************************/

#endif 