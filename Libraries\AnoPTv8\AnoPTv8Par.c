/**
 * @file AnoPTv8Par.c
 * @brief AnoPTv8参数管理模块 - 性能优化版本
 * @version 2.0
 * @date 2025-01-20
 * 
 * @description
 * AnoPTv8上位机参数系统实现，提供参数注册、读写和帧解析功能。
 * 
 * 性能优化：
 * 1. 减少分支判断：用if-else链替代switch-case，优先判断常用类型
 * 2. 位运算优化：对称类型使用位运算判断符号位
 * 3. 内存访问优化：减少重复的类型转换和指针解引用
 */

#include "AnoPTv8Par.h"
#include "MotorParams.h"  // 用于参数同步功能

/**
 * @brief 参数信息列表指针数组
 */
const _st_par_info * pParInfoList[ANOPTV8_PAR_MAXCOUNT];

/**
 * @brief 已注册的参数数量
 */
int parCount = 0;

/**
 * @brief 设置参数值
 * @param[in] parid 参数ID
 * @param[in] val float类型的参数值
 */
void anoPTv8ParSetVal(uint16_t parid, float val);

/**
 * @brief 参数帧解析函数
 * @param[in] p 待解析的数据帧指针
 * @details 根据帧ID进行不同的处理:
 *          - 0xE0: 参数命令
 *              - data[0]=0: 读取设备信息
 *              - data[0]=1: 读取参数个数
 *              - data[0]=2: 读取参数值
 *              - data[0]=3: 读取参数信息
 *              - data[0]=0x10: 恢复默认参数(cmdval=0x00AA)
 *          - 0xE1: 参数写入
 */
void AnoPTv8ParFrameAnl(const _un_frame_v8 *p)
{
    switch (p->frame.frameid)
    {
    case 0xE0:
        //参数命令
        if (p->frame.data[0] == 0)
        {
            //读取设备信息
            // 调试信息：记录设备信息请求
            static uint32_t dev_info_req_count = 0;
            dev_info_req_count++;

            AnoPTv8SendDevInfo(p->frame.sdevid);
        }
        else if (p->frame.data[0] == 1)
        {
            //读取参数个数
            AnoPTv8SendParNum(p->frame.sdevid);
        }
        else if (p->frame.data[0] == 2)
        {
            //读取参数值
            AnoPTv8SendParVal(p->frame.sdevid, *(uint16_t *)(p->frame.data+1));
        }
        else if (p->frame.data[0] == 3)
        {
            //读取参数信息
            AnoPTv8SendParInfo(p->frame.sdevid, *(uint16_t *)(p->frame.data+1));
        }
        else if(p->frame.data[0] == 0x10)
        {
            uint16_t cmdval = *(uint16_t *)&p->frame.data[1];
            if(cmdval == 0x00AA)
            {
                //恢复默认参数
                AnoPTv8HwParCmdResetParameter();
            }
			AnoPTv8SendCheck(p->frame.sdevid, p->frame.frameid, p->frame.sumcheck, p->frame.addcheck);
        }
        AnoPTv8HwParCmdRecvCallback(p->frame.data[0], *(uint16_t *)(p->frame.data+1));
        break;
    case 0xE1:
        //参数写入
        {
            uint16_t parid = *(uint16_t *)(p->frame.data+0);
            uint8_t param_type = AnoPTv8GetParamType(parid);
            float val = 0.0f;
            
            // 优化的参数类型解析 - 减少分支判断
            if(param_type == 8) {
                // float类型 - 最常用，优先判断
                val = *(float *)(p->frame.data+2);
            } else if(param_type < 2) {
                // 8位类型 (0=uint8, 1=int8)
                val = (param_type & 1) ? 
                    (float)(*(int8_t *)(p->frame.data+2)) : 
                    (float)(*(uint8_t *)(p->frame.data+2));
            } else if(param_type < 4) {
                // 16位类型 (2=uint16, 3=int16)
                val = (param_type & 1) ? 
                    (float)(*(int16_t *)(p->frame.data+2)) : 
                    (float)(*(uint16_t *)(p->frame.data+2));
            } else {
                // 32位类型 (4=uint32, 5=int32)
                val = (param_type & 1) ? 
                    (float)(*(int32_t *)(p->frame.data+2)) : 
                    (float)(*(uint32_t *)(p->frame.data+2));
            }
            
            anoPTv8ParSetVal(parid, val);
            
            // // 参数写入后，快速同步到Motor实际参数
            // MotorParams_SyncSingleParamToMotor(parid);
            
            AnoPTv8SendCheck(p->frame.sdevid, p->frame.frameid, p->frame.sumcheck, p->frame.addcheck);
            //AnoPTv8HwParValRecvCallback(parid, val);
        }
        break;
    }
}

/**
 * @brief 获取已注册的参数数量
 * @return 已注册的参数数量
 */
int	AnoPTv8ParGetCount(void)
{
    return parCount;
}

/**
 * @brief 注册新参数
 * @param[in] _pi 参数信息结构体指针
 * @details 如果参数数量未超过最大值则注册新参数
 */
void AnoPTv8ParRegister(const _st_par_info * _pi)
{
    if(parCount <= (ANOPTV8_PAR_MAXCOUNT-1))
    {
        pParInfoList[parCount++] = _pi;
    }
}

/**
 * @brief 获取指定参数的信息
 * @param[in] parid 参数ID
 * @return 参数信息结构体指针,如果参数ID无效则返回NULL
 */
const _st_par_info * AnoPTv8ParGetInfo(uint16_t parid)
{
    if(parid > AnoPTv8ParGetCount())
        return 0;
    return pParInfoList[parid];
}

/**
 * @brief 获取参数类型
 * @param[in] parid 参数ID
 * @return 参数类型(0=uint8, 2=uint16, 8=float),如果参数ID无效则返回8
 * @details 从参数信息的mul字段获取类型标识
 */
uint8_t AnoPTv8GetParamType(uint16_t parid)
{
    if(parid >= AnoPTv8ParGetCount())
        return 8;  // 默认float类型
    return (uint8_t)(pParInfoList[parid]->mul);
}

/**
 * @brief 获取参数值
 * @param[in] parid 参数ID
 * @return float类型的参数值,如果参数ID无效则返回0
 * @details 根据参数类型进行适当的类型转换
 */
float AnoPTv8ParGetVal(uint16_t parid)
{
    if(parid >= AnoPTv8ParGetCount())
        return 0;
    
    const _st_par_info *p_info = pParInfoList[parid];
    uint8_t param_type = (uint8_t)(p_info->mul);
    
    // 优化的类型判断
    if(param_type == 8) {
        return *(float *)(p_info->pval);
    } else if(param_type == 0) {
        return (float)(*(uint8_t *)(p_info->pval));
    } else if(param_type == 2) {
        return (float)(*(uint16_t *)(p_info->pval));
    } else if(param_type == 4) {
        return (float)(*(uint32_t *)(p_info->pval));
    }
    return 0.0f;  // 未知类型
}

/**
 * @brief 设置参数值
 * @param[in] parid 参数ID
 * @param[in] val float类型的参数值
 * @details 根据参数类型进行适当的类型转换
 */
void anoPTv8ParSetVal(uint16_t parid, float val)
{
    if(parid >= AnoPTv8ParGetCount())
        return;
    
    const _st_par_info *p_info = pParInfoList[parid];
    uint8_t param_type = (uint8_t)(p_info->mul);
    
    // 优化的类型判断
    if(param_type == 8) {
        *(float *)(p_info->pval) = val;
    } else if(param_type == 0) {
        *(uint8_t *)(p_info->pval) = (uint8_t)val;
    } else if(param_type == 2) {
        *(uint16_t *)(p_info->pval) = (uint16_t)val;
    } else if(param_type == 4) {
        *(uint32_t *)(p_info->pval) = (uint32_t)val;
    }
    // 未知类型则忽略
}
