/* add user code begin Header */
/**
  **************************************************************************
  * @file     at32a423_wk_config.h
  * @brief    header file of work bench config
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to
  * download from Artery official website is the copyrighted work of Artery.
  * Artery authorizes customers to use, copy, and distribute the BSP
  * software and its related documentation for the purpose of design and
  * development in conjunction with Artery microcontrollers. Use of the
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * <PERSON><PERSON><PERSON><PERSON><PERSON>ES OR REPRESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */
/* add user code end Header */

/* define to prevent recursive inclusion -----------------------------------*/
#ifndef __AT32A423_WK_CONFIG_H
#define __AT32A423_WK_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* includes -----------------------------------------------------------------------*/
#include "stdio.h"
#include "at32a423.h"

/* private includes -------------------------------------------------------------*/
/* add user code begin private includes */

/* add user code end private includes */

/* exported types -------------------------------------------------------------*/
/* add user code begin exported types */

/* add user code end exported types */

/* exported constants --------------------------------------------------------*/
/* add user code begin exported constants */

/* add user code end exported constants */

/* exported macro ------------------------------------------------------------*/
/* add user code begin exported macro */

/* add user code end exported macro */

/* add user code begin dma define */
/* user can only modify the dma define value */
#define DMA1_CHANNEL1_BUFFER_SIZE   0
#define DMA1_CHANNEL1_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL1_PERIPHERAL_BASE_ADDR  0

//#define DMA1_CHANNEL2_BUFFER_SIZE   0
//#define DMA1_CHANNEL2_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL2_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL3_BUFFER_SIZE   0
//#define DMA1_CHANNEL3_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL3_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL4_BUFFER_SIZE   0
//#define DMA1_CHANNEL4_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL4_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL5_BUFFER_SIZE   0
//#define DMA1_CHANNEL5_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL5_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL6_BUFFER_SIZE   0
//#define DMA1_CHANNEL6_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL6_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL7_BUFFER_SIZE   0
//#define DMA1_CHANNEL7_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL7_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL1_BUFFER_SIZE   0
//#define DMA2_CHANNEL1_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL1_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL2_BUFFER_SIZE   0
//#define DMA2_CHANNEL2_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL2_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL3_BUFFER_SIZE   0
//#define DMA2_CHANNEL3_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL3_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL4_BUFFER_SIZE   0
//#define DMA2_CHANNEL4_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL4_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL5_BUFFER_SIZE   0
//#define DMA2_CHANNEL5_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL5_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL6_BUFFER_SIZE   0
//#define DMA2_CHANNEL6_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL6_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL7_BUFFER_SIZE   0
//#define DMA2_CHANNEL7_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL7_PERIPHERAL_BASE_ADDR   0
/* add user code end dma define */

/* Private defines -------------------------------------------------------------*/
#define LED_G_PIN    GPIO_PINS_2
#define LED_G_GPIO_PORT    GPIOE
#define LED_R_PIN    GPIO_PINS_3
#define LED_R_GPIO_PORT    GPIOE
#define AD2S_SAMPLE_PIN    GPIO_PINS_4
#define AD2S_SAMPLE_GPIO_PORT    GPIOE
#define AD2S_A0_PIN    GPIO_PINS_5
#define AD2S_A0_GPIO_PORT    GPIOE
#define AD2S_A1_PIN    GPIO_PINS_6
#define AD2S_A1_GPIO_PORT    GPIOE
#define TEST_PC13_PIN    GPIO_PINS_13
#define TEST_PC13_GPIO_PORT    GPIOC
#define AD2S_CS_PIN    GPIO_PINS_9
#define AD2S_CS_GPIO_PORT    GPIOF
#define ADC_UI_Ia_PIN    GPIO_PINS_1
#define ADC_UI_Ia_GPIO_PORT    GPIOC
#define ADC_VI_Ib_PIN    GPIO_PINS_2
#define ADC_VI_Ib_GPIO_PORT    GPIOC
#define ADC_WI_Ic_PIN    GPIO_PINS_3
#define ADC_WI_Ic_GPIO_PORT    GPIOC
#define ADC_TEM_CAP1_PIN    GPIO_PINS_0
#define ADC_TEM_CAP1_GPIO_PORT    GPIOA
#define ADC_UV_PIN    GPIO_PINS_1
#define ADC_UV_GPIO_PORT    GPIOA
#define ADC_UW_PIN    GPIO_PINS_2
#define ADC_UW_GPIO_PORT    GPIOA
#define ADC_TEM_OLL_PIN    GPIO_PINS_3
#define ADC_TEM_OLL_GPIO_PORT    GPIOA
#define ADC_TEM_CAP2_PIN    GPIO_PINS_4
#define ADC_TEM_CAP2_GPIO_PORT    GPIOA
#define ADC_TEM_MOT1_PIN    GPIO_PINS_5
#define ADC_TEM_MOT1_GPIO_PORT    GPIOA
#define ADC_TEM_MOT2_PIN    GPIO_PINS_6
#define ADC_TEM_MOT2_GPIO_PORT    GPIOA
#define ADC_700V_PIN    GPIO_PINS_7
#define ADC_700V_GPIO_PORT    GPIOA
#define IO_IN1_PIN    GPIO_PINS_0
#define IO_IN1_GPIO_PORT    GPIOB
#define IO_IN2_PIN    GPIO_PINS_1
#define IO_IN2_GPIO_PORT    GPIOB
#define ADC_VCC28V2_PIN    GPIO_PINS_2
#define ADC_VCC28V2_GPIO_PORT    GPIOB
#define CON_RST_PIN    GPIO_PINS_7
#define CON_RST_GPIO_PORT    GPIOE
#define TMR1_U_CH1C_PIN    GPIO_PINS_8
#define TMR1_U_CH1C_GPIO_PORT    GPIOE
#define TMR1_U_CH1_PIN    GPIO_PINS_9
#define TMR1_U_CH1_GPIO_PORT    GPIOE
#define TMR1_V_CH2C_PIN    GPIO_PINS_10
#define TMR1_V_CH2C_GPIO_PORT    GPIOE
#define TMR1_V_CH2_PIN    GPIO_PINS_11
#define TMR1_V_CH2_GPIO_PORT    GPIOE
#define TMR1_W_CH3C_PIN    GPIO_PINS_12
#define TMR1_W_CH3C_GPIO_PORT    GPIOE
#define TMR1_W_CH3_PIN    GPIO_PINS_13
#define TMR1_W_CH3_GPIO_PORT    GPIOE
#define Fail_RDY_MCU_PIN    GPIO_PINS_14
#define Fail_RDY_MCU_GPIO_PORT    GPIOE
#define ADC_NTC_Temp6_PIN    GPIO_PINS_10
#define ADC_NTC_Temp6_GPIO_PORT    GPIOB
#define ADC_NTC_Temp7_PIN    GPIO_PINS_11
#define ADC_NTC_Temp7_GPIO_PORT    GPIOB
#define Fail_FLT_MCU_PIN    GPIO_PINS_8
#define Fail_FLT_MCU_GPIO_PORT    GPIOF
#define ADC_Temp_BMF_U_PIN    GPIO_PINS_12
#define ADC_Temp_BMF_U_GPIO_PORT    GPIOB
#define ADC_Temp_BMF_V_PIN    GPIO_PINS_13
#define ADC_Temp_BMF_V_GPIO_PORT    GPIOB
#define ADC_Temp_BMF_W_PIN    GPIO_PINS_14
#define ADC_Temp_BMF_W_GPIO_PORT    GPIOB
#define ADC_VCC28V_PIN    GPIO_PINS_15
#define ADC_VCC28V_GPIO_PORT    GPIOB
#define OVER_WI_CUR1_PIN    GPIO_PINS_8
#define OVER_WI_CUR1_GPIO_PORT    GPIOD
#define OVER_WI_CUR0_PIN    GPIO_PINS_9
#define OVER_WI_CUR0_GPIO_PORT    GPIOD
#define OVER_VOL_BUS_PIN    GPIO_PINS_10
#define OVER_VOL_BUS_GPIO_PORT    GPIOD
#define OVER_VOL_UV1_PIN    GPIO_PINS_11
#define OVER_VOL_UV1_GPIO_PORT    GPIOD
#define OVER_VOL_UV0_PIN    GPIO_PINS_12
#define OVER_VOL_UV0_GPIO_PORT    GPIOD
#define OVER_VI_CUR1_PIN    GPIO_PINS_13
#define OVER_VI_CUR1_GPIO_PORT    GPIOD
#define OVER_VI_CUR0_PIN    GPIO_PINS_14
#define OVER_VI_CUR0_GPIO_PORT    GPIOD
#define OVER_UI_CUR1_PIN    GPIO_PINS_15
#define OVER_UI_CUR1_GPIO_PORT    GPIOD
#define TMR_encoder_A_PIN    GPIO_PINS_6
#define TMR_encoder_A_GPIO_PORT    GPIOC
#define TMR_encoder_B_PIN    GPIO_PINS_7
#define TMR_encoder_B_GPIO_PORT    GPIOC
#define EXTI_encoder_Z_PIN    GPIO_PINS_8
#define EXTI_encoder_Z_GPIO_PORT    GPIOC
#define OVER_UI_CUR0_PIN    GPIO_PINS_8
#define OVER_UI_CUR0_GPIO_PORT    GPIOA
#define SWDIO_PIN    GPIO_PINS_13
#define SWDIO_GPIO_PORT    GPIOA
#define SWCLK_PIN    GPIO_PINS_14
#define SWCLK_GPIO_PORT    GPIOA
#define SPI3_CS_PIN    GPIO_PINS_15
#define SPI3_CS_GPIO_PORT    GPIOA
#define SPI2_CS_PIN    GPIO_PINS_0
#define SPI2_CS_GPIO_PORT    GPIOD
#define OVER_VOL_UW0_PIN    GPIO_PINS_6
#define OVER_VOL_UW0_GPIO_PORT    GPIOD
#define OVER_VOL_UW1_PIN    GPIO_PINS_7
#define OVER_VOL_UW1_GPIO_PORT    GPIOD
#define AT_LOT_PIN    GPIO_PINS_3
#define AT_LOT_GPIO_PORT    GPIOB
#define AT_DOS_PIN    GPIO_PINS_4
#define AT_DOS_GPIO_PORT    GPIOB

/* exported functions ------------------------------------------------------- */
  /* system clock config. */
  void wk_system_clock_config(void);

  /* config periph clock. */
  void wk_periph_clock_config(void);

  /* init clkout function. */
  void wk_clkout_init(void);

  /* nvic config. */
  void wk_nvic_config(void);

  /* init gpio function. */
  void wk_gpio_config(void);

  /* init exint function. */
  void wk_exint_config(void);

  /* init adc1 function. */
  void wk_adc1_init(void);

  /* init crc function. */
  void wk_crc_init(void);

  /* init can1 function. */
  void wk_can1_init(void);

  /* init can2 function. */
  void wk_can2_init(void);

  /* init spi2 function. */
  void wk_spi2_init(void);

  /* init spi3 function. */
  void wk_spi3_init(void);

  /* init tmr1 function. */
  void wk_tmr1_init(void);

  /* init tmr3 function. */
  void wk_tmr3_init(void);

  /* init tmr13 function. */
  void wk_tmr13_init(void);

  /* init usart1 function. */
  void wk_usart1_init(void);

  /* init acc function. */
  void wk_acc_init(void);

  /* init usb_otgfs1 function. */
  void wk_usb_otgfs1_init(void);

  /* init wdt function. */
  void wk_wdt_init(void);

  /* init dma1 channel1 */
  void wk_dma1_channel1_init(void);

  /* config dma channel transfer parameter */
  /* user need to modify parameters memory_base_addr and buffer_size */
  void wk_dma_channel_config(dma_channel_type* dmax_channely, uint32_t peripheral_base_addr, uint32_t memory_base_addr, uint16_t buffer_size);

/* add user code begin exported functions */

/* add user code end exported functions */

#ifdef __cplusplus
}
#endif

#endif
