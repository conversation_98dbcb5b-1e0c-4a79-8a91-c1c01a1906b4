/**********************************************************
 * @file     Sic_SelfTest.c
 * @brief    碳化硅模块自检功能实现文件
 * <AUTHOR>
 * @date     2025-03-10
 * @version  V1.0.0
 *
 * ----------------------------------------------------------
 * 功能概述:
 * 实现碳化硅(SiC)功率模块自检和状态监测系统，包括静态测试、动态测试、
 * 实时故障检测、RMS计算和三相平衡度分析。
 * 主要模块:
 * 1. 静态测试 - 分别测试三相PWM响应，检测开路和短路故障
 * 2. 动态测试 - 监测运行中电流平衡性和谐波畸变率
 * 3. RMS计算 - 环形缓冲区存储，固定点采样计算
 * 4. 三相不平衡检测 - 基于最大偏差计算，分级告警
 * 使用方法:
 * 1. 初始化: Sic_SelfTest_Init()
 * 2. 上电自检: Sic_StaticTest()
 * 3. 运行监测: Sic_DynamicTest(), Sic_CheckFault()
 * 4. RMS计算: RMS_Update_Samples(), RMS_Calculate(), RMS_Get_Results()
 * 5. 三相平衡: Check_Phase_Balance()
 * 注意事项:
 * - PWM测试前确保电机静止
 * - 阈值参数需根据实际系统调整
 * - RMS计算需要足够采样点保证精度
 **********************************************************/
#include "Sic_SelfTest.h"
#include "wk_system.h"
#include <string.h>
#include <math.h>
#include "at32a423.h"       
#include "start_self_test.h"
#include "at32a423_wk_config.h"
#include "Sensor_Drive.h"      // 添加传感器驱动头文件

/************************** 碳化硅自检功能相关 **************************/

/* 碳化硅自检全局变量定义 */
SicSelfTest_TypeDef g_SicSelfTest = {0};

/* 静态函数声明 */
static void Sic_ApplyTestPWM(uint8_t phase, float duty_cycle);
static void Sic_DisablePWM(void);
static uint8_t Sic_MeasureResponse(float* Iu, float* Iv, float* Iw, float* Vuv, float* Vuw, uint8_t sample_count);
static uint8_t Sic_CheckPhaseResponse(float current, float voltage, float threshold);

/**
  * @brief  碳化硅自检初始化函数
  * @param  None
  * @retval None
  */
void Sic_SelfTest_Init(void)
{
    // 清空自检结构体
    memset(&g_SicSelfTest, 0, sizeof(SicSelfTest_TypeDef));
    
    // 初始化测试状态
    g_SicSelfTest.test_state = 0;
    g_SicSelfTest.fault_code = SIC_FAULT_NONE;
}

/**
  * @brief  碳化硅模块静态测试函数
  * @note   通过低占空比PWM信号测试各相的响应情况
  * @param  None
  * @retval SelfTestResult: 
  *         SELF_TEST_PASS - 自检通过
  *         SELF_TEST_FAIL - 自检失败
  */
SelfTestResult Sic_StaticTest(void)
{
    float Iu, Iv, Iw;         // 三相电流
    float Vuv, Vuw;           // 相间电压
    uint8_t phase_ok = 1;     // 相检查结果
    
    // PWM输出禁用
    Sic_DisablePWM();
    wk_delay_ms(100);  // 等待系统稳定
    
    // 1. 测试U相
    Sic_ApplyTestPWM(0, SIC_TEST_PWM_DUTYCYCLE);  // 0表示U相
    wk_delay_ms(10);  // 等待响应稳定
    
    // 测量响应
    if(Sic_MeasureResponse(&Iu, &Iv, &Iw, &Vuv, &Vuw, SIC_SAMPLE_COUNT) == 1) {
        return SELF_TEST_FAIL;
    }
    // 检查U相响应
    if(Iu < SIC_CURRENT_THRESHOLD && Vuv < SIC_TEST_PWM_DUTYCYCLE * 10.0f) {
        // U相可能开路
        g_SicSelfTest.fault_flags.bits.u_open = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_U_OPEN;
        phase_ok = 0;
    } else if(Iu > SIC_SHORT_CURRENT) {
        // U相可能短路
        g_SicSelfTest.fault_flags.bits.u_short = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_U_SHORT;
        phase_ok = 0;
    }
    
    // 禁用PWM输出
    Sic_DisablePWM();
    wk_delay_ms(100);
    
    // 如果发现故障直接返回
    if(!phase_ok) {
        gSelfTest_Manager.items.bits.sic = 1;
        return SELF_TEST_FAIL;
    }
    
    // 2. 测试V相
    Sic_ApplyTestPWM(1, SIC_TEST_PWM_DUTYCYCLE);  // 1表示V相
    wk_delay_ms(50);
    
    // 测量响应
    Sic_MeasureResponse(&Iu, &Iv, &Iw, &Vuv, &Vuw, SIC_SAMPLE_COUNT);
    
    // 检查V相响应
    if(Iv < SIC_CURRENT_THRESHOLD && Vuv < SIC_TEST_PWM_DUTYCYCLE * 10.0f) {
        // V相可能开路
        g_SicSelfTest.fault_flags.bits.v_open = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_V_OPEN;
        phase_ok = 0;
    } else if(Iv > SIC_SHORT_CURRENT) {
        // V相可能短路
        g_SicSelfTest.fault_flags.bits.v_short = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_V_SHORT;
        phase_ok = 0;
    }
    
    // 禁用PWM输出
    Sic_DisablePWM();
    wk_delay_ms(100);
    
    // 如果发现故障直接返回
    if(!phase_ok) {
        gSelfTest_Manager.items.bits.sic = 1;
        return SELF_TEST_FAIL;
    }
    
    // 3. 测试W相
    Sic_ApplyTestPWM(2, SIC_TEST_PWM_DUTYCYCLE);  // 2表示W相
    wk_delay_ms(50);
    
    // 测量响应
    Sic_MeasureResponse(&Iu, &Iv, &Iw, &Vuv, &Vuw, SIC_SAMPLE_COUNT);
    
    // 检查W相响应
    if(Iw < SIC_CURRENT_THRESHOLD && Vuw < SIC_TEST_PWM_DUTYCYCLE * 10.0f) {
        // W相可能开路
        g_SicSelfTest.fault_flags.bits.w_open = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_W_OPEN;
        phase_ok = 0;
    } else if(Iw > SIC_SHORT_CURRENT) {
        // W相可能短路
        g_SicSelfTest.fault_flags.bits.w_short = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_W_SHORT;
        phase_ok = 0;
    }
    
    // 禁用PWM输出
    Sic_DisablePWM();
    
    // 检查结果
    if(!phase_ok) {
        gSelfTest_Manager.items.bits.sic = 1;
        return SELF_TEST_FAIL;
    }
    
    return SELF_TEST_PASS;
}

/**
  * @brief  碳化硅模块动态测试函数
  * @note   监测运行中的电流不平衡度和谐波畸变率
  * @param  None
  * @retval SelfTestResult: 
  *         SELF_TEST_PASS - 自检通过
  *         SELF_TEST_FAIL - 自检失败
  */
SelfTestResult Sic_DynamicTest(void)
{
    // 获取当前三相电流和电压数据
    float Iu, Iv, Iw, Vuv, Vuw;
    
    // 调用测量函数获取实际值
    Sic_MeasureResponse(&Iu, &Iv, &Iw, &Vuv, &Vuw, SIC_SAMPLE_COUNT);
    
    // 计算电流不平衡度
    float unbalance = CalculateUnbalance(Iu, Iv, Iw);
    g_SicSelfTest.unbalance.unbalance = unbalance;
    
    // 检查电流不平衡度
    if(unbalance > SIC_PHASE_UNBALANCE) {
        // 检查是否有相电流接近零
        if(Iu < SIC_CURRENT_THRESHOLD) {
            g_SicSelfTest.fault_flags.bits.motor_phase_loss = 1;
            g_SicSelfTest.fault_code = SIC_FAULT_MOTOR_PHASE_LOSS;
            return SELF_TEST_FAIL;
        } else if(Iv < SIC_CURRENT_THRESHOLD) {
            g_SicSelfTest.fault_flags.bits.motor_phase_loss = 1;
            g_SicSelfTest.fault_code = SIC_FAULT_MOTOR_PHASE_LOSS;
            return SELF_TEST_FAIL;
        } else if(Iw < SIC_CURRENT_THRESHOLD) {
            g_SicSelfTest.fault_flags.bits.motor_phase_loss = 1;
            g_SicSelfTest.fault_code = SIC_FAULT_MOTOR_PHASE_LOSS;
            return SELF_TEST_FAIL;
        }
    }
    
    // 谐波分析需要使用时域电流波形数据
    // 检查电机温度，辅助判断绕组短路
    float motor_temp = Get_Motor_Temperature();
    if(motor_temp > 100.0f) {
        // 温度过高，可能存在绕组短路
        // 实际实现中应结合THD分析
        g_SicSelfTest.fault_flags.bits.motor_winding_short = 1;
        g_SicSelfTest.fault_code = SIC_FAULT_MOTOR_WINDING_SHORT;
        return SELF_TEST_FAIL;
    }
    
    return SELF_TEST_PASS;
}

/**
  * @brief  碳化硅模块故障检测函数
  * @note   综合判断各种故障情况
  * @param  None
  * @retval None
  */
void Sic_CheckFault(void)
{
    // 获取当前三相电流和电压数据
    float Iu, Iv, Iw, Vuv, Vuw;
    float Ibus = Get_Bus_Current();
    
    // 调用测量函数获取实际值
    Sic_MeasureResponse(&Iu, &Iv, &Iw, &Vuv, &Vuw, SIC_SAMPLE_COUNT);
    
    // 计算特征参数
    float I_avg = (Iu + Iv + Iw) / 3.0f;
    float unbalance = CalculateUnbalance(Iu, Iv, Iw);
    // 实际中应基于采集的时域波形计算THD
    float thd = 0.0f;  // 简化实现
    
    // 故障判断逻辑
    
    // 1. SiC模块断路检测
    if ((Iu < SIC_CURRENT_THRESHOLD && Vuv < 10.0f) || 
        (Iv < SIC_CURRENT_THRESHOLD && Vuv < 10.0f) || 
        (Iw < SIC_CURRENT_THRESHOLD && Vuw < 10.0f)) {
        
        // 确认持续时间
        g_SicSelfTest.fault_confirm_time++;
        if(g_SicSelfTest.fault_confirm_time >= SIC_FAULT_CONFIRM_TIME) {
            // 确认具体是哪相断路
            if(Iu < SIC_CURRENT_THRESHOLD) {
                Sic_SetFault(SIC_FAULT_U_OPEN);
            } else if(Iv < SIC_CURRENT_THRESHOLD) {
                Sic_SetFault(SIC_FAULT_V_OPEN);
            } else if(Iw < SIC_CURRENT_THRESHOLD) {
                Sic_SetFault(SIC_FAULT_W_OPEN);
            }
        }
    }
    // 2. SiC模块短路检测
    else if (Iu > 2.0f * 10.0f || Iv > 2.0f * 10.0f || Iw > 2.0f * 10.0f) {  // 假设额定电流为10A
        if (Vuv < 0.5f * 380.0f || Vuw < 0.5f * 380.0f) {  // 假设额定电压为380V
            // 短路故障立即确认，不需要延时
            // 确认具体是哪相短路
            if(Iu > 2.0f * 10.0f) {
                Sic_SetFault(SIC_FAULT_U_SHORT);
            } else if(Iv > 2.0f * 10.0f) {
                Sic_SetFault(SIC_FAULT_V_SHORT);
            } else if(Iw > 2.0f * 10.0f) {
                Sic_SetFault(SIC_FAULT_W_SHORT);
            }
        }
    }
    // 3. 电机缺相检测
    else if ((Iu < SIC_CURRENT_THRESHOLD || Iv < SIC_CURRENT_THRESHOLD || Iw < SIC_CURRENT_THRESHOLD) 
             && unbalance > SIC_PHASE_UNBALANCE) {
        
        g_SicSelfTest.fault_confirm_time++;
        if(g_SicSelfTest.fault_confirm_time >= SIC_FAULT_CONFIRM_TIME) {
            Sic_SetFault(SIC_FAULT_MOTOR_PHASE_LOSS);
        }
    }
    // 4. 电机绕组短路检测
    else if (thd > SIC_THD_THRESHOLD && Get_Motor_Temperature() > 100.0f) {
        g_SicSelfTest.fault_confirm_time++;
        if(g_SicSelfTest.fault_confirm_time >= SIC_FAULT_CONFIRM_TIME) {
            Sic_SetFault(SIC_FAULT_MOTOR_WINDING_SHORT);
        }
    }
    else {
        // 无故障，重置计数器
        g_SicSelfTest.fault_confirm_time = 0;
    }
}

/**
  * @brief  计算三相电流不平衡度
  * @param  Iu: U相电流
  * @param  Iv: V相电流
  * @param  Iw: W相电流
  * @retval 不平衡度(%)
  */
float CalculateUnbalance(float Iu, float Iv, float Iw)
{
    float I_avg = (Iu + Iv + Iw) *0.3333333f;
    
    // 防止除零
    if(fabs(I_avg) < 0.001f) {
        return 0.0f;
    }
    
    // 计算各相偏差
    float u_dev = fabs(Iu - I_avg);
    float v_dev = fabs(Iv - I_avg);
    float w_dev = fabs(Iw - I_avg);
    
    // 找出最大偏差
    float max_dev = u_dev;
    if(v_dev > max_dev) max_dev = v_dev;
    if(w_dev > max_dev) max_dev = w_dev;
    
    // 计算不平衡度
    float unbalance = (max_dev / I_avg) * 100.0f;
    
    // 更新结构体
    g_SicSelfTest.unbalance.u_deviation = u_dev;
    g_SicSelfTest.unbalance.v_deviation = v_dev;
    g_SicSelfTest.unbalance.w_deviation = w_dev;
    g_SicSelfTest.unbalance.max_deviation = max_dev;
    g_SicSelfTest.unbalance.unbalance = unbalance;
    
    return unbalance;
}

/**
  * @brief  计算信号的THD (简化版)
  * @param  signal: 信号数据指针
  * @param  length: 信号长度
  * @retval THD值(%)
  * @note   实际实现中应使用FFT计算谐波含量
  */
float CalculateTHD(float* signal, uint16_t length)
{
    // 简化实现
    return 5.0f;  // 5% THD
}

/**
  * @brief  设置故障状态
  * @param  fault_code: 故障代码
  * @retval None
  */
void Sic_SetFault(SicFaultCode_t fault_code)
{
    g_SicSelfTest.fault_code = fault_code;
    
    // 更新故障标志位
    switch(fault_code) {
        case SIC_FAULT_U_OPEN:
            g_SicSelfTest.fault_flags.bits.u_open = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_V_OPEN:
            g_SicSelfTest.fault_flags.bits.v_open = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_W_OPEN:
            g_SicSelfTest.fault_flags.bits.w_open = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_U_SHORT:
            g_SicSelfTest.fault_flags.bits.u_short = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_V_SHORT:
            g_SicSelfTest.fault_flags.bits.v_short = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_W_SHORT:
            g_SicSelfTest.fault_flags.bits.w_short = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_MOTOR_PHASE_LOSS:
            g_SicSelfTest.fault_flags.bits.motor_phase_loss = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        case SIC_FAULT_MOTOR_WINDING_SHORT:
            g_SicSelfTest.fault_flags.bits.motor_winding_short = 1;
            gSelfTest_Manager.items.bits.sic = 1;
            break;
        default:
            break;
    }
    
    // 设置全局自检故障标志
    gSelfTest_Manager.output.bits.fault = 1;
}

/**
  * @brief  应用测试PWM信号（适用于中心对齐模式3，互补输出）
  * @param  phase: 相索引 (0:U相, 1:V相, 2:W相)
  * @param  duty_cycle: PWM占空比(%)
  * @retval None
  * @note   在中心对齐模式3下使用互补输出控制上下管
  */
static void Sic_ApplyTestPWM(uint8_t phase, float duty_cycle)
{
    // 1. 禁用所有PWM输出
    Sic_DisablePWM();
    
    // 2. 获取定时器周期值并计算比较值（中心对齐模式3）
    uint16_t period = (uint16_t)tmr_period_value_get(TMR1);
    
    // 在中心对齐模式3中，计数器从0计数到period再回到0
    // 对于占空比为duty_cycle的PWM，计算比较值
    // 计算公式需要调整：当计数值<比较值时，通道输出高电平
    uint16_t test_pulse = (uint16_t)((duty_cycle / 100.0f) * period);
    
    // 设置其他通道为0或比period大的值，确保它们始终输出低电平
    uint16_t disable_pulse = 0;  // 或使用 period + 1
    
    // 3. 根据测试相配置PWM
    switch(phase) {
        case 0:  // U相测试
            // 设置U相PWM比较值，使能U相上下管互补输出
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_1, test_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1, TRUE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1C, TRUE);
            
            // 关闭V、W相PWM输出 - 通过设置比较值使其始终为低电平
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_2, disable_pulse);
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_3, disable_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2C, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3C, FALSE);
            break;
            
        case 1:  // V相测试
            // 设置V相PWM比较值，使能V相上下管互补输出
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_2, test_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2, TRUE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2C, TRUE);
            
            // 关闭U、W相PWM输出
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_1, disable_pulse);
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_3, disable_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1C, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3C, FALSE);
            break;
            
        case 2:  // W相测试
            // 设置W相PWM比较值，使能W相上下管互补输出
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_3, test_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3, TRUE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3C, TRUE);
            
            // 关闭U、V相PWM输出
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_1, disable_pulse);
            tmr_channel_value_set(TMR1, TMR_SELECT_CHANNEL_2, disable_pulse);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1C, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2, FALSE);
            tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2C, FALSE);
            break;
            
        default:
            break;
    }
    
    // 5. 使能PWM输出和计数器
    tmr_output_enable(TMR1, TRUE);
    tmr_counter_enable(TMR1, TRUE);
}
/**
  * @brief  禁用所有PWM输出
  * @param  None
  * @retval None
  */
static void Sic_DisablePWM(void)
{
    // 1. 关闭所有PWM通道及其互补通道
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1, FALSE);
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_1C, FALSE);
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2, FALSE);
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_2C, FALSE);
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3, FALSE);
    tmr_channel_enable(TMR1, TMR_SELECT_CHANNEL_3C, FALSE);
    tmr_output_enable(TMR1, FALSE);
}


/**
  * @brief  检查相响应是否正常
  * @param  current: 相电流
  * @param  voltage: 相电压
  * @param  threshold: 电流阈值
  * @retval 1: 正常, 0: 异常
  */
static uint8_t Sic_CheckPhaseResponse(float current, float voltage, float threshold)
{
    // 检查电流是否在正常范围内
    if(current < threshold) {
        return 0;  // 电流过低，可能断路
    }
    
    // 检查电流是否过大，可能短路
    if(current > SIC_SHORT_CURRENT) {
        return 0;  // 电流过大，可能短路
    }
    
    return 1;  // 响应正常
}

/**
  * @brief  测量三相电流和线电压响应
  * @param  Iu: U相电流指针
  * @param  Iv: V相电流指针
  * @param  Iw: W相电流指针
  * @param  Vuv: UV线电压指针
  * @param  Vuw: UW线电压指针
  * @param  sample_count: 采样次数
  * @retval 1: 测量成功, 0: 测量失败
  */
static uint8_t Sic_MeasureResponse(float* Iu, float* Iv, float* Iw, float* Vuv, float* Vuw, uint8_t sample_count)
{
    float sum_Iu = 0, sum_Iv = 0, sum_Iw = 0;
    float sum_Vuv = 0, sum_Vuw = 0;
    uint8_t valid_samples = 0;
    
    // 多次采样求平均值
    for(uint8_t i = 0; i < sample_count; i++) {
        // 直接从ADC结果获取三相电流值
        float current_u = gADC_Result.current_u;
        float current_v = gADC_Result.current_v;
        float current_w = gADC_Result.current_w;
        
        // 获取线电压值（从ADC结果获取）
        float uv_voltage = gADC_Result.uv_voltage;
        float uw_voltage = gADC_Result.uw_voltage;
        
        // 数据有效性检查
        if(current_u != -100.0f && current_v != -100.0f && current_w != -100.0f) {
            sum_Iu += current_u;
            sum_Iv += current_v;
            sum_Iw += current_w;
            sum_Vuv += uv_voltage;
            sum_Vuw += uw_voltage;
            valid_samples++;
        }
        
        // 采样间隔延时
        wk_delay_ms(2);
    }
    
    // 检查是否有足够的有效采样
    if(valid_samples < (sample_count / 2)) {
        return 0;  // 有效采样不足，测量失败
    }
    
    // 计算平均值
    *Iu = sum_Iu / valid_samples;
    *Iv = sum_Iv / valid_samples;
    *Iw = sum_Iw / valid_samples;
    *Vuv = sum_Vuv / valid_samples;
    *Vuw = sum_Vuw / valid_samples;
    
    return 1;  // 测量成功
}

/************************** RMS计算功能相关 **************************/

RMS_OUT Rms_Grid_PhaseVolt = RMS_OUT_DEFAULTS;

// 内部函数：环形缓冲区操作
static uint8_t RMS_Buffer_IsFull(RMS_RingBuffer_t *buffer) {
    return buffer->count >= RMS_BUFFER_SIZE;
}

static uint8_t RMS_Buffer_IsEmpty(RMS_RingBuffer_t *buffer) {
    return buffer->count == 0;
}

static void RMS_Buffer_Push(RMS_RingBuffer_t *buffer, float sample_A, float sample_B, float sample_C) {
    uint8_t next_head;
    
    // 计算下一个头位置
    next_head = (buffer->head + 1) % RMS_BUFFER_SIZE;
    
    // 如果缓冲区满，覆盖最旧的数据
    if (next_head == buffer->tail) {
        buffer->tail = (buffer->tail + 1) % RMS_BUFFER_SIZE;
    } else {
        buffer->count++;
    }
    
    // 存储新数据
    buffer->samples_A[buffer->head] = sample_A;
    buffer->samples_B[buffer->head] = sample_B;
    buffer->samples_C[buffer->head] = sample_C;
    
    // 更新头指针，保证数据完整后才更新指针
    buffer->head = next_head;
}

static uint8_t RMS_Buffer_Pop(RMS_RingBuffer_t *buffer, float *sample_A, float *sample_B, float *sample_C) {
    // 检查缓冲区是否为空
    if (buffer->head == buffer->tail && buffer->count == 0) {
        return 0; // 缓冲区空
    }
    
    // 获取数据
    *sample_A = buffer->samples_A[buffer->tail];
    *sample_B = buffer->samples_B[buffer->tail];
    *sample_C = buffer->samples_C[buffer->tail];
    
    // 更新尾指针
    buffer->tail = (buffer->tail + 1) % RMS_BUFFER_SIZE;
    
    // 确保计数一致性
    if (buffer->count > 0) {
        buffer->count--;
    }
    
    return 1; // 成功
}

/**
 * @brief 更新采样数据（在ADC中断中调用）
 * @param p RMS_OUT结构体指针
 * @param sample_A A相采样值
 * @param sample_B B相采样值
 * @param sample_C C相采样值
 */
void RMS_Update_Samples(RMS_OUT *p, float sample_A, float sample_B, float sample_C)
{
    // 将采样数据存入环形缓冲区
    RMS_Buffer_Push(&p->buffer, sample_A, sample_B, sample_C);
}

/**
 * @brief RMS计算函数（在主循环中调用）
 * @param p RMS_OUT结构体指针
 */
void RMS_Calculate(RMS_OUT *p)
{
    float sample_A, sample_B, sample_C;
    
    // 避免重入计算
    if (p->state == RMS_BUSY) {
        return; // 已经在计算中
    }
    
    p->state = RMS_BUSY;
    
    // 从环形缓冲区获取数据并计算
    while (RMS_Buffer_Pop(&p->buffer, &sample_A, &sample_B, &sample_C)) {
        // 计数与累加
        p->iN++;
        
        // 直接计算平方并累加
        p->fSquare_A += sample_A * sample_A;
        p->fSquare_B += sample_B * sample_B;
        p->fSquare_C += sample_C * sample_C;

        // 固定采样点数计算策略
        if (p->iN >= RMS_SAMPLE_COUNT) {
            // 使用乘法替代除法
            float sum_square_A = p->fSquare_A + p->fSquare_A_former;
            float sum_square_B = p->fSquare_B + p->fSquare_B_former;
            float sum_square_C = p->fSquare_C + p->fSquare_C_former;
            
            // 使用预计算的倒数进行乘法运算
            float mean_square_A = sum_square_A * RMS_SAMPLE_COUNT_RECIPROCAL;
            float mean_square_B = sum_square_B * RMS_SAMPLE_COUNT_RECIPROCAL;
            float mean_square_C = sum_square_C * RMS_SAMPLE_COUNT_RECIPROCAL;
            
            // 使用高效平方根计算
            p->fResult_A = __sqrtf(mean_square_A);
            p->fResult_B = __sqrtf(mean_square_B);
            p->fResult_C = __sqrtf(mean_square_C);
            
            
            // 更新状态
            p->fSquare_A_former = p->fSquare_A;
            p->fSquare_B_former = p->fSquare_B;
            p->fSquare_C_former = p->fSquare_C;
            p->iN = 0;
            p->fSquare_A = 0;
            p->fSquare_B = 0;
            p->fSquare_C = 0;
            
            // 标记结果有效
            p->results_valid = 1;
        }
    }
    
    p->state = RMS_IDLE;
}

/**
 * @brief 获取RMS计算结果
 * @param p RMS_OUT结构体指针
 * @param result_A A相RMS结果
 * @param result_B B相RMS结果
 * @param result_C C相RMS结果
 * @return 1: 结果有效，0: 结果无效
 */
uint8_t RMS_Get_Results(RMS_OUT *p, float *result_A, float *result_B, float *result_C)
{
    // 检查并复制结果
    if(p->results_valid == 1) {
        *result_A = p->fResult_A;
        *result_B = p->fResult_B;
        *result_C = p->fResult_C;
        p->results_valid = 0;
        return 1;
    } else {
        return 0;
    }
}

/************************** 三相不平衡相关功能 **************************/

// 三相不平衡检测全局变量
PhaseBalance_TypeDef g_PhaseBalance = {0};

/**
 * @brief 计算三相不平衡度
 * @param phase_A A相有效值
 * @param phase_B B相有效值
 * @param phase_C C相有效值
 * @return 不平衡度百分比
 */
float Calculate_Phase_Unbalance(float phase_A, float phase_B, float phase_C)
{
    // 计算平均值
    float avg = (phase_A + phase_B + phase_C) / 3.0f;
    
    // 如果平均值几乎为零，避免除零错误
    if (avg < 0.001f) {
        return 0.0f;
    }
    
    // 计算每相与平均值的偏差(绝对值)
    float dev_A = fabsf(phase_A - avg);
    float dev_B = fabsf(phase_B - avg);
    float dev_C = fabsf(phase_C - avg);
    
    // 找出最大偏差
    float max_dev = dev_A;
    if (dev_B > max_dev) max_dev = dev_B;
    if (dev_C > max_dev) max_dev = dev_C;
    
    // 计算不平衡度百分比
    return (max_dev / avg) * 100.0f;
}

/**
 * @brief 检查三相平衡状态
 * @param balance 三相平衡结构体指针
 * @param phase_A A相有效值
 * @param phase_B B相有效值
 * @param phase_C C相有效值
 * @return 1: 平衡, 0: 不平衡
 */
uint8_t Check_Phase_Balance(PhaseBalance_TypeDef *balance, float phase_A, float phase_B, float phase_C)
{
    // 更新相值
    balance->phase_A = phase_A;
    balance->phase_B = phase_B;
    balance->phase_C = phase_C;
    
    // 计算平均值
    balance->avg_value = (phase_A + phase_B + phase_C) / 3.0f;
    
    // 计算不平衡度
    balance->unbalance_percent = Calculate_Phase_Unbalance(phase_A, phase_B, phase_C);
    
    // 判断是否平衡
    if (balance->unbalance_percent < UNBALANCE_WARNING_THRESHOLD) {
        balance->is_balanced = 1;
        return 1; // 平衡
    } else {
        balance->is_balanced = 0;
        return 0; // 不平衡
    }
}