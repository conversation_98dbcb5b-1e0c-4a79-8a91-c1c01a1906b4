/**********************************************************
  * @file     Overall_Init.h
  * @brief    系统初始化总头文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  * @note     包含系统初始化相关的函数声明和配置
***********************************************************/

#ifndef __OVERALL_INIT_H
#define __OVERALL_INIT_H

#ifdef __cplusplus
extern "C" {
#endif

/*****************************头文件管理*********************************/
#include "at32a423.h"
#include "Sensor_Drive.h"
#include "wk_system.h"

/* 系统初始化函数声明 */
void AT32A423_system_init(void);
void user_init(void);
void Enable_System_Interrupts(void);  // 使能系统中断

#ifdef __cplusplus
}
#endif

#endif