/**********************************************************
  * @file     SysFSM.c
  * @brief    系统状态机文件
  * <AUTHOR>
  * @date     2024-01-05
  * @version  V1.0.0
  * @note     包含系统状态管理和定时任务功能
************************************************************/
#include "SysFSM.h"
#include "AnoPTv8.h"
#include "Sys_Protect.h"
#include "Sys_Can.h"
#include "Sys_Protect.h"
#include "at32a423_wk_config.h"
#include "wk_system.h"
#include "Sensor_Drive.h"
#include "start_self_test.h"
#include "SysCtl_AllHeaders.h"
#include "RS422_CU.h"

/*----------------------- 第一部分：定时事件驱动 -----------------------*/

/* 定时任务ID */
static volatile uint8_t taskId_500us=0;
static volatile uint8_t taskId_1ms=0;
static volatile uint8_t taskId_1s=0;
static volatile uint8_t FSM_Task_1S=0;

/* 定时任务中断回调函数 */
static void Task_500us(void)
{
    // TODO: 添加500us周期需要执行的任务
    
}

static void Task_1ms(void)
{
    // TODO: 添加1ms周期需要执行的任务
    
}

static void Task_1s(void)
{
    // TODO: 添加1s周期需要执行的任务

}

void Timer_Tasks_Init(void)
{
    // 初始化定时器事件系统，设置最大支持10个任务   
    TimerEvent_Init(10);
    
    // 注册500us任务(周期为1个基本单位)
    taskId_500us = TimerEvent_AddTask(Task_500us, 1);
    
    // 注册1ms任务(周期为2个基本单位)
    taskId_1ms = TimerEvent_AddTask(Task_1ms, 2);
    
    // 注册1s任务(周期为2000个基本单位: 1s = 2000*500us)
    taskId_1s = TimerEvent_AddTask(Task_1s, 2000);
}

void Timer_Tasks_Execute(void)
{
    // 检查并执行500us任务
    if(gTimerFlag.flag_500us) {
        
        update_wave();
        
        
        gTimerFlag.flag_500us = 0;
    }
    // 检查并执行1ms任务
    if(gTimerFlag.flag_1ms) {
        //CAN_ManageCommunication();
        UpdateF5ADCData();


        static uint8_t ms_cnt = 0;
        if(++ms_cnt >= 100) {


        }
        ms_cnt = 0;
        gTimerFlag.flag_1ms = 0;
    }

    // 检查并执行1s任务
    if(gTimerFlag.flag_1s) {
        FSM_Task_1S++;
        AnoPTv8HwSendFrame(4);
            
        Print_System_Status_Distributed();    // 打印系统状态
        
        gTimerFlag.flag_1s = 0;
    }

    // 检查并执行1min任务
    if(gTimerFlag.flag_1min) {
        gTimerFlag.flag_1min = 0;
    }
}

/*----------------------- 第二部分：状态机核心结构 -----------------------*/

/* 全局变量定义 */
SystemStatus_TypeDef g_SystemStatus = {0};  // 初始化
StateMachineContext_TypeDef g_state_machine_ctx = {0};  // 初始化为0

/* 状态处理函数声明 */
static void State_PowerOn_Handler(void);      // 上电状态处理函数
static void State_InitExternal_Handler(void); // 外设初始化状态处理函数
static void State_SelfTest_Handler(void);     // 自检状态处理函数
static void State_Hold_Handler(void);         // 系统保持状态处理函数
static void State_Ready_Handler(void);        // 系统就绪状态处理函数
static void State_Running_Handler(void);      // 系统运行状态处理函数
static void State_Fault_Handler(void);        // 系统故障状态处理函数
static void State_Shutdown_Handler(void);     // 系统停机状态处理函数

/* 转换条件判断函数声明 */
check_t Check_PowerStable(void);          // 检查电源是否稳定     (上电状态→外设初始化状态)
check_t Check_InitComplete(void);         // 检查初始化是否完成   (外设初始化状态→自检状态)
check_t Check_SelfTestPass(void);         // 检查自检是否通过     (自检状态→就绪状态)
check_t Check_StartCondition(void);       // 检查启动条件是否满足 (就绪状态→运行状态)
check_t Check_SelfTestRetry(void);        // 检查自检是否需要重试 (自检状态→上电状态)
check_t Check_StopCondition(void);        // 检查停止条件是否满足 (就绪状态→停机状态, 运行状态→就绪状态)
check_t Check_EmergencyStop(void);        // 检查是否需要紧急停止 (运行状态→停机状态)
check_t Check_RecoveryCondition(void);    // 检查恢复条件是否满足 (故障状态→保持状态)
check_t Check_ShutdownCondition(void);    // 检查关机条件是否满足 (故障状态→停机状态)
check_t Check_FaultCondition(void);       // 检查故障条件是否满足 (运行状态→故障状态)
check_t Check_Fault_Unrecoverable(void);  // 检查故障是否无法排除 (保持状态→停机状态)
check_t Check_Host_SelfTest_Command(void);// 检查上位机自检指令   (停机状态→自检状态)
check_t Check_Host_Stop_Command(void);    // 检查上位机停机指令
check_t Check_Warning_Condition(void);    // 检查警告条件是否满足
check_t Check_WarningLevel2(void);        // 检查是否达到二级警告
check_t Check_Warning_Cleared(void);      // 检查警告是否已清除
/* 告警检测函数声明 */
WarnLevel_TypeDef Get_Current_Warning_Level(void);     // 获取电流警告等级
WarnLevel_TypeDef Get_Voltage_Warning_Level(void);     // 获取电压警告等级
WarnLevel_TypeDef Get_Temperature_Warning_Level(void); // 获取温度警告等级

/* 故障检测函数声明 */
check_t Check_Current_Fault(void);      // 检查电流故障
check_t Check_Voltage_Fault(void);      // 检查电压故障
check_t Check_Temperature_Fault(void);  // 检查温度故障
void Update_Fault_Code(void);           // 更新故障代码
static void Update_Operating_Condition(void);

void Execute_Deceleration_Shutdown(void);

#define ERROR_NONE              0x00000000  // 无错误

//#define USB_PRINTF_ENABLE      TRUE             // 是否启用USB打印 1启用 0禁用


/* 状态转换表定义 */
static const StateTransition_TypeDef state_transitions[] = {
    // 当前状态              目标状态            优先级  条件判断函数
    {STATE_POWER_ON,        STATE_INIT_EXTERNAL, 1,     Check_PowerStable},          // 上电状态→外设初始化状态
    {STATE_INIT_EXTERNAL,   STATE_SELF_TEST,     1,     Check_InitComplete},         // 外设初始化状态→自检状态
    {STATE_SELF_TEST,       STATE_READY,         1,     Check_SelfTestPass},         // 自检状态→就绪状态
    {STATE_SELF_TEST,       STATE_POWER_ON,      2,     Check_SelfTestRetry},        // 自检状态→上电状态
    {STATE_SELF_TEST,       STATE_HOLD,          3,     Check_SelfTestFail},         // 自检状态→系统保持状态
    {STATE_HOLD,            STATE_READY,         1,     Check_SystemNormal},         // 系统保持状态→就绪状态
    {STATE_HOLD,            STATE_SHUTDOWN,      2,     Check_Fault_Unrecoverable},  // 系统保持状态→停机状态
    {STATE_READY,           STATE_RUNNING,       1,     Check_StartCondition},       // 就绪状态→运行状态
    {STATE_READY,           STATE_SHUTDOWN,      2,     Check_StopCondition},        // 就绪状态→停机状态
    {STATE_RUNNING,         STATE_SHUTDOWN,      1,     Check_EmergencyStop},        // 运行状态→停机状态
    {STATE_RUNNING,         STATE_FAULT,         2,     Check_FaultCondition},       // 运行状态→故障状态
    {STATE_RUNNING,         STATE_READY,         3,     Check_StopCondition},        // 运行状态→就绪状态
    {STATE_FAULT,           STATE_SHUTDOWN,      1,     Check_ShutdownCondition},    // 故障状态→停机状态
    {STATE_FAULT,           STATE_HOLD,          2,     Check_RecoveryCondition},    // 故障状态→系统保持状态
    {STATE_SHUTDOWN,        STATE_SELF_TEST,     1,     Check_Host_SelfTest_Command},// 停机状态→自检状态
};

/* 状态转换索引表 */
static const struct {
    uint8_t start_index;    // 该状态的起始索引
    uint8_t count;          // 该状态的转换数量
} state_transition_index[] = {
    [STATE_POWER_ON]      = {0,  1},  // 1个转换
    [STATE_INIT_EXTERNAL] = {1,  1},  // 1个转换
    [STATE_SELF_TEST]     = {2,  3},  // 3个转换
    [STATE_HOLD]          = {5,  2},  // 2个转换
    [STATE_READY]         = {7,  2},  // 2个转换
    [STATE_RUNNING]       = {9,  3},  // 3个转换
    [STATE_FAULT]         = {12, 2},  // 2个转换
    [STATE_SHUTDOWN]      = {14, 1}   // 1个转换
};

/* 状态机状态名称对照表 */
static const char* state_names[] = {
    "上电状态",             // STATE_POWER_ON
    "外设初始化",           // STATE_INIT_EXTERNAL
    "自检状态",             // STATE_SELF_TEST
    "系统保持",             // STATE_HOLD
    "系统就绪",             // STATE_READY
    "系统运行",             // STATE_RUNNING
    "系统故障",             // STATE_FAULT
    "系统关机"              // STATE_SHUTDOWN
};

/* 状态处理函数数组 */
static void (*const state_handlers[])(void) = {
    State_PowerOn_Handler,      // 上电状态处理函数
    State_InitExternal_Handler, // 外设初始化状态处理函数
    State_SelfTest_Handler,     // 自检状态处理函数
    State_Hold_Handler,         // 系统保持状态处理函数
    State_Ready_Handler,        // 系统就绪状态处理函数
    State_Running_Handler,      // 系统运行状态处理函数
    State_Fault_Handler,        // 系统故障状态处理函数
    State_Shutdown_Handler      // 系统关机状态处理函数
};

/* 预定义每个状态的上下文指针 */
static StateContext_t * const g_ctx_power_on = &g_state_machine_ctx.state_ctx[STATE_POWER_ON];
static StateContext_t * const g_ctx_init_external = &g_state_machine_ctx.state_ctx[STATE_INIT_EXTERNAL];
static StateContext_t * const g_ctx_self_test = &g_state_machine_ctx.state_ctx[STATE_SELF_TEST];
static StateContext_t * const g_ctx_hold = &g_state_machine_ctx.state_ctx[STATE_HOLD];
static StateContext_t * const g_ctx_ready = &g_state_machine_ctx.state_ctx[STATE_READY];
static StateContext_t * const g_ctx_running = &g_state_machine_ctx.state_ctx[STATE_RUNNING];
static StateContext_t * const g_ctx_fault = &g_state_machine_ctx.state_ctx[STATE_FAULT];
static StateContext_t * const g_ctx_shutdown = &g_state_machine_ctx.state_ctx[STATE_SHUTDOWN];

/**
 * @brief  检查并执行状态转换V2.1版本
 * @note   按优先级顺序检查可能的状态转换，立即执行，
 *         如果满足条件，则执行状态转换，不进行条件全判断并优先级判断
 *         要求：状态转化表按照优先级从高到低排列
 * @param  None
 * @retval None
 */
static void Check_StateTransition(void)
{
    SystemState_TypeDef current = g_state_machine_ctx.current_state;
    
    // 保存当前状态作为上一个状态
    g_state_machine_ctx.previous_state = current;
    
    // 定位到当前状态的转换组
    uint8_t start = state_transition_index[current].start_index;
    uint8_t count = state_transition_index[current].count;
    
    // 遍历当前状态的转换
    for (uint8_t i = 0; i < count; i++) {
        const StateTransition_TypeDef* trans = &state_transitions[start + i];
        
        // 检查转换条件
        if (trans->condition() == CHECK_PASS) {
            // 条件满足，执行状态转换
            SystemState_TypeDef next_state = trans->next_state;
            
            // 更新状态机上下文
            g_state_machine_ctx.entry_state = current;
            g_state_machine_ctx.current_state = next_state;
            g_state_machine_ctx.state_entry_time = Get_Runtime_Ms();
            
#if USB_PRINTF_ENABLE
            // 打印状态转换信息
            Usb_printf_direct(LOG_COLOR_GREEN, "状态转换: %s -> %s\n", 
                      state_names[current], state_names[next_state]);
#endif
            return;
        }
    }
    
    // 如果没有找到满足条件的转换，保持当前状态
}

/**
 * @brief  系统状态更新
 * @note   检查状态转换并执行当前状态的处理函数
 * @param  None
 * @retval None
 */
void System_StatusUpdate(void)
{
    // 更新告警状态
    //Update_Warning_Status();
    
    // 检查状态转换
    Check_StateTransition();
    
    // 执行处理函数
    if (g_state_machine_ctx.current_state < sizeof(state_handlers)/sizeof(state_handlers[0])) {
        state_handlers[g_state_machine_ctx.current_state]();
    }

#if USB_PRINTF_ENABLE
    if(FSM_Task_1S >= 5) {
        Usb_printf(LOG_COLOR_GREEN, "当前状态: %s\n", state_names[g_state_machine_ctx.current_state]);
        FSM_Task_1S = 0;
    }
#endif
}

/*----------------------- 第三部分：状态转换条件函数 -----------------------*/

#define POWER_STABLE_DELAY_MS    100           /* 电源稳定延时定义 */
#define INIT_RETRY_MAX_COUNT     3             /* 初始化重试次数定义 */
#define INIT_RETRY_INTERVAL_MS   100           /* 初始化重试间隔时间定义 */
#define SELF_TEST_MAX_RETRY      3             /* 自检最大重试次数定义 */
#define SELF_TEST_INTERVAL_MS    500           /* 自检间隔(ms) */
#define STATE_TRANSITION_COUNT   15            /* 当前状态转换的数量 */
#define MAX_STATE_TIME           180000        /* 3分钟，3分钟内未恢复则进入停机 */

/**
 * @brief  检查WarnFault_TypeDef结构体是否有任何故障标志被设置
 * @param  warn_fault: 指向WarnFault_TypeDef结构体的指针
 * @retval uint8_t: 1-有故障标志被设置, 0-无故障标志被设置
 */
static uint8_t Check_WarnFault_Any(const WarnFault_TypeDef* warn_fault)
{
    return (warn_fault->current || warn_fault->bus_voltage || warn_fault->bus_undervol ||
            warn_fault->phase_voltage || warn_fault->phase_undervol || warn_fault->v28_overvol ||
            warn_fault->v28_undervol || warn_fault->cap_temp || warn_fault->motor_temp ||
            warn_fault->driver_temp || warn_fault->phase_fault || warn_fault->overspeed);
}

/**
 * @brief  检查电源是否稳定
 * @note   负责检查状态，不执行操作
 * @param  None
 * @retval check_t: CHECK_PASS-电源稳定(STATE_POWER_ON -> STATE_INIT_EXTERNAL), CHECK_FAIL-电源不稳定
 */
check_t Check_PowerStable(void)
{
    if (g_SystemStatus.power.bits.power_28v_ok ) {
        return CHECK_PASS;
    } else {
        return CHECK_FAIL;
    }
//     // 第一层检查：电源电压是否正常
//     if (!g_SystemStatus.power.bits.power_28v_ok || 
//         !g_SystemStatus.power.bits.power_700v_ok) {
//         stable_start_time = 0;  // 重置计时器
// #if USB_PRINTF_ENABLE
//         static uint32_t power_print_time = 0;
//         static uint32_t temp_print_time = 0;
//         uint32_t current_time = Get_Runtime_Ms();   
//         // 电源状态每3秒打印一次
//         if(current_time - power_print_time >= 3000) {
//             Usb_printf_direct(LOG_COLOR_RED, "供电不正常 28V:%0.2fV 母线:%0.2fV\n", Get_V28_Main(), Get_Bus_Voltage());
//             power_print_time = current_time;
//         }
// #endif
//         return CHECK_FAIL;      // 电压不正常，直接返回失败
//     }  
//     // 第二层检查：是否需要开始计时
//     if (stable_start_time == 0) {
//         stable_start_time = Get_Runtime_Ms();  // 开始计时
//         return CHECK_FAIL;                     // 首次检测到电压正常，返回失败
//     }   
//     // 第三层检查：是否达到稳定时间且电源仍然稳定
//     if ((Get_Runtime_Ms() - stable_start_time) >= POWER_STABLE_DELAY_MS) {
//         // 再次确认电源状态
//         if (g_SystemStatus.power.bits.power_28v_ok && 
//             g_SystemStatus.power.bits.power_700v_ok) {
//             g_SystemStatus.power.bits.power_stable = 1;
// #if USB_PRINTF_ENABLE
//             Usb_printf_direct(LOG_COLOR_GREEN, "电源稳定，上电完成\n");
// #endif
//             return CHECK_PASS;  // 延时时间到且电源稳定
//         } else {
//             g_SystemStatus.power.bits.power_stable = 0;
//             stable_start_time = 0;  // 电源变得不稳定，重置计时器
//         }
//     }
//     return CHECK_FAIL;  // 等待延时中或电源不稳定

}

/**
 * @brief  检查初始化是否完成
 * @note   负责检查状态，不执行初始化操作，初始化完成后延迟100ms再进入自检
 * @param  None
 * @retval check_t: CHECK_PASS-初始化完成并延迟结束(STATE_INIT_EXTERNAL -> STATE_SELF_TEST), CHECK_FAIL-初始化未完成或延迟未结束
 */
check_t Check_InitComplete(void)
{
    static uint32_t init_complete_time = 0;
    uint32_t current_time = Get_Runtime_Ms();
    
    // 检查初始化标志
    if (g_SystemStatus.work.bits.sys_init) {
        // 如果是首次检测到初始化完成
        if (init_complete_time == 0) {
            init_complete_time = current_time;  // 记录完成时间
            return CHECK_FAIL;  // 返回失败，等待延迟
        }
        
        // 检查是否已经过了延迟时间
        if ((current_time - init_complete_time) >= 100) {  // 100ms延迟
#if USB_PRINTF_ENABLE
            Usb_printf_direct(LOG_COLOR_GREEN, "初始化完成,100ms后进入自检\n");
#endif
            return CHECK_PASS;  // 延迟结束，可以进入自检
        } else {
            return CHECK_FAIL;  // 延迟未结束，继续等待
        }
    } 
    else {
        // 初始化未完成，重置时间记录
        init_complete_time = 0;
        return CHECK_FAIL;
    }
}

/**
 * @brief  检查自检是否通过
 * @note   负责检查状态，不执行自检操作
 * @param  None
 * @retval check_t: CHECK_PASS-自检通过(STATE_SELF_TEST -> STATE_READY), CHECK_FAIL-自检失败
 */
check_t Check_SelfTestPass(void)
{
    return g_SystemStatus.work.bits.self_test_ok ? CHECK_PASS : CHECK_FAIL;
}

/**
 * @brief  检查是否需要重试自检
 * @note   自检失败后会回到上电状态重新开始，最多3次，每次间隔一定时间
 * @param  None
 * @retval check_t: CHECK_PASS-需要重试(STATE_SELF_TEST -> STATE_POWER_ON), CHECK_FAIL-不需要重试
 */
check_t Check_SelfTestRetry(void)
{
    static uint8_t retry_count = 0;
    static uint32_t last_retry_time = 0;  // 上次重试的时间
    uint32_t current_time = Get_Runtime_Ms();  // 获取当前时间
    
    // 检查是否存在极端故障
    if (g_SystemStatus.sys_fault.bits.extreme) {

        retry_count = 0;
        return CHECK_FAIL;  // 直接退出，检查系统保持
    }
    // 未超过最大重试次数
    if (retry_count < SELF_TEST_MAX_RETRY) {
    // 检查是否已经过了足够的时间间隔
        if (last_retry_time == 0 || (current_time - last_retry_time) >= SELF_TEST_INTERVAL_MS) {
            retry_count++;  // 增加重试计数
            g_SystemStatus.work.bits.sys_init = 0;  // 清除初始化标志
            g_SystemStatus.power.bits.power_stable = 0;  // 清除电源稳定标志
            last_retry_time = current_time;  // 更新上次重试时间
                
#if USB_PRINTF_ENABLE
            Usb_printf_direct(LOG_COLOR_RED, "自检失败，第%d次重试\n", retry_count);
#endif
            return CHECK_PASS;  // 返回到上电状态
            } else {
            // 时间间隔不足，暂不重试
            return CHECK_FAIL;
        }
    }
    // 达到最大重试次数
        else {
            retry_count = 0;
            last_retry_time = 0;  // 重置重试时间
#if USB_PRINTF_ENABLE
            Usb_printf_direct(LOG_COLOR_RED, "自检失败，达到最大重试次数\n");
#endif
            return CHECK_FAIL;  // 让状态机检查其他转换条件
        }
    }


/**
 * @brief  检查自检是否失败
 * @retval check_t: CHECK_PASS-自检失败且已达到最大重试次数(STATE_SELF_TEST -> STATE_HOLD), CHECK_FAIL-自检通过或仍可重试
 */
check_t Check_SelfTestFail(void)
{
    // 达到最大重试次数且自检仍未通过，返回通过（进入HOLD状态）
    return CHECK_PASS;
}

/**
 * @brief  检查系统是否满足启动条件
 * @note   需要系统就绪、未运行、未停机，且收到启动命令
 * @param  None
 * @retval check_t: CHECK_PASS-可以启动(STATE_READY -> STATE_RUNNING), CHECK_FAIL-不满足启动条件
 */
check_t Check_StartCondition(void)
{
    if (g_SystemStatus.work.bits.sys_ready && 
        !g_SystemStatus.work.bits.sys_running &&
        !g_SystemStatus.work.bits.sys_stop &&
        g_SystemStatus.cmd.bits.motor_start) {
        return CHECK_PASS;
    }
    return CHECK_FAIL;
}

/**
 * @brief  检查是否满足正常停机条件
 * @param  None
 * @retval check_t: CHECK_PASS-可以停机(STATE_READY -> STATE_SHUTDOWN 或 STATE_RUNNING -> STATE_READY), CHECK_FAIL-不满足停机条件
 */
check_t Check_StopCondition(void)
{
    return g_SystemStatus.cmd.bits.motor_stop ? CHECK_PASS : CHECK_FAIL;
}

/**
 * @brief  检查是否需要紧急停机
 * @param  None
 * @retval check_t: CHECK_PASS-需要紧急停机(STATE_RUNNING -> STATE_SHUTDOWN), CHECK_FAIL-无需紧急停机
 */
check_t Check_EmergencyStop(void)
{
    // 检查电机紧急停止命令和极端故障
    if (g_SystemStatus.cmd.bits.motor_emergency_stop || 
        g_SystemStatus.sys_fault.bits.extreme) {
        return CHECK_PASS;  // 需要紧急停机
    }
    return CHECK_FAIL;  // 无需紧急停机
}



/**
 * @brief  检查是否可以从故障切入到HOLD状态
 * @note   负责检查状态
 * @param  None
 * @retval check_t: CHECK_PASS-可以切入到HOLD状态(STATE_FAULT -> STATE_HOLD), CHECK_FAIL-无法切入到HOLD状态
 */
check_t Check_RecoveryCondition(void)
{
    static uint32_t first_fault_time = 0;
    uint32_t current_time = Get_Runtime_Ms();
    
    // 检查是否是极限故障或相线故障
    if (g_SystemStatus.sys_fault.bits.extreme ||          // 极限故障
        g_SystemStatus.warn_fault.phase_fault) {     // 相线故障
        return CHECK_FAIL;
    }
    
    // 如果是第一次进入故障状态，记录时间
    if (first_fault_time == 0) {
        first_fault_time = current_time;
        return CHECK_FAIL;
    }
    
    // 检查是否已经过去100ms
    if ((current_time - first_fault_time) < 100) {
        return CHECK_FAIL;
    }
    
    // 检查其他故障是否已清除
    if (!g_SystemStatus.work.bits.sys_fault) {
        // 清除故障时间记录
        first_fault_time = 0;
        return CHECK_PASS;
    }
    
    return CHECK_FAIL;
}

/**
 * @brief  故障状态检查是否需要进入关机
 * @note   负责检查状态，不执行关机操作
 * @param  None
 * @retval check_t: CHECK_PASS-需要关机(STATE_FAULT -> STATE_SHUTDOWN), CHECK_FAIL-无需关机
 */
check_t Check_ShutdownCondition(void)
{
    static uint32_t fault_start_time = 0;
    uint32_t current_time = Get_Runtime_Ms();
    
    // 检查极限故障
    if (g_SystemStatus.sys_fault.bits.extreme) {
        return CHECK_PASS;
    }
    
    // 检查急停控制
    if (g_SystemStatus.cmd.bits.motor_emergency_stop) {
        return CHECK_PASS;
    }
    
    // 检查故障持续时间
    if (g_SystemStatus.work.bits.sys_fault) {
        // 如果是第一次进入故障状态
        if (fault_start_time == 0) {
            fault_start_time = current_time;
            return CHECK_FAIL;
        }
        
        // 检查故障持续时间是否超过1min
        if ((current_time - fault_start_time) >= 60000) {
            return CHECK_PASS;
        }
    } else {
        // 如果不在故障状态，重置故障时间
        fault_start_time = 0;
    }
    
    return CHECK_FAIL;
}


/**
 * @brief  检查是否存在故障条件
 * @note   负责检查状态，不执行故障处理
 * @param  None
 * @retval check_t: CHECK_PASS-存在故障(STATE_RUNNING -> STATE_FAULT), CHECK_FAIL-无故障
 */
check_t Check_FaultCondition(void)
{
    // 操作检查是否有故障位被设置
    if (Check_WarnFault_Any(&g_SystemStatus.warn_fault) || 
        (g_SystemStatus.sys_fault.all & SYS_FAULT_MASK)) {
        return CHECK_PASS;
    }
    return CHECK_FAIL;
}


/**
 * @brief  检查上位机是否发送自检指令
 * @note   检查上位机通信命令中的自检请求标志
 * @param  None
 * @retval check_t: CHECK_PASS-收到自检指令(STATE_SHUTDOWN -> STATE_SELF_TEST), CHECK_FAIL-未收到自检指令
 */
check_t Check_Host_SelfTest_Command(void)
{
    // 检查电机自检命令位是否使能
    if (g_SystemStatus.cmd.bits.motor_selftest) {
        g_SystemStatus.cmd.bits.motor_selftest = 0;
        return CHECK_PASS;
    }
    return CHECK_FAIL;
}

/*----------------------- 第四部分：状态处理函数 -----------------------*/


/**
 * @brief  上电状态处理函数
 * @note   负责执行上电过程
 * @param  None
 * @retval None
 */
static void State_PowerOn_Handler(void)
{
    static uint8_t first_power_on = 1; // 1表示首次上电
    
    /* 状态切换处理 - 包括首次上电处理 */
    if (first_power_on || g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_power_on->entry_count++;
        g_SystemStatus.power.bits.power_stable = 0;

        /* 上电时，对于电机部分执行故障复位和系统参数重置 */
        //fnSysFaultReset();
		//fnSystemResetParam();
        /* 上电时，使能ADC采样,进行参数检查 */
        //ADC_Enable_Flag();
        /* 上电时，直接使能28V电源正常，因为28V电源无法采样 */
        g_SystemStatus.power.bits.power_28v_ok = 1;

        g_ctx_power_on->last_update_time = Get_Runtime_Ms();
        first_power_on = 0; // 清除首次上电标志
#if USB_PRINTF_ENABLE
        Usb_printf_direct(LOG_COLOR_GREEN, "开始上电检查\n");
#endif
    }
}

/**
 * @brief  外部初始化状态处理函数
 * @note   负责执行初始化过程
 * @param  None
 * @retval None
 */

uint8_t g_External_Init_Count = 0;

static void State_InitExternal_Handler(void)
{   

    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_External_Init_Count++;
        /* 外设初始化延时50ms，等待外设稳定 */
        
        /* 目前的设计user_init放在主循环之外进行初始化，这里只进行参数初始化外 */
        /* 系统参数初始化 START */
        
        /* 自检初始化 */
        gSelfTest_Manager.Init();

        /* 系统参数初始化 END */
        g_SystemStatus.work.bits.sys_init = 1; /* 系统初始化完成 */
        g_ctx_init_external->last_update_time = Get_Runtime_Ms();  // 记录初始化时间
        
#if USB_PRINTF_ENABLE
        Usb_printf_direct(LOG_COLOR_GREEN, "外设初始化完成\n");
#endif
    }
    /* 自循环时不做处理 */
}

/**
 * @brief  自检状态处理函数
 * @note   负责执行自检过程和状态管理
 * @param  None
 * @retval None
 */
static void State_SelfTest_Handler(void)
{
    static uint32_t selftest_start_time = 0;
    uint32_t current_time = Get_Runtime_Ms();
    
    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_self_test->entry_count++;
        g_ctx_self_test->flag = 0;
        selftest_start_time = current_time;
        
        // 重置自检相关标志
        g_SystemStatus.work.bits.self_test = 1;      // 自检进行中
        g_SystemStatus.work.bits.self_test_ok = 0;   // 自检未通过
        g_SystemStatus.work.bits.sys_ready = 0;      // 系统未就绪
        
        g_ctx_self_test->last_update_time = current_time;
        
#if USB_PRINTF_ENABLE
        Usb_printf_direct(LOG_COLOR_GREEN, "开始系统自检\n");
#endif
    }
    
    /* 周期性自检任务执行(100ms) */
    if ((current_time - g_ctx_self_test->last_update_time) >= 100) {
        
        // 执行自检管理器
        ADC_TestValue_t adc_test_data;
        
        // 从ADC结果获取测试数据
        adc_test_data.bus_voltage = gADC_Result.bus_voltage;
        adc_test_data.uv_voltage = gADC_Result.uv_voltage;
        adc_test_data.uw_voltage = gADC_Result.uw_voltage;
        adc_test_data.v28_voltage = gADC_Result.v28_voltage;
        adc_test_data.temp_mot1 = gADC_Result.temp_mot1;
        adc_test_data.temp_mot2 = gADC_Result.temp_mot2;
        adc_test_data.temp_cap1 = gADC_Result.temp_cap1;
        adc_test_data.temp_cap2 = gADC_Result.temp_cap2;
        adc_test_data.temp_bmf_u = gADC_Result.temp_bmf_u;
        adc_test_data.temp_bmf_v = gADC_Result.temp_bmf_v;
        adc_test_data.temp_bmf_w = gADC_Result.temp_bmf_w;
        adc_test_data.temp_PT100_6 = gADC_Result.temp_PT100_6;
        adc_test_data.temp_PT1000_7 = gADC_Result.temp_PT1000_7;
        adc_test_data.u_current = gADC_Result.current_u;
        adc_test_data.v_current = gADC_Result.current_v;
        adc_test_data.w_current = gADC_Result.current_w;
        
        // 执行自检
        SelfTestResult result = SelfTest_Manager(SELF_TEST_OP_EXECUTE, &gSelfTest_Manager, &adc_test_data);
        
        // 根据自检结果更新状态
        if (result == SELF_TEST_PASS) {
            g_SystemStatus.work.bits.self_test_ok = 1;  // 自检通过
            g_SystemStatus.work.bits.self_test = 0;     // 自检完成
#if USB_PRINTF_ENABLE
            Usb_printf_direct(LOG_COLOR_GREEN, "系统自检通过\n");
#endif
        } else if (result == SELF_TEST_FAIL) {
            g_SystemStatus.work.bits.self_test_ok = 0;  // 自检失败
            g_SystemStatus.work.bits.self_test = 0;     // 自检完成
#if USB_PRINTF_ENABLE
            Usb_printf_direct(LOG_COLOR_RED, "系统自检失败\n");
#endif
        }
        // SELF_TEST_TIMEOUT的情况继续等待
        
        g_ctx_self_test->last_update_time = current_time;
    }
}

/**
 * @brief  系统保持状态处理函数
 * @note   负责执行系统保持过程
 * @param  None
 * @retval None
 */
static void State_Hold_Handler(void)
{
    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_hold->entry_count++;
        g_ctx_hold->flag = 0;


#if USB_PRINTF_ENABLE
        Usb_printf_direct(LOG_COLOR_GREEN, "进入系统保持，等待监测正常\n");
#endif
    }
    
    /* 周期性任务执行(10ms) */
    uint32_t current_time = Get_Runtime_Ms();
    if ((current_time - g_ctx_hold->last_update_time) >= 10) {
        
        
        g_ctx_hold->last_update_time = current_time;
    }
}

/**
 * @brief  系统就绪状态处理函数
 * @note   区分切换进入和循环进入
 * @param  None
 * @retval None
 */
static void State_Ready_Handler(void)
{
    uint32_t current_time = Get_Runtime_Ms();
    
    /* 状态切换进入处理 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        // 检查系统状态运行且是运行状态切入就绪
        if (g_SystemStatus.work.bits.sys_running && g_state_machine_ctx.current_state == STATE_RUNNING) {
            // 减速停机逻辑接口
            Execute_Deceleration_Shutdown();
            g_SystemStatus.work.bits.sys_running = 0;
            g_SystemStatus.work.bits.sys_ready = 1;
        } else {
            // 自检进入
            g_SystemStatus.work.bits.sys_ready = 1;
            g_SystemStatus.work.bits.sys_running = 0;
        }
        
        g_ctx_ready->last_update_time = current_time;
    }
    /* 自循环处理 */
    else {
        /* 周期性任务执行(10ms) */
        if ((current_time - g_ctx_ready->last_update_time) >= 10) {
            // 10ms执行一次
        }
    }
}

/**
 * @brief  系统运行状态处理函数
 * @note   负责执行运行状态过程
 * @param  None
 * @retval None
 */
static void State_Running_Handler(void)
{
    uint32_t current_time = Get_Runtime_Ms();

    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_running->entry_count++;
        g_ctx_running->flag = 0;
        g_SystemStatus.work.bits.sys_ready = 0;
        g_SystemStatus.work.bits.sys_running = 1;

        Motor_Control_Execute();
    }
    
    /* 周期性任务执行*/
    if ((current_time - g_ctx_running->last_update_time) >= 100) {  // 100ms
        // 更新功率计算
        Update_Motion_Status();
        g_ctx_running->last_update_time = current_time;
    }
}

/**
 * @brief  故障状态处理函数
 * @note   负责执行故障处理过程
 * @param  None
 * @retval None
 */
static void State_Fault_Handler(void)
{
    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_fault->entry_count++;
        g_ctx_fault->flag = 0;
        g_SystemStatus.work.bits.sys_fault = 1;
        
        // 执行故障保护
        Prepare_For_Fault_State();
        // 关闭所有输出
        Disable_All_Outputs();

        // 记录故障信息
        Log_Warning_Info();
        
        // 发送故障信息到上位机
        Send_Warning_To_Host();

    }
    
    /* 周期性任务执行(10ms) */
    uint32_t current_time = Get_Runtime_Ms();
    if ((current_time - g_ctx_fault->last_update_time) >= 10) {
        

        g_ctx_fault->last_update_time = current_time;
    }
}




/*----------------------- 第五部分：系统功能函数接口 -----------------------*/

void System_ProtectCheck(void)
{
    // TODO: 实现系统保护检查逻辑
}

bool_t System_SelfTest(void)
{
    SelfTestResult result;
    bool_t test_pass = TRUE;
    
    // 1. 初始化自检管理器
    SelfTest_Init();
    
    // 3. 执行系统级别自检
    
    if (result != SELF_TEST_PASS) {
        // 系统级别自检失败
        g_SystemStatus.work.bits.self_test_ok = 0;
        g_SystemStatus.work.bits.sys_fault = 1;
        
        // 根据自检管理器中的失败项确定故障类型
        if (gSelfTest_Manager.items.bits.clock) {
            g_SystemStatus.sys_fault.bits.extreme = 1;  // 时钟故障是极端故障
        }
        if (gSelfTest_Manager.items.bits.bus_voltage || 
            gSelfTest_Manager.items.bits.v28) {
            g_SystemStatus.warn_fault.bus_voltage = 1;  // 电压故障
        }
        if (gSelfTest_Manager.items.bits.motor_temp || 
            gSelfTest_Manager.items.bits.cap_temp || 
            gSelfTest_Manager.items.bits.sic_temp) {
            g_SystemStatus.warn_fault.cap_temp = 1;  // 温度故障
        }
        
        test_pass = FALSE;
    } else {
        // 系统级别自检通过，继续检查是否有告警
        if (gSelfTest_Manager.output.bits.warning) {
            // 有告警但可以运行
            g_SystemStatus.status.flags.is_warning = 1;
        }
        
        if (gSelfTest_Manager.output.bits.power_limit) {
            g_SystemStatus.status.flags.is_power_limited = 1;
        }
        
        // 自检通过
        g_SystemStatus.work.bits.self_test_ok = 1;
        test_pass = TRUE;
    }
    
    // 5. 更新系统状态标志
    if (test_pass) {
        g_SystemStatus.work.bits.sys_ready = 1;  // 系统就绪
    } else {
        g_SystemStatus.work.bits.sys_ready = 0;  // 系统未就绪
    }
    
    return test_pass;
}

/**
 * @brief  获取最高警告等级
 * @note   检查所有警告并返回最高等级
 * @param  None
 * @retval WarnLevel_TypeDef: 当前最高警告等级
 */
WarnLevel_TypeDef Get_Max_Warning_Level(void)
{
    WarnLevel_TypeDef max_level = WARN_LEVEL_NONE;
    
    // 检查各类警告等级
    if (g_SystemStatus.warn.current > max_level)
        max_level = g_SystemStatus.warn.current;
    
    if (g_SystemStatus.warn.bus_voltage > max_level)
        max_level = g_SystemStatus.warn.bus_voltage;
    
    if (g_SystemStatus.warn.motor_temp > max_level)
        max_level = g_SystemStatus.warn.motor_temp;
    
    if (g_SystemStatus.warn.driver_temp > max_level)
        max_level = g_SystemStatus.warn.driver_temp;
    
    if (g_SystemStatus.warn.cap_temp > max_level)
        max_level = g_SystemStatus.warn.cap_temp;
    
    return max_level;
}

/**
 * @brief  检查故障是否可恢复
 * @note   负责检查状态，不执行恢复操作
 * @param  None
 * @retval bool_t: TRUE-可恢复, FALSE-不可恢复
 */
bool_t Is_Fault_Recoverable(void)
{
    // 检查是否存在不可恢复的故障
    if (Check_Fault_Unrecoverable() == CHECK_PASS) {
        return FALSE;
    }
    
    // 检查故障是否已清除
    if (!g_SystemStatus.work.bits.sys_fault) {
        return TRUE;
    }
    
    return FALSE;
}

/**
 * @brief  尝试故障恢复
 * @note   执行故障恢复操作
 * @param  None
 * @retval ret_t: RET_OK-恢复成功, RET_ERROR-恢复失败
 */
ret_t Attempt_Fault_Recovery(void)
{
    // 清除可恢复的故障标志
    if (Is_Fault_Recoverable()) {
        g_SystemStatus.work.bits.sys_fault = 0;
        return RET_OK;
    }
    return RET_ERROR;
}

void Perform_Safe_Shutdown(void)
{
    // TODO: 实现安全停机逻辑
    // 1. 关闭所有输出
    // 2. 保存必要的状态信息
    // 3. 执行硬件保护措施
}


/**
 * @brief  更新系统告警状态
 * @note   检查告警条件并更新相应的状态标志
 * @param  None
 * @retval None
 */
void Update_Warning_Status(void)
{
    // 更新告警标志
    if (Check_Warning_Condition() == CHECK_PASS) {
        g_SystemStatus.status.flags.is_warning = 1;
        
        // 获取最高告警等级
        WarnLevel_TypeDef max_level = Get_Max_Warning_Level();
        g_SystemStatus.status.warn_level = max_level;
        
        // 根据告警等级执行相应操作
        if (max_level >= WARN_LEVEL_2) {
            g_SystemStatus.status.flags.is_power_limited = 1;
            Limit_Output_Power();
        }
    } else {
        g_SystemStatus.status.flags.is_warning = 0;
        g_SystemStatus.status.flags.is_power_limited = 0;
        g_SystemStatus.status.warn_level = WARN_LEVEL_NONE;
    }
}

/*----------------------- 第六部分：辅助函数实现 -----------------------*/


/**
 * @brief  外设初始化
 * @retval bool_t: true-初始化成功, false-初始化失败
 */
bool_t External_Devices_Init(void)
{
    // TODO: 实现外设初始化
    return true;
}

/**
 * @brief  电机控制执行
 * @retval None
 */
void Motor_Control_Execute(void)
{
    // TODO: 实现电机控制
}

/**
 * @brief  更新运动状态
 * @retval None
 */
void Update_Motion_Status(void)
{
    // TODO: 实现运动状态更新
}

/**
 * @brief  记录告警信息
 * @retval None
 */
void Log_Warning_Info(void)
{
    // TODO: 实现告警信息记录
}

/**
 * @brief  发送告警信息到上位机
 * @retval None
 */
void Send_Warning_To_Host(void)
{
    // TODO: 实现告警信息发送
}

/**
 * @brief  监控告警参数
 * @retval None
 */
void Monitor_Warning_Parameters(void)
{
    // TODO: 实现告警参数监控
}

/**
 * @brief  限制输出功率
 * @retval None
 */
void Limit_Output_Power(void)
{
    // TODO: 实现输出功率限制
}

/**
 * @brief  准备进入故障状态
 * @retval None
 */
void Prepare_For_Fault_State(void)
{
    // TODO: 实现故障状态准备
}

/**
 * @brief  检查上位机停机指令
 * @note   检查是否收到上位机的停机命令
 * @param  None
 * @retval check_t: CHECK_PASS-收到停机指令, CHECK_FAIL-未收到停机指令
 */
check_t Check_Host_Stop_Command(void)
{
    // TODO: 实现上位机停机指令检查
    // 1. 检查通信数据中的停机指令标志
    // 2. 检查其他停机条件
    
    return CHECK_FAIL;  // 暂时返回未收到停机指令
}

/**
 * @brief  检查系统参数是否恢复正常
 * @retval check_t: CHECK_PASS-系统正常, CHECK_FAIL-系统异常
 */
check_t Check_SystemNormal(void)
{
    // 1. 直接检查供电标志位
    if (!g_SystemStatus.power.bits.power_28v_ok || 
        !g_SystemStatus.power.bits.power_700v_ok) {
        return CHECK_FAIL;
    }
    
    // 2. 检查故障标志（三级告警故障和系统级故障）
    if (Check_WarnFault_Any(&g_SystemStatus.warn_fault) || g_SystemStatus.sys_fault.all) {
        return CHECK_FAIL;
    }
    
    // 3. 检查并更新系统工况
    //Update_Operating_Condition();
    
    return CHECK_FAIL;
}


/**
 * @brief  检查故障是否无法排除
 * @note   检查系统故障是否已超过最大重试次数或存在硬件永久性故障
 * @param  None
 * @retval check_t: CHECK_PASS-故障无法排除, CHECK_FAIL-故障可能恢复
 */
check_t Check_Fault_Unrecoverable(void)
{
    static uint32_t state_start_time = 0;
    
    /* 检查是否存在极端故障（如硬件损坏） */
    if (g_SystemStatus.sys_fault.bits.extreme) {
        return CHECK_PASS;
    }
    
    /* 检查当前状态持续时间是否超过3分钟 */
    if (state_start_time == 0) {
        state_start_time = Get_Runtime_Ms();  // 首次进入状态时记录时间
        return CHECK_FAIL;
    }
    
    if (Get_Runtime_Ms() - state_start_time >= MAX_STATE_TIME) {
        state_start_time = 0;
        return CHECK_PASS;
    }
    
    return CHECK_FAIL;
}

/**
 * @brief  检查28V电源状态
 * @note   检查控制电源标志位
 * @param  None
 * @retval check_t: CHECK_PASS-电源正常, CHECK_FAIL-电源异常
 */
check_t Check_28V_Power(void)
{
    return g_SystemStatus.power.bits.power_28v_ok ? CHECK_PASS : CHECK_FAIL;
}

/**
 * @brief  检查700V电源状态
 * @note   检查母线电压标志位
 * @param  None
 * @retval check_t: CHECK_PASS-电源正常, CHECK_FAIL-电源异常
 */
check_t Check_Bus_Power(void)
{
    return g_SystemStatus.power.bits.power_700v_ok ? CHECK_PASS : CHECK_FAIL;
}

void Disable_All_Outputs(void)
{
    // TODO: 实现禁用所有输出
}
void Update_Power_Status(float power)
{
    // TODO: 实现电源状态更新
}

/**
 * @brief  更新系统工况状态
 * @note   根据系统参数判断当前工况并设置相应限制
 * @param  None
 * @retval None
 */
static void Update_Operating_Condition(void)
{
    // 工况判断示例
    /*
    if (Get_Motor_Speed() > SPEED_THRESHOLD_HIGH) {
        // 高速工况处理
        g_SystemStatus.condition.high_speed = 1;
        Limit_Power_HighSpeed();
    }
    
    if (Get_System_Temperature() > TEMP_THRESHOLD_HIGH) {
        // 高温工况处理
        g_SystemStatus.condition.high_temp = 1;
        Limit_Power_HighTemp();
    }
    
    if (Get_Load_Current() > CURRENT_THRESHOLD_HIGH) {
        // 重载工况处理
        g_SystemStatus.condition.heavy_load = 1;
        Limit_Power_HeavyLoad();
    }
    */
}

void Execute_Deceleration_Shutdown(void) {
    // 实现具体的减速停机逻辑
    // 逐步降低电机速度，转速为0进入就绪状态
    // for (int speed = MAX_SPEED; speed > 0; speed -= DECELERATION_STEP) {
    //     Set_Motor_Speed(speed);
    //     Delay_ms(100); // 等待100ms
    // }
    Disable_All_Outputs(); // 停止所有输出
}

void Update_System_Status(void)
{

}


/**
 * @brief  检查是否达到二级警告
 * @param  None
 * @retval check_t: CHECK_PASS-达到二级警告, CHECK_FAIL-未达到二级警告
 */
check_t Check_WarningLevel2(void)
{
    return (Get_Max_Warning_Level() >= WARN_LEVEL_2) ? CHECK_PASS : CHECK_FAIL;
}

/**
 * @brief  检查警告是否已解除
 * @param  None
 * @retval check_t: CHECK_PASS-警告已解除, CHECK_FAIL-警告未解除
 */
check_t Check_Warning_Cleared(void)
{
    return (Get_Max_Warning_Level() == WARN_LEVEL_NONE) ? CHECK_PASS : CHECK_FAIL;
}

/**
 * @brief  检查是否存在警告条件
 * @note   负责检查状态，不执行警告处理
 * @param  None
 * @retval check_t: CHECK_PASS-存在警告, CHECK_FAIL-无警告
 */
check_t Check_Warning_Condition(void)
{
    // 检查各类警告条件

    return CHECK_FAIL;
}

/**
 * @brief  关机状态处理函数
 * @note   负责执行关机过程
 * @param  None
 * @retval None
 */
static void State_Shutdown_Handler(void)
{
    /* 首次进入状态的初始化 */
    if (g_state_machine_ctx.current_state != g_state_machine_ctx.previous_state) {
        g_ctx_shutdown->entry_count++;
        g_ctx_shutdown->flag = 0;
        g_SystemStatus.work.bits.sys_stop = 1;
        
        // 执行安全关机程序
        Perform_Safe_Shutdown();
    }
    
    /* 保持在关机状态，等待外部复位或重新上电 */
}