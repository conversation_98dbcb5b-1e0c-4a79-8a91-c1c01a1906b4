/*
	Copyright 2016 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
    */

/*
 * 电机控制接口头文件 (Motor Control Interface Header)
 *
 * 本文件定义了VESC电机控制系统的统一接口层，提供了对底层电机控制算法的
 * 高级抽象。该接口层支持多种电机控制模式和双电机配置。
 *
 * 主要功能模块：
 * 1. 电机控制模式 - 占空比、电流、速度、位置控制
 * 2. 状态监测 - 电流、电压、温度、转速等参数读取
 * 3. 故障管理 - 故障检测、报告和恢复
 * 4. 配置管理 - 电机参数配置和校验
 * 5. 统计功能 - 运行统计和数据记录
 * 6. 双电机支持 - 多电机协调控制
 *
 * 设计特点：
 * - 硬件无关性：统一接口适配不同硬件平台
 * - 线程安全：支持多线程并发访问
 * - 实时性：高效的中断处理和数据采集
 * - 可扩展性：模块化设计便于功能扩展
 */

#ifndef MC_INTERFACE_H_
#define MC_INTERFACE_H_

#include "conf_general.h"
#include "hw.h"
#include "datatypes.h"

// 函数声明 (Function Declarations)
/*
 * ============================================================================
 * 系统初始化和配置管理函数 (System Initialization & Configuration Management)
 * ============================================================================
 */

/**
 * 电机控制接口初始化
 * 初始化电机控制系统，包括硬件配置、中断设置、状态变量等
 */
void mc_interface_init(void);

/**
 * 获取当前活动电机编号
 * @return 当前电机编号 (0=电机1, 1=电机2)
 */
int mc_interface_motor_now(void);

/**
 * 选择电机线程
 * 在双电机系统中切换当前操作的电机
 * @param motor 电机编号 (0=电机1, 1=电机2)
 */
void mc_interface_select_motor_thread(int motor);

/**
 * 获取当前电机线程编号
 * @return 当前电机线程编号
 */
int mc_interface_get_motor_thread(void);

/**
 * 获取电机配置参数
 * @return 指向当前电机配置的常量指针
 */
const volatile mc_configuration* mc_interface_get_configuration(void);

/**
 * 设置电机配置参数
 * 更新电机配置并应用到控制系统
 * @param configuration 新的电机配置参数
 */
void mc_interface_set_configuration(mc_configuration *configuration);

/**
 * 计算配置参数CRC校验码
 * 用于验证配置参数的完整性
 * @param conf 配置参数结构体指针
 * @param is_motor_2 是否为第二个电机
 * @return CRC校验码
 */
unsigned mc_interface_calc_crc(mc_configuration* conf, bool is_motor_2);

/**
 * 检查直流校准是否完成
 * 直流校准用于消除ADC采样的直流偏移
 * @return true=校准完成, false=校准进行中
 */
bool mc_interface_dccal_done(void);

/**
 * 设置PWM回调函数
 * 注册在PWM中断中执行的用户回调函数
 * @param p_func 回调函数指针
 */
void mc_interface_set_pwm_callback(void (*p_func)(void));

/*
 * ============================================================================
 * 线程安全和互斥控制函数 (Thread Safety & Mutex Control)
 * ============================================================================
 */

/**
 * 锁定电机控制接口
 * 防止其他线程同时访问电机控制功能
 */
void mc_interface_lock(void);

/**
 * 解锁电机控制接口
 * 允许其他线程访问电机控制功能
 */
void mc_interface_unlock(void);

/**
 * 一次性锁定覆盖
 * 临时覆盖锁定状态，执行一次操作后自动恢复
 */
void mc_interface_lock_override_once(void);

/*
 * ============================================================================
 * 状态监测和故障管理函数 (Status Monitoring & Fault Management)
 * ============================================================================
 */
/**
 * 获取当前故障代码
 * @return 故障代码枚举值
 */
mc_fault_code mc_interface_get_fault(void);

/**
 * 将故障代码转换为字符串描述
 * @param fault 故障代码
 * @return 故障描述字符串
 */
const char* mc_interface_fault_to_string(mc_fault_code fault);

/**
 * 获取电机当前状态
 * @return 电机状态枚举值 (停止/运行/故障等)
 */
mc_state mc_interface_get_state(void);

/**
 * 获取当前控制模式
 * @return 控制模式枚举值 (占空比/电流/速度/位置)
 */
mc_control_mode mc_interface_get_control_mode(void);
/*
 * ============================================================================
 * 电机控制命令函数 (Motor Control Command Functions)
 * ============================================================================
 */

/**
 * 设置占空比控制
 *
 * 占空比控制是最基本的电机控制方式，直接控制PWM的占空比。
 * 占空比范围：-1.0 到 +1.0
 * - 正值：正向旋转
 * - 负值：反向旋转
 * - 0：停止
 *
 * 数学关系：V_avg = duty_cycle × V_bus
 * 其中 V_avg 为平均输出电压，V_bus 为总线电压
 *
 * @param dutyCycle 占空比 (-1.0 到 +1.0)
 */
void mc_interface_set_duty(float dutyCycle);

/**
 * 设置占空比控制(无斜坡)
 * 立即设置占空比，不使用斜坡过渡
 * @param dutyCycle 占空比 (-1.0 到 +1.0)
 */
void mc_interface_set_duty_noramp(float dutyCycle);

/**
 * 设置PID速度控制
 *
 * 使用PID控制器实现速度闭环控制。
 * 控制方程：u(t) = Kp×e(t) + Ki×∫e(t)dt + Kd×de(t)/dt
 * 其中 e(t) = rpm_set - rpm_actual
 *
 * @param rpm 目标转速 (RPM，正值=正转，负值=反转)
 */
void mc_interface_set_pid_speed(float rpm);

/**
 * 设置PID位置控制
 *
 * 使用PID控制器实现位置闭环控制。
 * 位置单位为弧度，支持多圈定位。
 *
 * @param pos 目标位置 (弧度)
 */
void mc_interface_set_pid_pos(float pos);

/**
 * 设置电流控制
 *
 * 直接控制电机电流，提供精确的转矩控制。
 * 转矩与电流成正比：T = Kt × I
 * 其中 Kt 为转矩常数，I 为电流
 *
 * @param current 目标电流 (A，正值=正转转矩，负值=反转转矩)
 */
void mc_interface_set_current(float current);

/**
 * 设置制动电流
 *
 * 设置再生制动电流，将电机动能转换为电能回馈到电池。
 * 制动力与制动电流成正比。
 *
 * @param current 制动电流 (A，正值)
 */
void mc_interface_set_brake_current(float current);

/**
 * 设置相对电流控制
 *
 * 使用相对值设置电流，范围 -1.0 到 +1.0
 * 实际电流 = val × 最大电流限制
 *
 * @param val 相对电流值 (-1.0 到 +1.0)
 */
void mc_interface_set_current_rel(float val);

/**
 * 设置相对制动电流
 * @param val 相对制动电流值 (0.0 到 1.0)
 */
void mc_interface_set_brake_current_rel(float val);

/**
 * 设置手刹电流
 *
 * 设置静态制动电流，用于保持电机位置不动。
 * 常用于坡道停车或精确定位。
 *
 * @param current 手刹电流 (A)
 */
void mc_interface_set_handbrake(float current);

/**
 * 设置相对手刹电流
 * @param val 相对手刹电流值 (0.0 到 1.0)
 */
void mc_interface_set_handbrake_rel(float val);

/*
 * ============================================================================
 * 开环控制函数 (Open-Loop Control Functions)
 * ============================================================================
 */

/**
 * 设置开环电流控制
 *
 * 开环控制不依赖位置传感器，通过估算的转子位置进行控制。
 * 适用于启动、低速运行或传感器故障时的备用控制。
 *
 * @param current 电流幅值 (A)
 * @param rpm 估算转速 (RPM)
 */
void mc_interface_set_openloop_current(float current, float rpm);

/**
 * 设置开环相位控制
 *
 * 直接指定转子电角度进行开环控制。
 *
 * @param current 电流幅值 (A)
 * @param phase 转子电角度 (弧度)
 */
void mc_interface_set_openloop_phase(float current, float phase);

/**
 * 设置开环占空比控制
 * @param dutyCycle 占空比 (-1.0 到 +1.0)
 * @param rpm 估算转速 (RPM)
 */
void mc_interface_set_openloop_duty(float dutyCycle, float rpm);

/**
 * 设置开环占空比相位控制
 * @param dutyCycle 占空比 (-1.0 到 +1.0)
 * @param phase 转子电角度 (弧度)
 */
void mc_interface_set_openloop_duty_phase(float dutyCycle, float phase);

/*
 * ============================================================================
 * 电机状态控制函数 (Motor State Control Functions)
 * ============================================================================
 */

/**
 * 设置转速计数值
 *
 * 手动设置转速计数器的值，用于位置校准或重置。
 *
 * @param steps 转速计数值 (步数)
 * @return 设置后的实际计数值
 */
int mc_interface_set_tachometer_value(int steps);

/**
 * 立即制动电机
 *
 * 紧急制动功能，立即停止电机运行。
 * 使用最大制动电流快速停止电机。
 */
void mc_interface_brake_now(void);

/**
 * 释放电机
 *
 * 停止所有控制输出，让电机自由转动。
 * 电机将在摩擦力作用下自然停止。
 */
void mc_interface_release_motor(void);

/**
 * 强制释放电机
 *
 * 覆盖所有保护机制，强制释放电机控制。
 * 仅在紧急情况下使用。
 */
void mc_interface_release_motor_override(void);

/**
 * 等待电机释放完成
 *
 * 阻塞等待直到电机完全释放或超时。
 *
 * @param timeout 超时时间 (秒)
 * @return true=成功释放, false=超时
 */
bool mc_interface_wait_for_motor_release(float timeout);
/*
 * ============================================================================
 * 状态读取函数 (Status Reading Functions)
 * ============================================================================
 */

/**
 * 获取设定的占空比
 * @return 当前设定的占空比值 (-1.0 到 +1.0)
 */
float mc_interface_get_duty_cycle_set(void);

/**
 * 获取实际占空比
 * @return 当前实际的占空比值 (-1.0 到 +1.0)
 */
float mc_interface_get_duty_cycle_now(void);

/**
 * 获取当前采样频率
 * @return 当前ADC采样频率 (Hz)
 */
float mc_interface_get_sampling_frequency_now(void);

/**
 * 获取电机转速
 *
 * 转速计算基于编码器或观测器反馈：
 * RPM = (ω × 60) / (2π)
 * 其中 ω 为角速度 (rad/s)
 *
 * @return 电机转速 (RPM，正值=正转，负值=反转)
 */
float mc_interface_get_rpm(void);

/*
 * ============================================================================
 * 能量统计函数 (Energy Statistics Functions)
 * ============================================================================
 */

/**
 * 获取累计安时数(放电)
 *
 * 安时数计算：Ah = ∫I(t)dt / 3600
 * 其中 I(t) 为瞬时电流，积分时间单位为秒
 *
 * @param reset 是否重置计数器
 * @return 累计安时数 (Ah)
 */
float mc_interface_get_amp_hours(bool reset);

/**
 * 获取累计安时数(充电)
 *
 * 记录再生制动等充电过程的安时数
 *
 * @param reset 是否重置计数器
 * @return 累计充电安时数 (Ah)
 */
float mc_interface_get_amp_hours_charged(bool reset);

/**
 * 获取累计瓦时数(放电)
 *
 * 瓦时数计算：Wh = ∫P(t)dt / 3600 = ∫V(t)×I(t)dt / 3600
 * 其中 P(t) 为瞬时功率，V(t) 为电压，I(t) 为电流
 *
 * @param reset 是否重置计数器
 * @return 累计瓦时数 (Wh)
 */
float mc_interface_get_watt_hours(bool reset);

/**
 * 获取累计瓦时数(充电)
 * @param reset 是否重置计数器
 * @return 累计充电瓦时数 (Wh)
 */
float mc_interface_get_watt_hours_charged(bool reset);

/*
 * ============================================================================
 * 电流测量函数 (Current Measurement Functions)
 * ============================================================================
 */

/**
 * 获取总电机电流(瞬时值)
 *
 * 三相电流的矢量和：I_total = √(I_α² + I_β²)
 * 其中 I_α, I_β 为αβ坐标系下的电流分量
 *
 * @return 总电机电流幅值 (A)
 */
float mc_interface_get_tot_current(void);

/**
 * 获取总电机电流(滤波值)
 *
 * 经过低通滤波的电机电流，减少噪声影响
 *
 * @return 滤波后的总电机电流 (A)
 */
float mc_interface_get_tot_current_filtered(void);

/**
 * 获取有向电机电流(瞬时值)
 *
 * 考虑方向的电机电流：
 * - 正值：电机模式(消耗功率)
 * - 负值：发电机模式(再生制动)
 *
 * @return 有向电机电流 (A)
 */
float mc_interface_get_tot_current_directional(void);

/**
 * 获取有向电机电流(滤波值)
 * @return 滤波后的有向电机电流 (A)
 */
float mc_interface_get_tot_current_directional_filtered(void);

/**
 * 获取输入电流(瞬时值)
 *
 * 从电池或电源输入的电流
 *
 * @return 输入电流 (A)
 */
float mc_interface_get_tot_current_in(void);

/**
 * 获取输入电流(滤波值)
 * @return 滤波后的输入电流 (A)
 */
float mc_interface_get_tot_current_in_filtered(void);

/**
 * 获取输入电压(滤波值)
 *
 * 经过滤波的总线电压，用于功率计算和保护
 *
 * @return 滤波后的输入电压 (V)
 */
float mc_interface_get_input_voltage_filtered(void);

/*
 * ============================================================================
 * 位置和转速测量函数 (Position & Speed Measurement Functions)
 * ============================================================================
 */

/**
 * 获取转速计数值
 *
 * 转速计基于编码器脉冲或电角度计算：
 * 计数值 = (电角度 / 2π) × 极对数 × 6
 *
 * @param reset 是否重置计数器
 * @return 转速计数值 (步数)
 */
int mc_interface_get_tachometer_value(bool reset);

/**
 * 获取转速计绝对值
 *
 * 只记录转动的绝对量，不考虑方向
 *
 * @param reset 是否重置计数器
 * @return 转速计绝对计数值 (步数)
 */
int mc_interface_get_tachometer_abs_value(bool reset);

/*
 * ============================================================================
 * 性能监测函数 (Performance Monitoring Functions)
 * ============================================================================
 */

/**
 * 获取上次注入ADC中断持续时间
 *
 * 用于监测ADC中断处理的实时性能
 *
 * @return 中断持续时间 (微秒)
 */
float mc_interface_get_last_inj_adc_isr_duration(void);

/**
 * 读取并重置平均电机电流
 *
 * 获取一段时间内的平均电机电流并重置累加器
 *
 * @return 平均电机电流 (A)
 */
float mc_interface_read_reset_avg_motor_current(void);

/**
 * 读取并重置平均输入电流
 * @return 平均输入电流 (A)
 */
float mc_interface_read_reset_avg_input_current(void);

/**
 * 读取并重置平均d轴电流
 *
 * d轴电流主要用于磁通控制和弱磁控制
 *
 * @return 平均d轴电流 (A)
 */
float mc_interface_read_reset_avg_id(void);

/**
 * 读取并重置平均q轴电流
 *
 * q轴电流直接对应电机转矩：T = Kt × Iq
 *
 * @return 平均q轴电流 (A)
 */
float mc_interface_read_reset_avg_iq(void);

/**
 * 读取并重置平均d轴电压
 * @return 平均d轴电压 (V)
 */
float mc_interface_read_reset_avg_vd(void);

/**
 * 读取并重置平均q轴电压
 * @return 平均q轴电压 (V)
 */
float mc_interface_read_reset_avg_vq(void);

/*
 * ============================================================================
 * 位置控制相关函数 (Position Control Related Functions)
 * ============================================================================
 */

/**
 * 获取PID位置设定值
 * @return 位置设定值 (弧度)
 */
float mc_interface_get_pid_pos_set(void);

/**
 * 获取PID位置当前值
 * @return 位置当前值 (弧度)
 */
float mc_interface_get_pid_pos_now(void);

/**
 * 更新PID位置偏移
 *
 * 用于位置零点校准和偏移补偿
 *
 * @param angle_now 当前角度 (弧度)
 * @param store 是否存储到非易失性存储器
 */
void mc_interface_update_pid_pos_offset(float angle_now, bool store);

/**
 * 获取上次采样ADC中断持续时间
 * @return 中断持续时间 (微秒)
 */
float mc_interface_get_last_sample_adc_isr_duration(void);

/**
 * 采样数据打印
 *
 * 用于调试和数据分析，可以采样各种电机参数
 *
 * @param mode 采样模式
 * @param len 采样长度
 * @param decimation 抽取比例
 * @param raw 是否为原始数据
 * @param reply_func 回复函数指针
 */
void mc_interface_sample_print_data(debug_sampling_mode mode, uint16_t len, uint8_t decimation, bool raw,
		void(*reply_func)(unsigned char *data, unsigned int len));

/*
 * ============================================================================
 * 温度监测函数 (Temperature Monitoring Functions)
 * ============================================================================
 */

/**
 * 获取MOSFET温度(滤波值)
 *
 * MOSFET温度监测用于过热保护
 *
 * @return MOSFET温度 (°C)
 */
float mc_interface_temp_fet_filtered(void);

/**
 * 获取电机温度(滤波值)
 *
 * 电机温度监测用于过热保护和温度补偿
 *
 * @return 电机温度 (°C)
 */
float mc_interface_temp_motor_filtered(void);

/*
 * ============================================================================
 * 电池和运动状态函数 (Battery & Motion Status Functions)
 * ============================================================================
 */

/**
 * 获取电池电量百分比
 *
 * 基于电压和容量计算电池剩余电量
 *
 * @param wh_left 输出参数：剩余瓦时数指针
 * @return 电池电量百分比 (0.0-1.0)
 */
float mc_interface_get_battery_level(float *wh_left);

/**
 * 获取运动速度
 *
 * 基于电机转速和轮径计算的线速度：
 * v = ω × r = (RPM × 2π × r) / 60
 * 其中 r 为轮径
 *
 * @return 运动速度 (m/s)
 */
float mc_interface_get_speed(void);

/**
 * 获取运动距离(有向)
 *
 * 考虑方向的累计运动距离
 *
 * @return 运动距离 (m，正值=前进，负值=后退)
 */
float mc_interface_get_distance(void);

/**
 * 获取运动距离(绝对值)
 *
 * 不考虑方向的累计运动距离
 *
 * @return 运动距离绝对值 (m)
 */
float mc_interface_get_distance_abs(void);

/*
 * ============================================================================
 * 系统设置和GNSS函数 (System Setup & GNSS Functions)
 * ============================================================================
 */

/**
 * 获取系统设置值
 *
 * 包含电机检测、编码器设置等系统配置信息
 *
 * @return 系统设置值结构体
 */
setup_values mc_interface_get_setup_values(void);

/**
 * 获取GNSS数据指针
 *
 * 提供GPS/GNSS定位数据的访问接口
 *
 * @return GNSS数据结构体的易失性指针
 */
volatile gnss_data *mc_interface_gnss(void);

/*
 * ============================================================================
 * 里程计功能 (Odometer Functions)
 * ============================================================================
 */

/**
 * 获取里程计读数
 *
 * 累计行驶距离，存储在非易失性存储器中
 *
 * @return 累计里程 (米)
 */
uint64_t mc_interface_get_odometer(void);

/**
 * 设置里程计读数
 *
 * 手动设置里程计数值，用于校准或重置
 *
 * @param new_odometer_meters 新的里程计读数 (米)
 */
void mc_interface_set_odometer(uint64_t new_odometer_meters);

/*
 * ============================================================================
 * 输入控制和保护函数 (Input Control & Protection Functions)
 * ============================================================================
 */

/**
 * 忽略输入指定时间
 *
 * 在指定时间内忽略所有控制输入，用于保护和故障恢复
 *
 * @param time_ms 忽略时间 (毫秒)
 */
void mc_interface_ignore_input(int time_ms);

/**
 * 设置电流关闭延时
 *
 * 设置电流控制关闭的延时时间，用于平滑停止
 *
 * @param delay_sec 延时时间 (秒)
 */
void mc_interface_set_current_off_delay(float delay_sec);

/**
 * 覆盖电机温度值
 *
 * 手动设置电机温度值，用于测试或故障诊断
 *
 * @param temp 温度值 (°C)
 */
void mc_interface_override_temp_motor(float temp);

/*
 * ============================================================================
 * 双电机控制函数 (Dual Motor Control Functions)
 * ============================================================================
 */

/**
 * 忽略双电机输入指定时间
 *
 * 同时忽略两个电机的控制输入
 *
 * @param time_ms 忽略时间 (毫秒)
 */
void mc_interface_ignore_input_both(int time_ms);

/**
 * 强制释放双电机
 *
 * 同时强制释放两个电机的控制
 */
void mc_interface_release_motor_override_both(void);

/**
 * 等待双电机释放完成
 *
 * 等待两个电机都完全释放或超时
 *
 * @param timeout 超时时间 (秒)
 * @return true=成功释放, false=超时
 */
bool mc_interface_wait_for_motor_release_both(float timeout);

/*
 * ============================================================================
 * 运行统计函数 (Runtime Statistics Functions)
 * ============================================================================
 */

/**
 * 获取平均速度统计
 *
 * 统计周期内的平均运行速度
 *
 * @return 平均速度 (m/s)
 */
float mc_interface_stat_speed_avg(void);

/**
 * 获取最大速度统计
 *
 * 统计周期内的最大运行速度
 *
 * @return 最大速度 (m/s)
 */
float mc_interface_stat_speed_max(void);

/**
 * 获取平均功率统计
 *
 * 统计周期内的平均功率消耗：
 * P_avg = (1/T) × ∫P(t)dt
 *
 * @return 平均功率 (W)
 */
float mc_interface_stat_power_avg(void);

/**
 * 获取最大功率统计
 *
 * 统计周期内的最大瞬时功率
 *
 * @return 最大功率 (W)
 */
float mc_interface_stat_power_max(void);

/**
 * 获取平均电流统计
 *
 * 统计周期内的平均电机电流
 *
 * @return 平均电流 (A)
 */
float mc_interface_stat_current_avg(void);

/**
 * 获取最大电流统计
 *
 * 统计周期内的最大电机电流
 *
 * @return 最大电流 (A)
 */
float mc_interface_stat_current_max(void);

/**
 * 获取平均MOSFET温度统计
 *
 * 统计周期内的平均MOSFET温度
 *
 * @return 平均MOSFET温度 (°C)
 */
float mc_interface_stat_temp_mosfet_avg(void);

/**
 * 获取最大MOSFET温度统计
 *
 * 统计周期内的最大MOSFET温度
 *
 * @return 最大MOSFET温度 (°C)
 */
float mc_interface_stat_temp_mosfet_max(void);

/**
 * 获取平均电机温度统计
 *
 * 统计周期内的平均电机温度
 *
 * @return 平均电机温度 (°C)
 */
float mc_interface_stat_temp_motor_avg(void);

/**
 * 获取最大电机温度统计
 *
 * 统计周期内的最大电机温度
 *
 * @return 最大电机温度 (°C)
 */
float mc_interface_stat_temp_motor_max(void);

/**
 * 获取统计计时时间
 *
 * 当前统计周期的持续时间
 *
 * @return 统计时间 (秒)
 */
float mc_interface_stat_count_time(void);

/**
 * 重置运行统计
 *
 * 清零所有统计计数器，开始新的统计周期
 */
void mc_interface_stat_reset(void);

/*
 * ============================================================================
 * 故障处理和系统内部函数 (Fault Handling & System Internal Functions)
 * ============================================================================
 */

/**
 * 设置故障信息
 *
 * 记录详细的故障信息，包括描述字符串和参数
 *
 * @param str 故障描述字符串
 * @param argn 参数个数
 * @param arg0 第一个参数
 * @param arg1 第二个参数
 */
void mc_interface_set_fault_info(const char *str, int argn, float arg0, float arg1);

/**
 * 故障停止函数
 *
 * 当检测到故障时立即停止电机运行
 *
 * @param fault 故障代码
 * @param is_second_motor 是否为第二个电机
 * @param is_isr 是否在中断服务程序中调用
 */
void mc_interface_fault_stop(mc_fault_code fault, bool is_second_motor, bool is_isr);

/**
 * 尝试输入处理
 *
 * 检查是否可以接受新的控制输入
 *
 * @return 输入状态码
 */
int mc_interface_try_input(void);

/**
 * 电机控制定时器中断服务程序
 *
 * 核心的电机控制中断处理函数，执行FOC算法和状态更新
 *
 * @param is_second_motor 是否为第二个电机
 */
void mc_interface_mc_timer_isr(bool is_second_motor);

/*
 * ============================================================================
 * 中断处理函数 (Interrupt Handlers)
 * ============================================================================
 */

/**
 * ADC注入中断处理函数
 *
 * 处理ADC注入模式的中断，用于电流采样和控制循环
 * 这是FOC控制的核心中断，执行频率通常为20-30kHz
 *
 * 主要功能：
 * 1. 电流采样和处理
 * 2. FOC算法计算
 * 3. PWM占空比更新
 * 4. 保护检查
 */
void mc_interface_adc_inj_int_handler(void);

/*
 * ============================================================================
 * 外部变量声明 (External Variable Declarations)
 * ============================================================================
 */

/**
 * ADC原始采样值数组
 *
 * 存储所有ADC通道的原始采样值，包括：
 * - 三相电流采样
 * - 电压采样
 * - 温度采样
 * - 其他模拟信号采样
 */
extern volatile uint16_t ADC_Value[];

/**
 * ADC电流归一化值数组
 *
 * 经过校准和归一化处理的电流采样值：
 * I_norm = (ADC_raw - ADC_offset) × scale_factor
 */
extern volatile float ADC_curr_norm_value[];

/*
 * ============================================================================
 * 通用固定参数 (Common Fixed Parameters)
 * ============================================================================
 */

/**
 * 硬件死区时间定义
 *
 * PWM死区时间用于防止上下桥臂同时导通造成直通短路。
 * 死区时间的设置需要考虑：
 * 1. MOSFET的开关特性
 * 2. 驱动电路延时
 * 3. PCB布线延时
 *
 * 计算公式：T_dead = t_rise + t_fall + t_delay + margin
 * 其中：
 * - t_rise: MOSFET上升时间
 * - t_fall: MOSFET下降时间
 * - t_delay: 驱动电路延时
 * - margin: 安全裕量
 */
#ifndef HW_DEAD_TIME_NSEC
#define HW_DEAD_TIME_NSEC				360.0	// 死区时间 (纳秒)
#endif

#endif /* MC_INTERFACE_H_ */
