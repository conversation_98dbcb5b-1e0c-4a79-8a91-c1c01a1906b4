#ifndef __ENC_SPEED_H
#define __ENC_SPEED_H

#include "at32a423_wk_config.h"
#include "ad2s1212_spi.h"
#include "arm_math.h"

/*============================ 系统参数 ============================*/
/* 采样周期配置 */
#define SAMPLE_TIME           0.00005f  // 采样时间 50us (20kHz)
#define SAMPLE_TIME_US        50        // 采样时间(微秒)

/* 速度环配置 */
#define SPEED_LOOP_TIME       0.00025f  // 速度环周期 250us (4kHz)
#define SPEED_LOOP_TIME_US    250       // 速度环周期(微秒)
#define SPEED_LOOP_DIVIDER    5         // 速度环分频比 (250us/50us)

/*============================ 电机参数 ============================*/
#define MOTOR_POLE_PAIRS      4         // 电机极对数

/*============================ 编码器/旋变参数 ====================*/
#define RESOLVER_POLE_PAIRS   4         // 旋变极对数
#define RESOLVER_BITS         12        // 旋变分辨率(位)
#define RESOLVER_MAX_COUNT    4096      // 旋变最大计数值(2^12)
#define ALIGN_POSITION        2393      // 磁链对齐零位置
#define TIM3_COUNTER_PERIOD   16383     // TIM3计数器周期值(4096*4-1)
#define MECH_CYCLE_COUNT      16384     // 一个机械周期的计数值(4096*4)
#define COUNTS_PER_ELEC_REV   4096      // 电角度每圈计数，等于RESOLVER_MAX_COUNT
#define COUNTS_PER_MECH_REV   16384     // 机械角度每圈计数

/*============================ Z相参数 ============================*/
#define Z_PHASE_AS_ABS_ZERO   0         // Z相绝对零位使能
#define Z_PHASE_COUNT_PER_REV 1         // 每圈Z相脉冲数
#define Z_PHASE_EXPECTED_POS  13991     // Z相绝对零位(0-2393+周期)的TIM3计数值

/*============================ 转换系数 ===========================*/
/* 角度转换系数 */
#define ANGLE_CONVERSION_FACTOR      0.001534f       // 电角度转换因子(2π/4096)
#define MECHANICAL_ANGLE_FACTOR      0.0003835f      // 机械角度转换因子(2π/16384)

/*============================ 速度计算参数 =======================*/
/* 基础转换常数 */
#define RAD_TO_RPS                   0.159154943f    // 1/(2*PI) - 转换rad/s到RPS
#define RPS_TO_RPM                   60.0f           // 60 - 转换RPS到RPM
#define RAD_TO_RPM                   9.549296586f    // 60/(2*PI) - 转换rad/s到RPM
#define INV_RAD_TO_RPM               0.10471975512f  // 1/RAD_TO_RPM - 转换RPM到rad/s

/* 预计算的速度计算系数 - 4KHz速度环 */
#define SPEED_CALC_COEFF_RPS         (RAD_TO_RPS / SPEED_LOOP_TIME)    // 636.6197724 = 0.159154943 / 0.00025
#define SPEED_CALC_COEFF_RPM         (RAD_TO_RPM / SPEED_LOOP_TIME)    // 38197.18664 = 9.549296586 / 0.00025

/*============================ 滤波器参数 =========================*/
#define DEFAULT_FILTER_ALPHA   0.5f                  // 低通滤波器默认系数
#define DEFAULT_FILTER_SIZE    5                     // 滑动平均滤波器默认大小

/* 卡尔曼滤波器参数 */
#define DEFAULT_KALMAN_Q_VEL       0.02f    // 速度过程噪声
#define DEFAULT_KALMAN_Q_ACC       0.01f    // 加速度过程噪声
#define DEFAULT_KALMAN_R_MEASURE   2.0f     // 测量噪声
#define DEFAULT_KALMAN_P0_INIT     1.0f     // 初始协方差

/* 滤波器类型 */
typedef enum {
    FILTER_NONE,              // 无滤波
    FILTER_LOWPASS,           // 一阶低通滤波
    FILTER_MOVING_AVG,        // 滑动平均滤波
    FILTER_KALMAN             // 卡尔曼滤波器(速度+加速度)
} FilterType_t;

/* 低通滤波器 */
typedef struct {
    float output;             // 滤波输出
    float last_output;        // 上次输出
    float alpha;              // 滤波系数(0-1)
    float one_minus_alpha;    // 预计算的(1-alpha)值
} LowPassFilter_t;

/* 滑动平均滤波器 */
typedef struct {
    float *buffer;            // 数据缓冲区
    uint16_t size;            // 缓冲区大小
    uint16_t index;           // 当前索引
    float sum;                // 数据和
    float inv_size;           // 预计算的1/size值
} MovingAvgFilter_t;

/* 卡尔曼滤波器结构体 */
typedef struct {
    float x[2];               // 状态变量[速度, 加速度]
    float p[2][2];            // 状态协方差矩阵
    float q[2];               // 过程噪声协方差
    float r;                  // 测量噪声协方差
    float k[2];               // 卡尔曼增益
    
    /* 预计算常量 */
    float dt;                 // 固定时间间隔
    float dt_sq;              // dt的平方
    float dt_half;            // dt/2
    float dt_sq_half;         // dt²/2
    float inv_dt;             // 1/dt
    float inv_r;              // 1/r
    
    /* ARM DSP矩阵实例 */
    arm_matrix_instance_f32 F_matrix;     // 状态转移矩阵 2x2
    arm_matrix_instance_f32 P_matrix;     // 协方差矩阵 2x2
    arm_matrix_instance_f32 Q_matrix;     // 过程噪声矩阵 2x2
    arm_matrix_instance_f32 H_matrix;     // 观测矩阵 1x2
    arm_matrix_instance_f32 R_matrix;     // 测量噪声矩阵 1x1
    arm_matrix_instance_f32 K_matrix;     // 卡尔曼增益矩阵 2x1
    
    /* 矩阵数据存储 */
    float F_data[4];          // F矩阵数据 [1, dt; 0, 1]
    float Q_data[4];          // Q矩阵数据 [q_vel, 0; 0, q_acc]
    float H_data[2];          // H矩阵数据 [1, 0]
    float R_data[1];          // R矩阵数据 [r]
    
    /* 临时矩阵存储 */
    float temp1_data[4];      // 临时矩阵1 2x2
    float temp2_data[4];      // 临时矩阵2 2x2
    float temp3_data[2];      // 临时矩阵3 2x1
    arm_matrix_instance_f32 temp1_matrix;
    arm_matrix_instance_f32 temp2_matrix;
    arm_matrix_instance_f32 temp3_matrix;
} KalmanFilter_t;

/* 角度计算结构体 */
typedef struct {
    float elec_angle;         // 电角度(0-2π)
    float mech_angle;         // 机械角度(0-2π)
    int32_t mech_revolutions; // 机械旋转圈数
    float total_mech_angle;   // 总机械角度
} AngleData_t;

/* 速度计算结构体 */
typedef struct {
    float elec_speed_rad;      // 电气角速度(rad/s)
    float mech_speed_rad;      // 机械角速度(rad/s)
    float mech_speed_rps;      // 机械转速(rps)
    float signed_speed_rps;    // 带符号转速(rps)
    float signed_speed_rpm;    // 带符号转速(rpm)
    float last_mech_angle;     // 上次机械角度
    float last_total_angle;    // 上次总角度
} SpeedData_t;

/* 卡尔曼滤波器函数 */
void InitKalmanFilter(float q_vel, float q_acc, float r, float p0);
void SetKalmanFilterParams(float q_vel, float q_acc, float r, float p0);

/*============================ FHAN路径规划参数 ======================*/
/* FHAN算法参数 */
#define FHAN_DEFAULT_R0          50.0f     // 默认速度因子
#define FHAN_DEFAULT_H0          0.00025f  // 积分步长
#define FHAN_MAX_ACCEL_DEFAULT   200.0f    // 默认最大加速度限制

/* FHAN滤波器结构体 */
typedef struct {
    float x1;            // 位置状态(当前输出速度 RPS)
    float x2;            // 速度状态(当前加速度 RPS/s) 
    float r0;            // 速度因子参数
    float h0;            // 步长参数
    float max_accel;     // 最大加速度限制
    uint8_t enabled;     // 使能标志：1=启用fhan，0=直通
    
    /* 预计算常量 */
    float h0_sq;         // h0的平方
    float r0_h0;         // r0*h0
} FhanFilter_t;

/*============================ PI控制器参数 ===========================*/
/* PI控制器设计参数 */
#define PI_BANDWIDTH_HZ          500.0f    // PI控制器目标带宽
#define PI_INTEGRAL_SEPARATION   5.0f      // 积分分离系数
#define SYSTEM_INERTIA_KGM2      8.113f    // 系统总转动惯量
#define MOTOR_KT_AVERAGE         0.38f     // 电机平均转矩常数

/* PI控制器增益配置 */
#define PI_DEFAULT_KP            0.2f      // 默认比例增益
#define PI_DEFAULT_KI            0.001f    // 默认积分增益(连续域)
#define PI_DEFAULT_KI_TS         (PI_DEFAULT_KI * SPEED_LOOP_TIME)  // 离散积分增益
#define PI_DEFAULT_MAX_INTEGRAL  50.0f     // 积分限幅
#define PI_DEFAULT_MAX_OUTPUT    200.0f    // 输出限幅
#define PI_INTEGRAL_LEAK_RATE    0.9999f   // 积分泄漏系数

/* 前馈补偿系数 - 根据电机参数计算 */
#define FEEDFORWARD_COEFF        (SYSTEM_INERTIA_KGM2 / MOTOR_KT_AVERAGE)  // J/Kt = 8.113/0.38 ≈ 21.35

/* PI控制器结构体 */
typedef struct {
    float kp;                // 比例增益
    float ki;                // 积分增益(连续域)
    float ki_ts;             // 离散积分增益
    float integral_sum;      // 积分累加和
    float max_integral_sum;  // 积分累加和限幅
    float max_output;        // 输出限幅
    float leak_rate;         // 积分泄漏系数
    float output;            // PI输出
    float feedforward;       // 前馈补偿
    float total_output;      // 总输出
    uint8_t enabled;         // 使能标志
    uint8_t reset_integral;  // 积分复位标志
    uint8_t ff_enabled;      // 前馈使能标志
} PIController_t;

/* 编码器处理结构体 */
typedef struct {
    uint16_t resolver_pos;    // 旋变绝对位置
    int32_t encoder_count;    // 编码器计数
    uint8_t z_detected;       // Z相检测标志
    int8_t direction;         // 旋转方向: 1=正转, -1=反转, 0=静止
    int8_t angle_direction;   // 角度差方向: 1=正转, -1=反转, 0=静止
    
    AngleData_t angle;        // 角度数据
    SpeedData_t speed;        // 速度数据
    
    FilterType_t filter_type; // 滤波器类型
    LowPassFilter_t lp_filter;   // 低通滤波器
    MovingAvgFilter_t ma_filter; // 滑动平均滤波器
    KalmanFilter_t kalman;       // 卡尔曼滤波器
    
    FhanFilter_t fhan;        // FHAN路径规划滤波器
    PIController_t speed_pi;  // 速度PI控制器
    
    uint16_t z_counter;       // Z相计数器
    
    /* Z相检测数据 */
    uint16_t z_stable_counter; // Z相稳定计数器
    uint16_t z_expected_pos;   // Z相期望位置
    int16_t z_position_error;  // Z相位置误差
    
    /* 预计算的常量 */
    float angle_diff_threshold; // 角度差判断阈值
} EncoderHandler_t;

/* 外部变量声明 */
extern EncoderHandler_t g_encoder;

/* 接口函数声明 */
void EncoderSpeed_Init(void);
void EncoderSpeed_Reset(void);

/**
 * @brief 完整的速度环系统初始化
 * @param filter_type 速度滤波器类型选择
 * @param enable_fhan 是否启用FHAN路径规划 (1=启用, 0=禁用)
 * @param enable_feedforward 是否启用前馈补偿 (1=启用, 0=禁用)
 * @note 一次性完成编码器、滤波器、FHAN、PI控制器的初始化和配置
 *       避免在主函数中重复调用多个配置函数
 */
void SpeedLoopSystem_Init(FilterType_t filter_type, uint8_t enable_fhan, uint8_t enable_feedforward);

/**
 * @brief 获取电角度（主接口函数）
 * @return 电角度(0-2π)
 * @note 在20kHz电流环中断中调用，只负责角度计算和更新
 *       不再包含速度计算逻辑
 */
float GetElectricalAngle_ENC(void);

/**
 * @brief 速度计算函数（新增）
 * @note 在4kHz速度环定时器中调用，专门负责速度计算
 *       卡尔曼滤波->FHAN路径规划->PI控制器(500Hz带宽)
 */
void CalculateSpeed_4KHz(void);

/**
 * @brief 获取带方向的转速(RPS)
 * @return 带方向的转速(rps)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPS(void);

/**
 * @brief 获取带方向的转速(RPM)
 * @return 带方向的转速(rpm)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPM(void);

/**
 * @brief 获取FHAN滤波后的速度指令(RPS)
 * @return FHAN输出的平滑速度指令(rps)，用于PI控制器
 */
float GetFhanSpeed_RPS(void);

/**
 * @brief 获取FHAN输出的加速度(RPS/s)
 * @return FHAN输出的加速度，可用于前馈补偿
 */
float GetFhanAcceleration_RPS(void);

/**
 * @brief TIM3溢出中断回调函数
 * @note 在TIM3中断服务程序中调用，用于圈数跟踪备份机制
 */
void TIM3_OverflowCallback(void);

/**
 * @brief Z脉冲检测回调函数
 * @note 外部Z相IO中断中调用
 *      用于记录旋转圈数
 */
void ZPulseDetectedCallback(void);

/* 配置函数 */
void SetSpeedFilterType(FilterType_t type);
void SetLowPassFilterCoeff(float alpha);
void SetMovingAvgFilterSize(uint16_t size);

/* FHAN路径规划函数 */
void InitFhanFilter(float r0, float max_accel);
void SetFhanParams(float r0, float max_accel);
void EnableFhanFilter(uint8_t enable);
uint8_t IsFhanEnabled(void);

/* PI控制器函数 */
void InitSpeedPI(float kp, float ki, float max_integral, float max_output);
void SetSpeedPIParams(float kp, float ki, float max_integral, float max_output);
void EnableSpeedPI(uint8_t enable);
void EnablePIFeedforward(uint8_t enable);
void ResetSpeedPIIntegral(void);
uint8_t IsSpeedPIEnabled(void);

/**
 * @brief 获取速度PI控制器输出(电流指令A)
 * @param speed_target 目标速度(RPS) - 来自FHAN输出或直接给定
 * @return PI控制器输出的电流指令(A)
 */
float GetSpeedPIOutput(float speed_target);

/**
 * @brief 获取速度PI总输出(包含前馈)
 * @return 总电流指令(A) = PI输出 + 前馈补偿
 */
float GetSpeedPITotalOutput(void);

/**
 * @brief 250us速度环中断回调函数
 * @param speed_target 目标速度指令(RPS)
 * @return 电流指令输出(A)，包含PI控制和前馈补偿
 * @note 完整的速度环流程：角度更新 → 卡尔曼滤波 → FHAN路径规划 → PI控制
 *       在4KHz定时器中断中调用此函数
 */
float SpeedLoop_4KHz_Callback(float speed_target);

/**
 * @brief 获取速度环输出的Iq电流指令
 * @return Iq电流指令(A)，用于电流环控制
 * @note 等效于GetSpeedPITotalOutput()，提供更明确的接口名称
 */
float GetIqCurrentCommand(void);

#endif /* __ENC_SPEED_H */
