// #ifndef MATHBASIC_H
// #define MATHBASIC_H

// #include "at32a423.h"                   // Device header


// // 数学常量定义
// #define PI            3.14159265
// #define PI2           6.28318531
// #define SQRT2By3      0.816496581
// #define SQRT3_D2      0.866025404
// #define SQRT3         1.73205080
// #define SQRT2         1.41421356
// #define f1_DSQRT2     0.70710678
// #define SQRT3_DSQRT2  (SQRT3/SQRT2)
// #define TWO_D_SQRT3   1.15470054
// #define FOUR_D_3PI    (PI * 4.0 / 3.0)
// #define TWO_D_3PI     (PI * 2.0 / 3.0)
// #define VarAveToRMS   1.11072073	// = pi / (2 * sqrt(2))
// #define N_PI2		  (-1.0)*(PI2)//
// #define f4_D_3PI      (4.0/3.0)*(PI)//
// #define f2_D_3PI      (2.0/3.0)*(PI)//
// #define D2By3         0.66666667//
// #define PowerCalLPFSampFre 5988.0//
// // 宏函数定义
// //#define MAX(x,y)	(((x) >= (y)) ? (x) : (y))		// 求最大值
// //#define MIN(x,y)	(((x) <= (y)) ? (x) : (y))		// 求最小值
// #define POW2(x)  	((x) * (x))		
// #define POW3(x)  	((x) * (x) * (x))		
// #define UP_LIMIT(x, limit)	(MIN(x, limit))		// 上限幅
// #define DOWN_LIMIT(x, limit)	(MAX(x, limit))				// 下限幅
// #define DUAL_LIMIT(x, vmax, vmin)		(DOWN_LIMIT(UP_LIMIT(x, vmax), vmin))		// 双向限幅
// // 函数声明
// extern float LPFKY(float fLPFVarIn,float fLPFVarInOld,float fLPFVarOutOld,float fLPFCutFre);
// extern float Max(float a,float b,float c);
// extern float Min(float a,float b,float c);
// extern uint16_t uMax(uint16_t a,uint16_t b,uint16_t c);
// extern uint16_t uMin(uint16_t a,uint16_t b,uint16_t c);
// extern uint16_t uMid(uint16_t a,uint16_t b,uint16_t c);
// extern float Mux3s1(uint16_t SelectSig,float fIn0,float fIn1,float fIn2);
// extern int HysLoop(float fVarIn,float fVarCom,float fVarDeta);
// extern float LPFCompensate(float fVarIn,float fVarInOld,float fTCon);
// extern float Limit(float fMinIn,float fMaxIn,float fDateIn);
// extern float LPF(float fLPFVarIn,float fLPFTCon,float fLPFOutOld);

// #endif
// //========================================================================
// // No more.
// //========================================================================