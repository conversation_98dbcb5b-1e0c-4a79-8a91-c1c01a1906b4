/**********************************************************
  * @file     Sic_SelfTest.h
  * @brief    碳化硅模块自检功能头文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  * @note     包含碳化硅模块自检相关的数据结构定义和函数声明
***********************************************************/

#ifndef _SIC_SELF_TEST_H
#define _SIC_SELF_TEST_H

#include "at32a423.h"                   // Device header
#include "arm_math.h"

/************************** 碳化硅自检功能相关 **************************/

/* 碳化硅模块自检参数定义 */
static const float SIC_TEST_PWM_DUTYCYCLE = 5.0f;     // 测试PWM占空比(%)
static const float SIC_CURRENT_THRESHOLD = 0.1f;      // 电流正常阈值(A)
static const float SIC_SHORT_CURRENT = 1.0f;          // 短路电流阈值(A)
static const float SIC_PHASE_UNBALANCE = 20.0f;       // 相不平衡度阈值(%)
static const float SIC_THD_THRESHOLD = 10.0f;         // THD阈值(%)
static const uint16_t SIC_FAULT_CONFIRM_TIME = 100;   // 故障确认时间(ms)
static const uint8_t SIC_SAMPLE_COUNT = 8;           // 默认采样次数

/* 故障代码定义 */
typedef enum {
    SIC_FAULT_NONE = 0,           // 无故障
    SIC_FAULT_U_OPEN,             // U相开路
    SIC_FAULT_V_OPEN,             // V相开路
    SIC_FAULT_W_OPEN,             // W相开路
    SIC_FAULT_U_SHORT,            // U相短路
    SIC_FAULT_V_SHORT,            // V相短路
    SIC_FAULT_W_SHORT,            // W相短路
    SIC_FAULT_MOTOR_PHASE_LOSS,   // 电机缺相
    SIC_FAULT_MOTOR_WINDING_SHORT // 电机绕组短路
} SicFaultCode_t;

/* 故障标志位结构体 */
typedef union {
    struct {
        uint8_t u_open : 1;           // U相开路
        uint8_t v_open : 1;           // V相开路
        uint8_t w_open : 1;           // W相开路
        uint8_t u_short : 1;          // U相短路
        uint8_t v_short : 1;          // V相短路
        uint8_t w_short : 1;          // W相短路
        uint8_t motor_phase_loss : 1; // 电机缺相
        uint8_t motor_winding_short : 1; // 电机绕组短路
    } bits;
    uint8_t all;
} SicFaultFlags_t;

/* 电流不平衡度结构体 */
typedef struct {
    float u_deviation;      // U相偏差
    float v_deviation;      // V相偏差
    float w_deviation;      // W相偏差
    float max_deviation;    // 最大偏差
    float unbalance;        // 不平衡度(%)
} CurrentUnbalance_t;

/* 谐波分析结果 */
typedef struct {
    float thd_u;            // U相THD(%)
    float thd_v;            // V相THD(%)
    float thd_w;            // W相THD(%)
    float max_thd;          // 最大THD(%)
} HarmonicDistortion_t;

/* 碳化硅自检结果结构体 */
typedef struct {
    SicFaultFlags_t fault_flags;        // 故障标志
    SicFaultCode_t fault_code;          // 故障代码
    CurrentUnbalance_t unbalance;       // 电流不平衡度
    HarmonicDistortion_t harmonic;      // 谐波畸变率
    uint32_t fault_confirm_time;        // 故障确认时间计数
    uint8_t test_state;                 // 测试状态
} SicSelfTest_TypeDef;

/* 自检相关函数声明 */
uint8_t Sic_StaticTest(void);
uint8_t Sic_DynamicTest(void);
void Sic_SelfTest_Init(void);
void Sic_CheckFault(void);
float CalculateUnbalance(float Iu, float Iv, float Iw);
float CalculateTHD(float* signal, uint16_t length);
void Sic_SetFault(SicFaultCode_t fault_code);

/* 自检全局变量声明 */
extern SicSelfTest_TypeDef g_SicSelfTest;

/************************** RMS计算功能相关 **************************/

// 定义环形缓冲区大小
#define RMS_BUFFER_SIZE 10

// 定义RMS算法常量参数
#define RMS_SAMPLE_COUNT 2000   // 固定采样点数
#define RMS_SAMPLE_COUNT_RECIPROCAL 0.00025f  // 1/4000，用乘法替代除法

// RMS计算状态
typedef enum {
    RMS_IDLE = 0,   // 空闲状态
    RMS_BUSY = 1    // 计算中状态
} RMS_State_t;

// 环形缓冲区结构体
typedef struct {
    float samples_A[RMS_BUFFER_SIZE];  // A相数据缓冲区
    float samples_B[RMS_BUFFER_SIZE];  // B相数据缓冲区
    float samples_C[RMS_BUFFER_SIZE];  // C相数据缓冲区
    volatile uint8_t head;            // 头指针
    volatile uint8_t tail;            // 尾指针
    volatile uint8_t count;           // 当前数据数量
} RMS_RingBuffer_t;

// RMS计算结构体 - 优化结构体成员
typedef struct RMS_OUT_TAG { 
    /* 输入变量 */
    float fSampl_A;        // A相采样输入值
    float fSampl_B;        // B相采样输入值
    float fSampl_C;        // C相采样输入值

    uint16_t iN;           // 当前采样点计数
    float fSquare_A;       // A相采样值平方和
    float fSquare_B;       // B相采样值平方和
    float fSquare_C;       // C相采样值平方和
    float fSquare_A_former;// A相上一周期采样值平方和
    float fSquare_B_former;// B相上一周期采样值平方和
    float fSquare_C_former;// C相上一周期采样值平方和

    /* 输出变量 */
    float fResult_A;       // A相RMS计算结果
    float fResult_B;       // B相RMS计算结果
    float fResult_C;       // C相RMS计算结果

    /* 环形缓冲区 */
    RMS_RingBuffer_t buffer;
    
    /* 计算状态 */
    volatile RMS_State_t state;
    uint8_t results_valid;    // 指示结果是否有效
} RMS_OUT;

// RMS计算接口函数
void RMS_Update_Samples(RMS_OUT *p, float sample_A, float sample_B, float sample_C);
void RMS_Calculate(RMS_OUT *p);
uint8_t RMS_Get_Results(RMS_OUT *p, float *result_A, float *result_B, float *result_C);

// 默认值宏定义 - 直接使用优化版本
#define RMS_OUT_DEFAULTS {0,0,0,0,0,0,0,0,0,0,0,0,0,{{0},0,0,0},RMS_IDLE,0}

// RMS计算全局变量声明
extern RMS_OUT Rms_Grid_PhaseVolt; 

/************************** 三相不平衡相关功能 **************************/

// 不平衡度计算参数
#define UNBALANCE_WARNING_THRESHOLD  10.0f   // 不平衡度告警阈值(%)
#define UNBALANCE_FAULT_THRESHOLD    20.0f   // 不平衡度故障阈值(%)
#define UNBALANCE_CHECK_INTERVAL     100     // 不平衡度检测间隔(ms)

// 三相不平衡检测结构体
typedef struct {
    float phase_A;                // A相有效值
    float phase_B;                // B相有效值
    float phase_C;                // C相有效值
    float avg_value;              // 三相平均值
    float max_deviation;          // 最大偏差
    float unbalance_percent;      // 不平衡百分比
    uint8_t is_balanced;          // 平衡状态指示
    uint16_t check_counter;       // 检测计数器
} PhaseBalance_TypeDef;

// 三相不平衡相关函数声明
float Calculate_Phase_Unbalance(float phase_A, float phase_B, float phase_C);
uint8_t Check_Phase_Balance(PhaseBalance_TypeDef *balance, float phase_A, float phase_B, float phase_C);

// 三相不平衡全局变量声明
extern PhaseBalance_TypeDef g_PhaseBalance;

#endif  

