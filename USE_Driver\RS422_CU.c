/**********************************************************
  * @file     RS422_CU.c
  * @brief    RS422控制单元实现文件
  * <AUTHOR>
  * @version  V1.0.0
  * @date     2025-05-21
  * @note     实现RS422通信控制电机的功能
************************************************************/

#include "RS422_CU.h"
#include <string.h>
#include "MotorParams.h"

/* 全局变量定义 */
RS422_CU_TypeDef g_rs422_cu;

/* 外部变量声明 */
extern SystemStatus_TypeDef g_SystemStatus;

/* 结构体存储帧接收状态 */
struct {
    RS422_FrameState_t state;     // 当前状态机状态
    uint8_t command;              // 命令字节
    uint8_t data_count;           // 已接收数据计数
    uint8_t data[2];              // 固定2字节数据
    uint8_t checksum;             // 校验和字节
    uint32_t rx_count;             // 接收计数
} rx_frame = {FRAME_STATE_IDLE, 0, 0, {0, 0}, 0};

/**
 * @brief  RS422控制单元初始化
 * @note   初始化相关变量
 * @param  无
 * @retval 无
 */
void RS422_CU_Init(void)
{
    /* 初始化结构体 */
    memset(&g_rs422_cu, 0, sizeof(RS422_CU_TypeDef));
    
    /* 初始化帧格式 */
    g_rs422_cu.frame.start = RS422_FRAME_START;
    g_rs422_cu.frame.end = RS422_FRAME_END;
    
    /* 初始化接收状态结构体 */
    rx_frame.state = FRAME_STATE_IDLE;
    rx_frame.data_count = 0;
    
    /* 使能接收中断 */
    //usart_interrupt_enable(USART1, USART_RDBF_INT, TRUE);
}

/**
 * @brief  USART1中断处理函数
 * @note   接收数据并直接在中断中解析处理命令
 * @param  无
 * @retval 无
 */
void USART1_IRQHandler(void)
{
    if (usart_interrupt_flag_get (USART1,USART_ROERR_FLAG)!=RESET )
    {

    }

    /* 接收中断 */
    if(usart_interrupt_flag_get(USART1, USART_RDBF_FLAG) != RESET)
    {
        
        //usart_flag_clear (USART1, USART_BFF_FLAG ); 
        uint8_t received_byte = usart_data_receive(USART1);  // 读取数据寄存器
        rx_frame.rx_count ++ ;
        /* 状态机处理 */
        switch(rx_frame.state)
        {
            case FRAME_STATE_IDLE:
                /* 等待帧起始符 */
                if(received_byte == RS422_FRAME_START)
                {
                    rx_frame.state = FRAME_STATE_ADDR;
                }
                break;
                
            case FRAME_STATE_ADDR:
                /* 检查地址是否匹配 */
                if(received_byte == RS422_DEVICE_ADDR)
                {
                    rx_frame.state = FRAME_STATE_CMD;
                }
                else
                {
                    /* 地址不匹配，重置状态机 */
                    rx_frame.state = FRAME_STATE_IDLE;
                }
                break;
                
            case FRAME_STATE_CMD:
                /* 保存命令字节 */
                rx_frame.command = received_byte;
                rx_frame.state = FRAME_STATE_LEN;
                break;
                
            case FRAME_STATE_LEN:
                /* 对于固定2字节数据，检查长度是否为2 */
                if(received_byte == 2)
                {
                    rx_frame.data_count = 0;
                    rx_frame.state = FRAME_STATE_DATA;
                }
                else
                {
                    /* 数据长度错误，重置状态机 */
                    rx_frame.state = FRAME_STATE_IDLE;
                }
                break;
                
            case FRAME_STATE_DATA:
                /* 接收固定2字节数据 */
                rx_frame.data[rx_frame.data_count++] = received_byte;
                
                /* 接收完2字节数据后进入校验位状态 */
                if(rx_frame.data_count >= 2)
                {
                    rx_frame.state = FRAME_STATE_CHECKSUM;
                }
                break;
                
            case FRAME_STATE_CHECKSUM:
                /* 保存校验和字节 */
                rx_frame.checksum = received_byte;
                /* 不校验校验和，直接进入帧尾检查 */
                rx_frame.state = FRAME_STATE_END;
                break;
                
            case FRAME_STATE_END:
                /* 检查帧尾 */
                if(received_byte == RS422_FRAME_END)
                {
                    /* 帧接收完成，直接执行命令 */
                    switch(rx_frame.command)
                    {
                        case CMD_START:
                            /* 启动电机命令 */
                            g_SystemStatus.cmd.bits.motor_start = 1;
                            g_SystemStatus.cmd.bits.motor_stop = 0;
                            break;
                            
                        case CMD_STOP:
                            /* 停止电机命令 */
                            g_SystemStatus.cmd.bits.motor_stop = 1;
                            g_SystemStatus.cmd.bits.motor_start = 0;
                            break;
                            
                        case CMD_SPEED:
                            /* 设置速度命令（低字节在前） */
                            {
                                int16_t speed = (int16_t)((uint16_t)rx_frame.data[0] | 
                                             ((uint16_t)rx_frame.data[1] << 8));
                                *pCsvParamSpeed_ref = (float)speed;
                            }
                            break;
                            
                        default:
                            /* 未知命令，不处理 */
                            break;
                    }
                }
                
                /* 无论帧尾是否正确，都重置状态机准备接收下一帧 */
                rx_frame.state = FRAME_STATE_IDLE;
                rx_frame.data_count = 0;
                break;
                
            default:
                /* 未知状态，重置状态机 */
                rx_frame.state = FRAME_STATE_IDLE;
                rx_frame.data_count = 0;
                break;
        }
    }
}

/**
 * @brief  启动电机
 * @note   设置电机启动标志
 * @param  无
 * @retval 无
 */
void RS422_CU_StartMotor(void)
{
    g_SystemStatus.cmd.bits.motor_start = 1;
    g_SystemStatus.cmd.bits.motor_stop = 0;
}

/**
 * @brief  停止电机
 * @note   设置电机停止标志
 * @param  无
 * @retval 无
 */
void RS422_CU_StopMotor(void)
{
    g_SystemStatus.cmd.bits.motor_stop = 1;
    g_SystemStatus.cmd.bits.motor_start = 0;
}

/**
 * @brief  设置电机速度
 * @note   设置电机转速
 * @param  speed: 目标转速(RPM)
 * @retval 无
 */
void RS422_CU_SetSpeed(uint16_t speed)
{
    /* 设置电机转速参考值 */
    *(gMotorParams + 51) = (float)speed;
}

/**
 * @brief  发送响应帧
 * @note   根据命令和数据构建响应帧并发送
 * @param  cmd: 命令
 * @param  data: 数据域
 * @param  len: 数据长度
 * @retval 无
 */
void RS422_CU_SendResponse(uint8_t cmd, uint8_t* data, uint8_t len)
{
    uint16_t i;
    uint16_t tx_count = 0;
    
    /* 构建响应帧 */
    g_rs422_cu.tx_buffer[tx_count++] = RS422_FRAME_START;
    g_rs422_cu.tx_buffer[tx_count++] = RS422_DEVICE_ADDR;
    g_rs422_cu.tx_buffer[tx_count++] = cmd;
    g_rs422_cu.tx_buffer[tx_count++] = len;
    
    /* 复制数据域 */
    for(i = 0; i < len; i++)
    {
        g_rs422_cu.tx_buffer[tx_count++] = data[i];
    }
    
    /* 添加帧尾 */
    g_rs422_cu.tx_buffer[tx_count++] = RS422_FRAME_END;
    
    /* 发送数据 */
    for(i = 0; i < tx_count; i++)
    {
        while(usart_flag_get(USART1, USART_TDBE_FLAG) == RESET);
        usart_data_transmit(USART1, g_rs422_cu.tx_buffer[i]);
    }
    
    /* 等待发送完成 */
    while(usart_flag_get(USART1, USART_TDC_FLAG) == RESET);
}