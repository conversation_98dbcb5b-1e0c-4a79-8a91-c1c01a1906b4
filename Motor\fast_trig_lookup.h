/**
 * @file fast_trig_lookup.h
 * @brief 基于查找表和线性插值的快速三角函数计算
 *
 * 本模块提供高性能的正弦和余弦计算，使用预计算的查找表配合线性插值
 * 来提高精度。函数接受弧度角作为输入，完全与硬件无关。
 *
 * 特性:
 * - 硬件无关的实现
 * - 线性插值提高精度
 * - 优化的sin/cos组合计算
 * - 输入范围: 任意浮点弧度值（自动标准化）
 *
 * <AUTHOR>
 * @date 2025
 */

#ifndef FAST_TRIG_LOOKUP_H
#define FAST_TRIG_LOOKUP_H

#include <stdint.h>

// 查找表常量定义
#define SIN_TABLE_SIZE 4096                        // 查找表条目数量
#define SIN_TABLE_MASK (SIN_TABLE_SIZE - 1)        // 4095，用于快速取模运算
#define SIN_TABLE_COS_OFFSET (SIN_TABLE_SIZE / 4)  // 1024，cos(x) = sin(x + π/2)的偏移量

// 角度转换的数学常量
#define TWO_PI 6.28318530717958647692f             // 2π
#define INV_TWO_PI 0.15915494309189533577f         // 1/(2π)
#define TABLE_SCALE (SIN_TABLE_SIZE * INV_TWO_PI)  // 角度到索引转换的缩放因子

// 正弦查找表的外部声明
extern const float g_sin_table[SIN_TABLE_SIZE];

/**
 * @brief 使用查找表和线性插值的快速正弦计算
 *
 * 使用预计算的查找表配合线性插值来计算给定角度的正弦值，
 * 相比直接查表具有更高的精度。
 *
 * @param angle 输入角度（弧度），任意值，会自动标准化到[0, 2π)范围
 * @return 输入角度的正弦值
 */
float fast_sin(float angle);

/**
 * @brief 使用查找表和线性插值的快速余弦计算
 *
 * 使用预计算的查找表配合线性插值来计算给定角度的余弦值，
 * 相比直接查表具有更高的精度。
 *
 * @param angle 输入角度（弧度），任意值，会自动标准化到[0, 2π)范围
 * @return 输入角度的余弦值
 */
float fast_cos(float angle);

/**
 * @brief 快速组合正弦余弦计算
 *
 * 在单次函数调用中高效计算给定角度的正弦和余弦值。
 * 比分别调用fast_sin()和fast_cos()更高效，因为只进行一次角度到索引的转换。
 *
 * @param angle 输入角度（弧度），任意值，会自动标准化到[0, 2π)范围
 * @param sin_val 存储计算得到的正弦值的指针
 * @param cos_val 存储计算得到的余弦值的指针
 */
void fast_sincos(float angle, float* sin_val, float* cos_val);

/*============================ 兼容性函数 ============================*/
/*
 * 这些函数提供与旧版encoder_sin_lookup模块的兼容性
 * 支持直接使用编码器计数值进行查表
 */

/**
 * @brief 兼容性函数：从编码器值获取sin值
 *
 * 提供与旧版encoder_sin_lookup模块的兼容性。直接使用编码器计数值
 * 作为查找表索引，不进行插值计算。
 *
 * @param encoder_value 编码器计数值 (0 到 SIN_TABLE_SIZE-1)
 * @return 对应的sin值
 */
float get_sin_from_encoder(uint16_t encoder_value);

/**
 * @brief 兼容性函数：从编码器值获取cos值
 *
 * 提供与旧版encoder_sin_lookup模块的兼容性。直接使用编码器计数值
 * 作为查找表索引，不进行插值计算。
 *
 * @param encoder_value 编码器计数值 (0 到 SIN_TABLE_SIZE-1)
 * @return 对应的cos值
 */
float get_cos_from_encoder(uint16_t encoder_value);

#endif // FAST_TRIG_LOOKUP_H
