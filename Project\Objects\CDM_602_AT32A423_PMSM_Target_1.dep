Dependencies for Project 'CDM_602_AT32A423_PMSM', Target 'Target_1': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\Libraries\dsp\arm_cortexM4lf_math.lib)(0x67638300)()
F (..\Libraries\usb_drivers\src\usbd_core.c)(0x67775555)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/usbd_core.o -MMD)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_sdr.h)(0x67775555)
F (..\Libraries\usb_drivers\src\usbd_sdr.c)(0x67775555)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/usbd_sdr.o -MMD)
I (..\Libraries\usb_drivers\inc\usbd_sdr.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
F (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)()
F (..\Libraries\usb_drivers\src\usbd_int.c)(0x68904AF7)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/usbd_int.o -MMD)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
F (..\Libraries\usb_drivers\src\usb_core.c)(0x67775555)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/usb_core.o -MMD)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
F (..\Libraries\cmsis\cm4\device_support\system_at32a423.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/system_at32a423.o -MMD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\cmsis\cm4\device_support\startup\mdk\startup_at32a423.s)(0x67FF67D5)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 540" -Wa,armasm,--pd,"AT32A423VCT7 SETA 1"

-o ./objects/startup_at32a423.o)
F (..\Libraries\drivers\src\at32a423_acc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_acc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_adc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_adc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_can.c)(0x681C25AB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_can.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_crc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_crc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_crm.c)(0x67FF1917)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_crm.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_dac.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_dac.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_debug.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_debug.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_dma.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_dma.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_ertc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_ertc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_exint.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_exint.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_flash.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_flash.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_gpio.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_gpio.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_i2c.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_i2c.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_misc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_misc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_pwc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_pwc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_scfg.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_scfg.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_spi.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_spi.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_tmr.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_tmr.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_usart.c)(0x6834922B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_usart.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_usb.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_usb.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_wdt.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_wdt.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_wwdt.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_wwdt.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\src\at32a423_xmc.c)(0x67638300)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_xmc.o -MMD)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)()
F (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_dac.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_ertc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)()
F (..\Libraries\drivers\inc\at32a423_i2c.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_wwdt.h)(0x67638300)()
F (..\Libraries\drivers\inc\at32a423_xmc.h)(0x67638300)()
F (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)()
F (..\Libraries\AnoPTv8\AnoPTv8Cmd.c)(0x687D8AE2)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/anoptv8cmd.o -MMD)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)()
F (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.c)(0x68906005)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/anoptv8framefactory.o -MMD)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
F (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)()
F (..\Libraries\AnoPTv8\AnoPTv8Par.c)(0x687D8BB4)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/anoptv8par.o -MMD)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)()
F (..\Libraries\AnoPTv8\AnoPTv8Run.c)(0x6879BB60)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/anoptv8run.o -MMD)
I (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
F (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)()
F (..\Libraries\AnoPTv8\HWInterface.c)(0x68904A45)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/hwinterface.o -MMD)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
F (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)()
F (..\Libraries\AnoPTv8\MotorCmd.c)(0x68788AB7)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/motorcmd.o -MMD)
I (..\Libraries\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\User\SysFSM.h)(0x684067DD)
I (..\Motor\delay.h)(0x67FC677C)
F (..\Libraries\AnoPTv8\MotorCmd.h)(0x6822FFB1)()
F (..\Libraries\AnoPTv8\MotorData.c)(0x68904A7C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/motordata.o -MMD)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
F (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)()
F (..\Libraries\AnoPTv8\MotorParams.c)(0x687F3816)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/motorparams.o -MMD)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)()
F (..\Motor\SysCtl_SysMoore.h)(0x681F2844)()
F (..\Motor\delay.c)(0x677CF8CA)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/delay.o -MMD)
I (..\Motor\delay.h)(0x67FC677C)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\Motor\delay.h)(0x67FC677C)()
F (..\Motor\MathBasic.c)(0x684A3911)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/mathbasic.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\Motor\MathBasic.h)(0x684CEC86)()
F (..\Motor\Motor_VectorControl.c)(0x689167D9)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/motor_vectorcontrol.o -MMD)
I (..\Libraries\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\Libraries\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\Motor\Motor_VectorControl.h)(0x68351A82)()
F (..\Motor\Sys_Isr_Controller.c)(0x68906C04)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_isr_controller.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\System\Sys_Protect.h)(0x683F1C22)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
F (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)()
F (..\Motor\SysCtl_AnalogProcess.c)(0x681DCA6E)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysctl_analogprocess.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)()
F (..\Motor\SysCtl_GlobalVar.c)(0x68247CEF)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysctl_globalvar.o -MMD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)()
F (..\Motor\SysCtl_IoAd2s1210.c)(0x67E10B00)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysctl_ioad2s1210.o -MMD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Motor\delay.h)(0x67FC677C)
F (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)()
F (..\Motor\SysCtl_RotorGet.c)(0x684CE6C1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysctl_rotorget.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)()
F (..\Motor\SysCtl_SysMoore.c)(0x6847DE7C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysctl_sysmoore.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\User\SysFSM.h)(0x684067DD)
F (..\Motor\SysCtl_ConstDef.h)(0x67B43716)()
F (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)()
F (..\Motor\Algorithm.c)(0x67AAE528)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/algorithm.o -MMD)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\System\Sys_BaseValue.c)(0x67A5CCC9)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_basevalue.o -MMD)
F (..\System\Sys_BaseValue.h)(0x67EE3C8E)()
F (..\System\Sys_TimerEvent.c)(0x683FE4BA)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_timerevent.o -MMD)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\System\Sys_TimerEvent.h)(0x683FE4AB)()
F (..\System\start_self_test.c)(0x684017A8)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/start_self_test.o -MMD)
I (..\System\start_self_test.h)(0x683E4817)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\System\Sic_SelfTest.h)(0x67F37AFD)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\USE_Driver\ENC_Speed.h)(0x68472823)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
F (..\System\start_self_test.h)(0x683E4817)()
F (..\System\sys_SpiFlash.c)(0x67EDD735)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_spiflash.o -MMD)
I (..\System\sys_SpiFlash.h)(0x67ECFA75)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\SPI_w25q256.h)(0x67A57890)
F (..\System\sys_SpiFlash.h)(0x67ECFA75)()
F (..\System\Sys_Protect.c)(0x684D26B7)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_protect.o -MMD)
I (..\System\Sys_Protect.h)(0x683F1C22)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\User\SysFSM.h)(0x684067DD)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
F (..\System\Sys_Protect.h)(0x683F1C22)()
F (..\System\Sys_Can.h)(0x684068AF)()
F (..\System\Sys_Can.c)(0x684CF21D)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sys_can.o -MMD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\System\Sys_Can.h)(0x684068AF)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\User\SysFSM.h)(0x684067DD)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
F (..\System\Sic_SelfTest.c)(0x683E4AA9)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sic_selftest.o -MMD)
I (..\System\Sic_SelfTest.h)(0x67F37AFD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\System\start_self_test.h)(0x683E4817)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
F (..\System\Sic_SelfTest.h)(0x67F37AFD)()
F (..\System\wk_system.h)(0x67F31F4C)()
F (..\USE_Driver\cdc_class.c)(0x67775555)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/cdc_class.o -MMD)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\USE_Driver\cdc_class.h)(0x67775555)
I (..\USE_Driver\cdc_desc.h)(0x67C1731A)
F (..\USE_Driver\cdc_desc.c)(0x67775555)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/cdc_desc.o -MMD)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usbd_sdr.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\cdc_desc.h)(0x67C1731A)
I (..\USE_Driver\cdc_class.h)(0x67775555)
F (..\USE_Driver\usb_app.c)(0x68904AB1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/usb_app.o -MMD)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\USE_Driver\cdc_class.h)(0x67775555)
I (..\USE_Driver\cdc_desc.h)(0x67C1731A)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\User\sysTypeDef.h)(0x6858BCEB)
F (..\USE_Driver\cdc_class.h)(0x67775555)()
F (..\USE_Driver\cdc_desc.h)(0x67C1731A)()
F (..\USE_Driver\usb_app.h)(0x68904B7E)()
F (..\USE_Driver\usb_conf.h)(0x6854388A)()
F (..\USE_Driver\LBQ_design.c)(0x683C62E4)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/lbq_design.o -MMD)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\cmsis_armclang.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\User\sysTypeDef.h)(0x6858BCEB)
F (..\USE_Driver\LBQ_design.h)(0x683C55F4)()
F (..\USE_Driver\SPI_w25q256.c)(0x67E6071B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/spi_w25q256.o -MMD)
I (..\USE_Driver\SPI_w25q256.h)(0x67A57890)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\USE_Driver\SPI_w25q256.h)(0x67A57890)()
F (..\USE_Driver\Sensor_Drive.c)(0x683E92F6)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sensor_drive.o -MMD)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\System\Sys_Protect.h)(0x683F1C22)
F (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)()
F (..\USE_Driver\ad2s1212_spi.c)(0x683DCD34)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/ad2s1212_spi.o -MMD)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)()
F (..\USE_Driver\ENC_Speed.c)(0x68906567)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/enc_speed.o -MMD)
I (..\USE_Driver\ENC_Speed.h)(0x68472823)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
F (..\USE_Driver\ENC_Speed.h)(0x68472823)()
F (..\USE_Driver\RS422_CU.c)(0x68351B9D)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/rs422_cu.o -MMD)
I (..\USE_Driver\RS422_CU.h)(0x68348975)
I (..\User\SysFSM.h)(0x684067DD)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
F (..\USE_Driver\RS422_CU.h)(0x68348975)()
F (..\User\at32a423_int.c)(0x683FE681)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_int.o -MMD)
I (..\User\at32a423_int.h)(0x682D433D)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\User\main.h)(0x67EDF302)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\User\Overall_Init.h)(0x67D0D770)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\User\SysFSM.h)(0x684067DD)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\System\Sys_Can.h)(0x684068AF)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\System\Sys_Protect.h)(0x683F1C22)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
F (..\User\at32a423_wk_config.c)(0x6847A68A)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/at32a423_wk_config.o -MMD)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
F (..\User\main.c)(0x6858C07C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/main.o -MMD)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\User\main.h)(0x67EDF302)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\User\Overall_Init.h)(0x67D0D770)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\User\SysFSM.h)(0x684067DD)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
F (..\User\Overall_Init.c)(0x68902BFC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/overall_init.o -MMD)
I (..\User\main.h)(0x67EDF302)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\USE_Driver\usb_app.h)(0x68904B7E)
I (..\Libraries\usb_drivers\inc\usbd_core.h)(0x67775555)
I (..\USE_Driver\usb_conf.h)(0x6854388A)
I (..\Libraries\usb_drivers\inc\usb_std.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usbd_int.h)(0x67775555)
I (..\Libraries\usb_drivers\inc\usb_core.h)(0x67775555)
I (..\User\Overall_Init.h)(0x67D0D770)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\User\SysFSM.h)(0x684067DD)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\System\Sys_Can.h)(0x684068AF)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\System\start_self_test.h)(0x683E4817)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\USE_Driver\RS422_CU.h)(0x68348975)
F (..\User\main.h)(0x67EDF302)()
F (..\User\SysFSM.c)(0x6858BCF1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Libraries/drivers/inc -I ../System -I ../USE_Driver -I ../User -I ../Libraries/cmsis/cm4/core_support -I ../Libraries/cmsis/cm4/device_support -I ../Libraries/usb_drivers/inc -I ../Libraries/AnoPTv8 -I ../Motor -Wno-deprecated-non-prototype

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A423_DFP/2.0.3/Device/Include"

-D__UVISION_VERSION="540" -DAT32A423VCT7 -DAT32A423VCT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A423_V1 -DARM_MATH_LOOP0UNROLL -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING

-o ./objects/sysfsm.o -MMD)
I (..\User\SysFSM.h)(0x684067DD)
I (..\User\sysTypeDef.h)(0x6858BCEB)
I (..\Libraries\cmsis\cm4\device_support\at32a423.h)(0x67638300)
I (..\Libraries\cmsis\cm4\core_support\core_cm4.h)(0x67B8127F)
I (..\Libraries\cmsis\cm4\device_support\system_at32a423.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_def.h)(0x67638300)
I (..\User\at32a423_conf.h)(0x67DBA380)
I (..\Libraries\drivers\inc\at32a423_crm.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_tmr.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_gpio.h)(0x677DE728)
I (..\Libraries\drivers\inc\at32a423_usart.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_pwc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_can.h)(0x67FF261C)
I (..\Libraries\drivers\inc\at32a423_adc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_spi.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_dma.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_debug.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_flash.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_crc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_wdt.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_exint.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_acc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_misc.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_scfg.h)(0x67638300)
I (..\Libraries\drivers\inc\at32a423_usb.h)(0x67638300)
I (..\Libraries\AnoPTv8\AnoPTv8.h)(0x6890600E)
I (..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\Libraries\AnoPTv8\HWInterface.h)(0x68872861)
I (..\Libraries\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\Libraries\AnoPTv8\MotorData.h)(0x68882E98)
I (..\System\Sys_Protect.h)(0x683F1C22)
I (..\USE_Driver\Sensor_Drive.h)(0x683C69DB)
I (..\Libraries\cmsis\cm4\core_support\arm_math.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\arm_math_types.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x67B2E276)
I (..\Libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\Libraries\cmsis\cm4\core_support\dsp\window_functions.h)(0x66A6B132)
I (..\USE_Driver\LBQ_design.h)(0x683C55F4)
I (..\System\Sys_Can.h)(0x684068AF)
I (..\System\Sys_TimerEvent.h)(0x683FE4AB)
I (..\User\at32a423_wk_config.h)(0x681B3EF1)
I (..\System\wk_system.h)(0x67F31F4C)
I (..\Motor\delay.h)(0x67FC677C)
I (..\System\start_self_test.h)(0x683E4817)
I (..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\Motor\MathBasic.h)(0x684CEC86)
I (..\Motor\SysCtl_SysMoore.h)(0x681F2844)
I (..\Motor\SysCtl_AnalogProcess.h)(0x680741D6)
I (..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\Motor\SysCtl_CsvParamDef.h)(0x6820D45E)
I (..\Motor\Motor_VectorControl.h)(0x68351A82)
I (..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\Motor\SysCtl_IoAd2s1210.h)(0x67E10B00)
I (..\Motor\SysCtl_GlobalVar.h)(0x68247CEF)
I (..\Motor\SysVoltBase.h)(0x67FC670C)
I (..\USE_Driver\ad2s1212_spi.h)(0x6825A2DC)
I (..\USE_Driver\RS422_CU.h)(0x68348975)
F (..\User\SysFSM.h)(0x684067DD)()
F (..\User\at32a423_conf.h)(0x67DBA380)()
F (..\User\at32a423_int.h)(0x682D433D)()
F (..\User\at32a423_wk_config.h)(0x681B3EF1)()
F (..\User\Overall_Init.h)(0x67D0D770)()
F (..\User\sysTypeDef.h)(0x6858BCEB)()
