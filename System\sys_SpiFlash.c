/**********************************************************
 * @file     sys_SpiFlash.c
 * @brief    SPI Flash参数存储管理模块
 * <AUTHOR> @date     
 * @version  V1.0.0
 *
 * 设计思路与功能:
 * ----------------------------------------------------------
 * 基于外部SPI Flash的参数存储管理系统：
 * 1. 参数区管理：维护系统运行参数和状态
 * 2. 索引区管理：记录数据存储位置和时间信息
 * 3. 数据区管理：支持多类型数据记录存储和检索
 * 4. 电机参数管理：循环存储电机控制参数配置
 * 
 * 技术实现:
 * ----------------------------------------------------------
 * 1. 分区管理: 将32MB Flash空间分为参数、索引、数据和保留四个区域
 * 2. 循环存储: 实现电机参数的多页循环覆盖存储，提高闪存寿命
 * 3. 类型索引: 支持按类型和时间索引快速检索记录数据
 * 4. 魔数验证: 使用魔数检验数据有效性，确保读取可靠性
 * 5. 参数打包与解包: 在内存参数和存储格式间高效转换
 * 
 * 内存分配设计:
 * ----------------------------------------------------------
 * 1. 系统参数区(Block 0): 
 *    - 大小: 64KB
 *    - 用途: 存储全局管理信息和系统状态
 * 2. 索引区(Block 1-15):
 *    - 大小: 960KB (15 * 64KB)
 *    - 用途: 存储数据区记录的地址索引和时间信息
 * 3. 数据区(Block 16-499):
 *    - 大小: 30.25MB (484 * 64KB)
 *    - 用途: 存储实际记录数据和电机参数
 * 4. 保留区(Block 500-511):
 *    - 大小: 768KB (12 * 64KB)
 *    - 用途: 预留扩展空间
 * 
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 初始化前确保SPI Flash硬件和驱动已正确配置
 * 2. 写入操作需检查剩余空间，避免Flash溢出
 * 3. 读取前应检查魔数有效性，确保数据完整
 * 4. 电机参数使用分页循环存储，注意扇区擦除时机
 **********************************************************/

#include "sys_SpiFlash.h"
#include <string.h> 
/* 全局变量定义 */
Flash_Manager_t gFlash_Manager = {0};

/* 魔数定义 */
#define FLASH_PARAM_MAGIC    0xA5A5A5A5

/* 全局变量 */
static Param_Manager_t gParam_Manager = {0};

/**
  * @brief  Flash初始化
  * @note   初始化Flash管理结构并检查参数区有效性
  */
void Flash_Init(void)
{
    // 初始化SPI Flash
    spiflash_init();
    
    // 读取参数区
    spiflash_read((uint8_t*)&gFlash_Manager.param, FLASH_PARAM_ADDR, sizeof(Flash_Param_t));
    
    // 检查魔数
    if(gFlash_Manager.param.param_magic != FLASH_PARAM_MAGIC) {
        // 参数区无效，初始化参数区
        gFlash_Manager.param.param_magic = FLASH_PARAM_MAGIC;
        gFlash_Manager.param.write_count = 0;
        gFlash_Manager.param.last_write_addr = FLASH_DATA_START_ADDR;
        
        // 擦除并写入参数区
        spiflash_sector_erase(FLASH_PARAM_ADDR/SPIF_SECTOR_SIZE);
        spiflash_write((uint8_t*)&gFlash_Manager.param, FLASH_PARAM_ADDR, sizeof(Flash_Param_t));
    }
    
    // 设置当前写入地址
    gFlash_Manager.current_addr = gFlash_Manager.param.last_write_addr;
    
    // 设置初始化完成标志
    gFlash_Manager.initialized = 1;
}

/**
  * @brief  写入参数
  * @param  data: 要写入的数据指针
  * @param  length: 数据长度
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Write_Params(uint8_t *data, uint32_t length)
{
    Flash_Index_t index;
    uint32_t write_addr;
    
    if(!gFlash_Manager.initialized) return 0;
    
    // 检查剩余空间
    if(gFlash_Manager.current_addr + length > FLASH_DATA_END_BLOCK * FLASH_BLOCK_SIZE) {
        return 0;
    }
    
    // 保存写入地址
    write_addr = gFlash_Manager.current_addr;
    
    // 写入数据
    spiflash_write(data, write_addr, length);
    
    // 更新索引
    index.param_addr = write_addr;
    index.write_time = 0; // TODO: 添加时间戳
    index.valid = 1;
    
    // 写入索引
    spiflash_write((uint8_t*)&index, 
                   FLASH_INDEX_START_ADDR + gFlash_Manager.param.write_count * sizeof(Flash_Index_t),
                   sizeof(Flash_Index_t));
    
    // 更新管理信息
    gFlash_Manager.current_addr += length;
    gFlash_Manager.param.write_count++;
    gFlash_Manager.param.last_write_addr = write_addr;
    
    // 更新参数区
    spiflash_write((uint8_t*)&gFlash_Manager.param, FLASH_PARAM_ADDR, sizeof(Flash_Param_t));
    
    return 1;
}

/**
  * @brief  读取最新参数
  * @param  data: 数据存放缓冲区指针
  * @param  length: 要读取的长度
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Read_Params(uint8_t *data, uint32_t length)
{
    if(!gFlash_Manager.initialized) return 0;
    
    // 从最后写入的地址读取数据
    spiflash_read(data, gFlash_Manager.param.last_write_addr, length);
    
    return 1;
}

/**
  * @brief  擦除所有数据
  * @note   擦除所有参数数据并重置管理信息
  */
void Flash_Erase_All(void)
{
    uint32_t block;
    
    // 擦除参数区
    spiflash_sector_erase(FLASH_PARAM_BLOCK);
    
    // 擦除索引区
    for(block = FLASH_INDEX_START_BLOCK; block <= FLASH_INDEX_END_BLOCK; block++) {
        spiflash_sector_erase(block);
    }
    
    // 擦除数据区
    for(block = FLASH_DATA_START_BLOCK; block <= FLASH_DATA_END_BLOCK; block++) {
        spiflash_sector_erase(block);
    }
    
    // 重置管理信息
    gFlash_Manager.param.write_count = 0;
    gFlash_Manager.param.last_write_addr = FLASH_DATA_START_ADDR;
    gFlash_Manager.current_addr = FLASH_DATA_START_ADDR;
    
    // 重新初始化参数区
    Flash_Init();
}

/**
  * @brief  获取剩余空间
  * @retval 剩余字节数
  */
uint32_t Flash_Get_Free_Size(void)
{
    if(!gFlash_Manager.initialized) return 0;
    
    return (FLASH_DATA_END_BLOCK * FLASH_BLOCK_SIZE) - gFlash_Manager.current_addr;
}

/**
  * @brief  写入一条记录
  * @param  record: 记录数据指针
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Write_Record(Param_Record_t *record)
{
    if(!gFlash_Manager.initialized) return 0;
    
    // 检查剩余空间
    if(Flash_Get_Free_Size() < sizeof(Param_Record_t)) {
        return 0;
    }
    
    // 写入记录
    if(!Flash_Write_Params((uint8_t*)record, sizeof(Param_Record_t))) {
        return 0;
    }
    
    // 更新记录计数
    switch(record->data_type) {
        case DATA_TYPE_SELFTEST:
            gParam_Manager.selftest_count++;
            break;
        case DATA_TYPE_NORMAL:
            gParam_Manager.normal_count++;
            break;
        case DATA_TYPE_WARNING:
            gParam_Manager.warning_count++;
            break;
        case DATA_TYPE_FAULT:
            gParam_Manager.fault_count++;
            break;
        default:
            return 0;
    }
    
    gParam_Manager.total_records++;
    gParam_Manager.last_record_addr = gFlash_Manager.current_addr - sizeof(Param_Record_t);
    
    return 1;
}

/**
  * @brief  读取最后一条记录
  * @param  record: 记录数据存放指针
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Read_Last_Record(Param_Record_t *record)
{
    if(!gFlash_Manager.initialized || gParam_Manager.total_records == 0) {
        return 0;
    }
    
    return Flash_Read_Params((uint8_t*)record, sizeof(Param_Record_t));
}

/**
  * @brief  按类型读取记录
  * @param  type: 记录类型
  * @param  index: 记录索引(从0开始)
  * @param  record: 记录数据存放指针
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Read_Record_ByType(uint8_t type, uint32_t index, Param_Record_t *record)
{
    uint32_t addr;
    uint32_t count = 0;
    Param_Record_t temp_record;
    
    // 检查参数
    if(!gFlash_Manager.initialized || !record) return 0;
    
    // 从数据区开始扫描
    addr = FLASH_DATA_START_ADDR;
    while(addr < gFlash_Manager.current_addr) {
        // 读取一条记录
        spiflash_read((uint8_t*)&temp_record, addr, sizeof(Param_Record_t));
        
        // 检查类型
        if(temp_record.data_type == type) {
            if(count == index) {
                // 找到目标记录
                memcpy(record, &temp_record, sizeof(Param_Record_t));
                return 1;
            }
            count++;
        }
        
        addr += sizeof(Param_Record_t);
    }
    
    return 0;
}

/**
  * @brief  获取指定类型的记录数
  * @param  type: 记录类型
  * @retval 记录数量
  */
uint32_t Flash_Get_Record_Count(uint8_t type)
{
    switch(type) {
        case DATA_TYPE_SELFTEST:
            return gParam_Manager.selftest_count;
        case DATA_TYPE_NORMAL:
            return gParam_Manager.normal_count;
        case DATA_TYPE_WARNING:
            return gParam_Manager.warning_count;
        case DATA_TYPE_FAULT:
            return gParam_Manager.fault_count;
        default:
            return 0;
    }
}
/**
  * @brief  查找最新的有效参数页
  * @param  none
  * @retval 页索引(0-7)，返回0xFF表示无有效参数
  */
static uint8_t Find_Latest_Param_Page(void)
{
    uint16_t magic;
    uint8_t i;
    
    // 从最后一页开始查找
    for(i = MOTOR_PARAM_PAGE_COUNT - 1; i < MOTOR_PARAM_PAGE_COUNT; i--) {
        spiflash_read((uint8_t*)&magic, 
                     MOTOR_PARAM_ADDR + i * MOTOR_PARAM_PAGE_SIZE, 
                     sizeof(magic));
        if(magic == MOTOR_PARAM_MAGIC) {
            return i;
        }
    }
    
    return 0xFF;
}

/**
  * @brief  写入电机参数(循环存储)
  * @param  param: 参数数据指针
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Write_Motor_Param(Motor_Param_t *param)
{
    uint8_t next_page;
    uint32_t write_addr;
    
    if(!gFlash_Manager.initialized || !param) return 0;
    
    // 设置魔数
    param->magic = MOTOR_PARAM_MAGIC;
    
    // 查找最新参数页
    next_page = Find_Latest_Param_Page();
    if(next_page == 0xFF) {
        // 无有效参数，从第0页开始
        next_page = 0;
    } else {
        // 计算下一页
        next_page = (next_page + 1) % MOTOR_PARAM_PAGE_COUNT;
    }
    
    // 计算写入地址
    write_addr = MOTOR_PARAM_ADDR + next_page * MOTOR_PARAM_PAGE_SIZE;
    
    // 擦除扇区(如果是该扇区的第一页)
    if(next_page == 0) {
        spiflash_sector_erase(MOTOR_PARAM_SECTOR);
    }
    
    // 写入参数
    spiflash_write((uint8_t*)param, write_addr, sizeof(Motor_Param_t));
    
    return 1;
}

/**
  * @brief  读取电机参数
  * @param  param: 参数数据存放指针
  * @retval 0:失败 1:成功
  */
uint8_t Flash_Read_Motor_Param(Motor_Param_t *param)
{
    uint8_t page;
    uint32_t read_addr;
    
    if(!gFlash_Manager.initialized || !param) return 0;
    
    // 查找最新参数页
    page = Find_Latest_Param_Page();
    if(page == 0xFF) {
        return 0;  // 无有效参数
    }
    
    // 计算读取地址
    read_addr = MOTOR_PARAM_ADDR + page * MOTOR_PARAM_PAGE_SIZE;
    
    // 读取参数
    spiflash_read((uint8_t*)param, read_addr, sizeof(Motor_Param_t));
    
    // 检查魔数
    if(param->magic != MOTOR_PARAM_MAGIC) {
        return 0;
    }
    
    return 1;
}

/**
  * @brief  将电机参数打包为存储格式
  * @param  src: MotorParamPtrs_t结构体指针(源数据)
  * @param  dst: Motor_Param_t结构体指针(目标存储格式)
  * @retval none
  */
void Pack_Motor_Param(MotorParamPtrs_t *src, Motor_Param_t *dst)
{
    // 设置头部信息
    dst->magic = MOTOR_PARAM_MAGIC;
    dst->version = 0x0001;  // 当前版本号
    
    // 基本控制参数
    dst->ConTc = *src->ConTc;
    dst->GiveUdc = *src->GiveUdc;
    
    // 电机额定参数
    dst->MotorRatedVoltage = *src->MotorRatedVoltage;
    dst->MotorRatedCurrent = *src->MotorRatedCurrent;
    dst->MotorRatedFre = *src->MotorRatedFre;
    dst->MotorRatedPower = *src->MotorRatedPower;
    dst->MotorRateSpeed = *src->MotorRateSpeed;
    dst->MotorPoleNum = *src->MotorPoleNum;
    dst->MotorRatedSlip = *src->MotorRatedSlip;
    
    // 永磁同步电机参数
    dst->SynRs = *src->SynRs;
    dst->SynLsd = *src->SynLsd;
    dst->SynLsq = *src->SynLsq;
    dst->SynFlux = *src->SynFlux;
    
    // 速度环PI参数
    dst->SynVcSpeedKi = *src->SynVcSpeedKi;
    dst->SynVcSpeedKp = *src->SynVcSpeedKp;
    
    // 电流环PI参数
    dst->SynVcCurrentKi = *src->SynVcCurrentKi;
    dst->SynVcCurrentKp = *src->SynVcCurrentKp;
    dst->SynVcCurrentKi2 = *src->SynVcCurrentKi2;
    dst->SynVcCurrentKp2 = *src->SynVcCurrentKp2;
    dst->SynVcCurrentKi1 = *src->SynVcCurrentKi1;
    dst->SynVcCurrentKp1 = *src->SynVcCurrentKp1;
    
    // 磁链环PI参数
    dst->SynVcFWKp = *src->SynVcFWKp;
    dst->SynVcFWKi = *src->SynVcFWKi;
    dst->SynVcLoseSpeedValue = *src->SynVcLoseSpeedValue;
    dst->fluxidmin = *src->fluxidmin;
    
    // 其他控制参数
    dst->DBT = *src->DBT;
    dst->DBEN = *src->DBEN;
    dst->PosCompEN = *src->PosCompEN;
    dst->feedforwordFlag = *src->feedforwordFlag;
    dst->ConRunType = *src->ConRunType;
    dst->ConStrategy = *src->ConStrategy;
    dst->VFDLoadType = *src->VFDLoadType;
    dst->Speed_mid = *src->Speed_mid;
    dst->Speed_high = *src->Speed_high;
    dst->marslpfw = *src->marslpfw;
}

/**
  * @brief  从存储格式恢复电机参数
  * @param  src: Motor_Param_t结构体指针(存储的数据)
  * @param  dst: MotorParamPtrs_t结构体指针(目标参数指针)
  * @retval none
  */
void Unpack_Motor_Param(Motor_Param_t *src, MotorParamPtrs_t *dst)
{
    // 检查魔数
    if(src->magic != MOTOR_PARAM_MAGIC) return;
    
    // 基本控制参数
    *dst->ConTc = src->ConTc;
    *dst->GiveUdc = src->GiveUdc;
    
    // 电机额定参数
    *dst->MotorRatedVoltage = src->MotorRatedVoltage;
    *dst->MotorRatedCurrent = src->MotorRatedCurrent;
    *dst->MotorRatedFre = src->MotorRatedFre;
    *dst->MotorRatedPower = src->MotorRatedPower;
    *dst->MotorRateSpeed = src->MotorRateSpeed;
    *dst->MotorPoleNum = src->MotorPoleNum;
    *dst->MotorRatedSlip = src->MotorRatedSlip;
    
    // 永磁同步电机参数
    *dst->SynRs = src->SynRs;
    *dst->SynLsd = src->SynLsd;
    *dst->SynLsq = src->SynLsq;
    *dst->SynFlux = src->SynFlux;
    
    // 速度环PI参数
    *dst->SynVcSpeedKi = src->SynVcSpeedKi;
    *dst->SynVcSpeedKp = src->SynVcSpeedKp;
    
    // 电流环PI参数
    *dst->SynVcCurrentKi = src->SynVcCurrentKi;
    *dst->SynVcCurrentKp = src->SynVcCurrentKp;
    *dst->SynVcCurrentKi2 = src->SynVcCurrentKi2;
    *dst->SynVcCurrentKp2 = src->SynVcCurrentKp2;
    *dst->SynVcCurrentKi1 = src->SynVcCurrentKi1;
    *dst->SynVcCurrentKp1 = src->SynVcCurrentKp1;
    
    // 磁链环PI参数
    *dst->SynVcFWKp = src->SynVcFWKp;
    *dst->SynVcFWKi = src->SynVcFWKi;
    *dst->SynVcLoseSpeedValue = src->SynVcLoseSpeedValue;
    *dst->fluxidmin = src->fluxidmin;
    
    // 其他控制参数
    *dst->DBT = src->DBT;
    *dst->DBEN = src->DBEN;
    *dst->PosCompEN = src->PosCompEN;
    *dst->feedforwordFlag = src->feedforwordFlag;
    *dst->ConRunType = src->ConRunType;
    *dst->ConStrategy = src->ConStrategy;
    *dst->VFDLoadType = src->VFDLoadType;
    *dst->Speed_mid = src->Speed_mid;
    *dst->Speed_high = src->Speed_high;
    *dst->marslpfw = src->marslpfw;
} 