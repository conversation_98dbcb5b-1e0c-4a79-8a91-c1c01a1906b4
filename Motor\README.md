# 高性能电流环控制系统

基于VESC架构的裸机电流环实现，支持编码器和非线性磁链观测器。

## 文件结构

- `motor_current_loop.h` - 电流环控制头文件
- `motor_current_loop.c` - 电流环控制实现
- `motor_math_utils.c` - 数学工具函数实现
- `adc_pmsm.h/c` - ADC电流采样模块

## 主要特性

1. **高性能FOC控制**
   - 20kHz电流环频率
   - dq轴解耦PI控制
   - Clarke和Park变换
   - 空间矢量PWM(SVPWM)

2. **角度补偿**
   - 编码器零点偏移补偿
   - 相位超前补偿
   - 开关频率相位滞后补偿
   - 编码器方向和传动比补偿

3. **解耦控制**
   - 交叉解耦控制
   - 反电动势补偿
   - 温度补偿

4. **电机参数管理**
   - 完整的电机参数结构
   - 温度补偿
   - 在线参数调整

5. **非线性磁链观测器**
   - Ortega观测器算法
   - MXLEMMING观测器算法
   - 锁相环(PLL)相位跟踪
   - 编码器/观测器智能切换

## 使用方法

### 1. 初始化

```c
#include "motor_current_loop.h"

// 获取电流环实例
current_loop_t *loop = current_loop_get_instance();

// 快速初始化
motor_current_loop_quick_init();

// 或者自定义初始化
current_loop_init(loop);

// 设置电机参数
motor_params_t motor_params = {
    .rs = 0.05f,              // 定子电阻 50mΩ
    .ld = 0.0001f,            // d轴电感 100μH
    .lq = 0.0001f,            // q轴电感 100μH
    .flux_linkage = 0.01f,    // 磁链 10mWb
    .pole_pairs = 4,          // 4极对
    .rated_current = 50.0f,   // 额定电流 50A
    .rated_voltage = 350.0f,  // 额定电压 350V
    .temp_coeff_rs = 0.004f,  // 温度系数
    .temp_ref = 25.0f         // 参考温度
};
motor_params_set(loop, &motor_params);

// 配置观测器参数
loop->config.observer.type = OBSERVER_ORTEGA_ORIGINAL;  // 观测器类型
loop->config.observer.gain = 1000.0f;                  // 观测器增益
loop->config.observer.pll_kp = 2000.0f;                // PLL比例增益
loop->config.observer.pll_ki = 40000.0f;               // PLL积分增益
loop->config.observer.sl_erpm = 2500.0f;               // 切换转速阈值
loop->config.observer.enable_switch = TRUE;            // 使能自动切换
```

### 2. 中断集成

```c
// 在PWM中断中调用(20kHz)
void TMR1_OVF_IRQHandler(void)
{
    if (tmr_flag_get(TMR1, TMR_OVF_FLAG) != RESET) {
        tmr_flag_clear(TMR1, TMR_OVF_FLAG);
        
        // 调用电流环中断处理
        motor_current_loop_interrupt_handler();
    }
}
```

### 3. 启动控制

```c
// 设置目标电流
current_loop_set_current_target(loop, 10.0f);  // 10A

// 或设置dq轴电流
current_loop_set_current_dq(loop, 0.0f, 10.0f);  // id=0, iq=10A

// 启动电流环
motor_current_loop_start(10.0f);
```

### 4. 状态监控

```c
// 获取电流环状态
float id = current_loop_get_id(loop);
float iq = current_loop_get_iq(loop);
float vd = current_loop_get_vd(loop);
float vq = current_loop_get_vq(loop);
float duty = current_loop_get_duty_cycle(loop);

// 获取观测器状态
float obs_phase = observer_get_phase(loop);     // 观测器相位
float obs_speed = observer_get_speed(loop);     // 观测器速度
bool using_encoder = loop->state.using_encoder; // 当前是否使用编码器
float pll_phase = loop->state.pll_phase;        // PLL相位
float pll_speed = loop->state.pll_speed;        // PLL速度
```

### 5. 角度补偿

```c
// 设置编码器零点偏移
angle_compensation_set_encoder_offset(loop, 0.1f);  // 0.1rad偏移

// 设置相位超前补偿
angle_compensation_set_phase_advance(loop, 0.05f);  // 0.05rad超前

// 设置传感器模式
loop->config.sensor_mode = SENSOR_MODE_ENCODER;     // 编码器模式
// loop->config.sensor_mode = SENSOR_MODE_OBSERVER; // 观测器模式
// loop->config.sensor_mode = SENSOR_MODE_HYBRID;   // 混合模式(自动切换)
```

## 硬件接口

需要用户实现以下硬件接口宏：

```c
// PWM输出接口
#define SET_PWM_DUTY_A(duty)    /* 用户实现A相PWM */
#define SET_PWM_DUTY_B(duty)    /* 用户实现B相PWM */
#define SET_PWM_DUTY_C(duty)    /* 用户实现C相PWM */

// PWM使能控制
#define ENABLE_PWM_OUTPUT()     /* 用户实现PWM使能 */
#define DISABLE_PWM_OUTPUT()    /* 用户实现PWM禁用 */
```

## 配置参数

主要配置参数包括：

- `current_kp/ki` - PI控制器增益
- `max_current/voltage/duty` - 限制参数
- `decoupling_mode` - 解耦模式
- `temp_comp_enabled` - 温度补偿使能
- `sensor_mode` - 传感器模式
- `observer.type` - 观测器类型(ORTEGA_ORIGINAL/MXLEMMING)
- `observer.gain` - 观测器增益
- `observer.pll_kp/ki` - PLL控制器增益
- `observer.sl_erpm` - 编码器/观测器切换转速
- `observer.enable_switch` - 使能自动切换

## 注意事项

1. 确保电流环频率为20kHz
2. 中断处理函数执行时间应小于50μs
3. 根据实际电机调整参数
4. 定期监控系统稳定性
5. 实现必要的保护功能

## 观测器算法

基于VESC的非线性磁链观测器实现，包含：

1. **Ortega观测器**
   - 基于磁链幅值约束的非线性观测器
   - 状态方程：dx/dt = v - R*i + γ/2 * (x - L*i) * err
   - 约束条件：|x - L*i| = λ (磁链幅值恒定)

2. **MXLEMMING观测器**
   - 改进的线性观测器，直接观测磁链矢量
   - 更好的数值稳定性和收敛性

3. **锁相环(PLL)**
   - 相位和速度平滑跟踪
   - 滤除观测器输出中的噪声
   - 提高控制系统稳定性

4. **智能切换**
   - 低速使用编码器(精度高)
   - 高速使用观测器(响应快)
   - 滞回控制避免振荡
