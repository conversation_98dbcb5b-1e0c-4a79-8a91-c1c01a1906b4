#include "MotorParams.h"
#include "AnoPTv8Par.h"
#include <string.h>

// 参数数组定义
float gMotorParams[MOTOR_PARAM_NUM] = {
    0.1f,       // 00采样时间s
    580.0f,     // 01母线电压Udc
    580.0f,     // 02电机额定电压
    50.0f,      // 03电机额定电流
    1800.0f,    // 04电机额定频率
    35000.0f,   // 05电机额定功率
    3000.0f,    // 06电机额定转速
    8.0f,       // 07电机极对数
    0.0f,       // 08电机额定滑差
    0.0f,       // 09定子电阻
    0.000325f,  // 10d轴电感
    0.000369f,  // 11q轴电感
    0.113f,     // 12电机磁链
    
    // 低速区域参数
    0.005f,    // 13 低速转速环积分增益
    10.0f,       // 14 低速转速环比例增益
    0.02f,       // 15 低速d轴电流环积分增益
    2.5f,       // 16 低速d轴电流环比例增益
    0.02f,       // 17 低速q轴电流环积分增益
    2.5f,       // 18 低速q轴电流环比例增益
    
    // 中速区域参数
    0.08f,      // 19 中速转速环积分增益
    0.8f,       // 20 中速转速环比例增益
    0.15f,      // 21 中速d轴电流环积分增益
    1.5f,       // 22 中速d轴电流环比例增益
    0.15f,      // 23 中速q轴电流环积分增益
    1.5f,       // 24 中速q轴电流环比例增益
    
    // 高速区域参数
    0.05f,      // 25 高速转速环积分增益
    0.6f,       // 26 高速转速环比例增益
    0.2f,       // 27 高速d轴电流环积分增益
    2.0f,       // 28 高速d轴电流环比例增益
    0.2f,       // 29 高速q轴电流环积分增益
    2.0f,       // 30 高速q轴电流环比例增益
    
    // 磁链PI参数
    1.0f,       // 31 磁链PI比例增益
    0.1f,       // 32 磁链PI积分增益
    
    // 其他参数
    12000.0f,   // 33 失速速度值
    0.1f,       // 34 磁链输出最小限幅
    2.0f,       // 35 死区时间us
    0.0f,       // 36 死区补偿使能
    0.0f,       // 37 正补偿使能
    0.0f,       // 38 前馈补偿标志
    0.0f,       // 39 变频器控制方式
    2.0f,       // 40 控制策略：有感矢量
    1.0f,       // 41 负载类型
    3000.0f,    // 42 分段PI，中速阈值
    8000.0f,    // 43 分段PI，高速阈值
    0.1f,       // 44 LPF滤波系数
    334.86f,      // 45 电流环输出限幅
    80.0f,      // 46 速度环输出限幅
    80.0f,      // 47 电流限幅
    0.0f,       // 48 预定位电流(A)
    0.0f,       // 49 给定id电流(A)
    0.0f,       // 50 给定iq电流(A)
    100.0f,     // 51 给定速度(rpm)
    0.0f,       // 52 控制环路模式
    22.0f,      // 53 开环频率设定值(Hz)
    4.0f,       // 54 频率步长增量(Hz/s)
    100.0f,     // 55 最大频率限制(Hz)
    1.0f,       // 56 开环角度增量系数
    1.0f,       // 57 预定位使能
    0.0f        // 58 设置固定角度
};

// 参数信息定义
const _st_par_info _motorParInfoList[MOTOR_PARAM_NUM] = {
    //参数ID: 参数指针:                     最小值:  最大值:  缩放倍数:  参数名称:         参数信息:
    {0,      pCsvParameConTc,                0,      1000,    8,        "采样时间",        "us"},
    {1,      pCsvParamGiveUdc,               0,      1000,    8,        "母线电压",        "V"},
    {2,      pCsvParamMotorRatedVoltage,     0,      1000,    8,        "额定电压",        "V"},
    {3,      pCsvParamMotorRatedCurrent,     0,      1000,    8,        "额定电流",        "A"},
    {4,      pCsvParamMotorRatedFre,         0,      2000,    8,        "额定频率",        "Hz"},
    {5,      pCsvParamMotorRatedPower,       0,      100000,  8,        "额定功率",        "W"},
    {6,      pCsvParamMotorRateSpeed,        0,      10000,   8,        "额定转速",        "rpm"},
    {7,      pCsvParamMotorPoleNum,          0,      100,     8,        "极对数",          "无"},
    {8,      pCsvParamMotorRatedSlip,        0,      100,     8,        "额定滑差",        "无"},
    {9,      pCsvParamSynRs,                 0,      100,     8,        "定子电阻",        "Ω"},
    {10,     pCsvParamSynLsd,                0,      8,       8,        "d轴电感",        "H"},
    {11,     pCsvParamSynLsq,                0,      8,       8,        "q轴电感",        "H"},
    {12,     pCsvParamSynFlux,               0,      10,      8,        "磁链值",          "Wb"},

    // 低速区域参数信息
    {13,     pCsvParamSynVcSpeedKi,          0,      1000,    8,        "低速转速积分",    "无"},
    {14,     pCsvParamSynVcSpeedKp,          0,      1000,    8,        "低速转速比例",    "无"},
    {15,     pCsvParamSynVcCurrentDKi,       0,      1000,    8,        "低速d轴积分",     "无"},
    {16,     pCsvParamSynVcCurrentDKp,       0,      1000,    8,        "低速d轴比例",     "无"},
    {17,     pCsvParamSynVcCurrentQKi,       0,      1000,    8,        "低速q轴积分",     "无"},
    {18,     pCsvParamSynVcCurrentQKp,       0,      1000,    8,        "低速q轴比例",     "无"},

    // 中速区域参数信息
    {19,     pCsvParamSynVcSpeedKi1,         0,      1000,    8,        "中速转速积分",    "无"},
    {20,     pCsvParamSynVcSpeedKp1,         0,      1000,    8,        "中速转速比例",    "无"},
    {21,     pCsvParamSynVcCurrentDKi1,      0,      1000,    8,        "中速d轴积分",     "无"},
    {22,     pCsvParamSynVcCurrentDKp1,      0,      1000,    8,        "中速d轴比例",     "无"},
    {23,     pCsvParamSynVcCurrentQKi1,      0,      1000,    8,        "中速q轴积分",     "无"},
    {24,     pCsvParamSynVcCurrentQKp1,      0,      1000,    8,        "中速q轴比例",     "无"},

    // 高速区域参数信息
    {25,     pCsvParamSynVcSpeedKi2,         0,      1000,    8,        "高速转速积分",    "无"},
    {26,     pCsvParamSynVcSpeedKp2,         0,      1000,    8,        "高速转速比例",    "无"},
    {27,     pCsvParamSynVcCurrentDKi2,      0,      1000,    8,        "高速d轴积分",     "无"},
    {28,     pCsvParamSynVcCurrentDKp2,      0,      1000,    8,        "高速d轴比例",     "无"},
    {29,     pCsvParamSynVcCurrentQKi2,      0,      1000,    8,        "高速q轴积分",     "无"},
    {30,     pCsvParamSynVcCurrentQKp2,      0,      1000,    8,        "高速q轴比例",     "无"},

    // 磁链环PI参数信息
    {31,     pCsvParamSynVcFWKp,             0,      1000,    8,        "磁链比例",        "无"},
    {32,     pCsvParamSynVcFWKi,             0,      1000,    8,        "磁链积分",        "无"},

    // 其他参数信息
    {33,     pCsvParamSynVcLoseSpeedValue,   0,      10000,   8,        "失速速度",        "rpm"},
    {34,     pCsvParamfluxidmin,             0,      1000,    8,        "磁链最小限幅",    "无"},
    {35,     pCsvParamDBT,                   0,      100,     8,        "死区时间",        "us"},
    {36,     pCsvParamDBEN,                  0,      8,       8,        "死区补偿使能",    "无"},
    {37,     pCsvParamPosCompEN,             0,      8,       8,        "正补偿使能",      "无"},
    {38,     pCsvParamfeedforwordFlag,       0,      8,       8,        "前馈补偿标志",    "无"},
    {39,     pCsvParaConRunType,             0,      10,      8,        "运行方式",        "无"},
    {40,     pCsvParaConStrategy,            0,      10,      8,        "控制策略",        "无"},
    {41,     pCsvParaVFDLoadType,            0,      10,      8,        "负载类型",        "无"},
    {42,     pCsvParamSpeed_mid,             0,      10000,   8,        "中速阈值",        "rpm"},
    {43,     pCsvParamSpeed_high,            0,      10000,   8,        "高速阈值",        "rpm"},
    {44,     pCsvParammarslpfw,              0,      8,       8,        "滤波系数",        "无"},
    {45,     pCsvParamSynVcUqmax,            0,      35,      8,        "电流环限幅",      "无"},
    {46,     pCsvParamSynVcIqmax,            0,      30,      8,        "速度环限幅",      "无"},
    {47,     pCsvParamCurrentLimit,          0,      100,     8,        "电流限幅",        "A"},
    {48,     pCsvParamPreid_ref,             0,      10,      8,        "预定位电流",      "A"},
    {49,     pCsvParamId_ref,                0,      10,      8,        "给定d轴电流",     "A"},
    {50,     pCsvParamIq_ref,                0,      10,      8,        "给定q轴电流",     "A"},
    {51,     pCsvParamSpeed_ref,             0,      12000,   8,        "给定速度",        "rpm"},
    {52,     pCsvControlLoopMode,            0,      10,      8,        "控制模式",        "0:闭环1:电流2:开环3:预定位"},
    {53,     pCsvParamOpenLoopFreq,          0,      1000,    8,        "开环频率",        "Hz"},
    {54,     pCsvParamOpenLoopFreqStep,      0,      100,     8,        "频率步长",        "Hz/s"},
    {55,     pCsvParamOpenLoopFreqMax,       0,      1000,    8,        "最大频率",        "Hz"},
    {56,     pCsvParamOpenLoopAngleCoef,     0,      100,     8,        "角度系数",        "无"},
    {57,     pCsvParamPrePositionEN,         0,      8,       8,        "预定位使能",      "无"},
    {58,     pCsvParamSetAngle,              0,      360,     8,        "设置角度",        "°"}
};


/**
 * @brief 电机参数初始化
 * @details 将所有电机参数注册到AnoPTv8系统中
 */
void MotorParamsInit(void)
{
    static uint8_t _alreadyInit = 0;
    if(_alreadyInit)
        return;
    _alreadyInit = 1;

    uint8_t _c = MOTOR_PARAM_NUM;
    for(int i=0; i<_c; i++)
        AnoPTv8ParRegister(&_motorParInfoList[i]);
}