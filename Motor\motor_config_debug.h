/**********************************************************
 * @file     motor_config_debug.h
 * @brief    电机控制配置和调试接口头文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     提供参数配置、在线调试、性能监控等功能
 *           兼容AnoPTv8调试协议
***********************************************************/

#ifndef _MOTOR_CONFIG_DEBUG_H
#define _MOTOR_CONFIG_DEBUG_H

#include "motor_current_loop.h"
#include "sysTypeDef.h"

/*============================ 调试配置宏定义 ============================*/

// 调试功能使能开关
#define MOTOR_DEBUG_ENABLE              1           // 使能调试功能
#define MOTOR_PERFORMANCE_MONITOR       1           // 使能性能监控
#define MOTOR_PARAMETER_TUNING          1           // 使能参数在线调整
#define MOTOR_DATA_LOGGING              1           // 使能数据记录

// 调试数据发送频率
#define DEBUG_DATA_SEND_FREQ_HZ         100         // 调试数据发送频率 100Hz
#define DEBUG_DATA_SEND_PERIOD_MS       10          // 调试数据发送周期 10ms

// 性能监控阈值
#define EXEC_TIME_WARNING_US            40.0f       // 执行时间警告阈值(μs)
#define EXEC_TIME_ERROR_US              50.0f       // 执行时间错误阈值(μs)
#define CURRENT_ERROR_WARNING_A         2.0f        // 电流误差警告阈值(A)
#define CURRENT_ERROR_ERROR_A           5.0f        // 电流误差错误阈值(A)

/*============================ 调试数据结构 ============================*/

/**
 * @brief 电流环调试数据结构体
 */
typedef struct {
    // 基本状态
    float id_ref;                   // d轴电流参考值(A)
    float iq_ref;                   // q轴电流参考值(A)
    float id_actual;                // d轴实际电流(A)
    float iq_actual;                // q轴实际电流(A)
    float id_error;                 // d轴电流误差(A)
    float iq_error;                 // q轴电流误差(A)
    
    // 电压输出
    float vd_output;                // d轴电压输出(V)
    float vq_output;                // q轴电压输出(V)
    float v_alpha;                  // α轴电压(V)
    float v_beta;                   // β轴电压(V)
    
    // 三相信息
    float ia, ib, ic;               // 三相电流(A)
    float va, vb, vc;               // 三相电压(V)
    float duty_a, duty_b, duty_c;   // 三相占空比
    
    // 系统状态
    float theta_elec;               // 电角度(rad)
    float omega_elec;               // 电角速度(rad/s)
    float v_bus;                    // 母线电压(V)
    float i_bus;                    // 母线电流(A)
    float temp_motor;               // 电机温度(°C)
    
    // 性能统计
    float exec_time_us;             // 执行时间(μs)
    float max_exec_time_us;         // 最大执行时间(μs)
    float avg_exec_time_us;         // 平均执行时间(μs)
    uint32_t loop_counter;          // 循环计数器
    
    // 控制器状态
    float pi_id_integral;           // d轴PI积分项
    float pi_iq_integral;           // q轴PI积分项
    float d_gain_scale;             // d轴增益缩放因子
    
    // 故障状态
    bool_t fault_flag;              // 故障标志
    uint32_t fault_code;            // 故障代码
    
} motor_debug_data_t;

/**
 * @brief 电流环可调参数结构体
 */
typedef struct {
    // PI控制器参数
    float current_kp;               // 电流环比例增益
    float current_ki;               // 电流环积分增益
    float d_gain_scale_start;       // d轴增益缩放起始点
    float d_gain_scale_max_mod;     // d轴增益缩放最大调制度
    
    // 滤波参数
    float current_filter_const;     // 电流滤波常数
    float voltage_filter_const;     // 电压滤波常数
    
    // 限制参数
    float max_current;              // 最大电流(A)
    float max_voltage;              // 最大电压(V)
    float max_duty;                 // 最大占空比
    
    // 解耦控制
    decoupling_mode_e decoupling_mode;  // 解耦模式
    bool_t temp_comp_enabled;       // 温度补偿使能
    
    // 电机参数
    float motor_rs;                 // 定子电阻(Ω)
    float motor_ld;                 // d轴电感(H)
    float motor_lq;                 // q轴电感(H)
    float motor_flux_linkage;       // 磁链(Wb)
    
} motor_tunable_params_t;

/**
 * @brief 性能监控结构体
 */
typedef struct {
    // 执行时间统计
    float exec_time_min_us;         // 最小执行时间(μs)
    float exec_time_max_us;         // 最大执行时间(μs)
    float exec_time_avg_us;         // 平均执行时间(μs)
    uint32_t exec_time_samples;     // 执行时间采样数
    
    // 电流误差统计
    float current_error_max_a;      // 最大电流误差(A)
    float current_error_rms_a;      // 电流误差RMS值(A)
    uint32_t current_error_samples; // 电流误差采样数
    
    // 系统负载统计
    float cpu_usage_percent;        // CPU使用率(%)
    uint32_t interrupt_count;       // 中断计数
    uint32_t overrun_count;         // 中断超时计数
    
    // 故障统计
    uint32_t fault_count;           // 故障次数
    uint32_t last_fault_code;       // 最后故障代码
    uint32_t fault_timestamp;       // 故障时间戳
    
} motor_performance_monitor_t;

/*============================ 函数声明 ============================*/

// 调试数据获取函数
ret_t motor_debug_get_data(motor_debug_data_t *debug_data);
ret_t motor_debug_get_tunable_params(motor_tunable_params_t *params);
ret_t motor_debug_get_performance_monitor(motor_performance_monitor_t *monitor);

// 参数调整函数
ret_t motor_debug_set_tunable_params(const motor_tunable_params_t *params);
ret_t motor_debug_set_pi_params(float kp, float ki);
ret_t motor_debug_set_filter_params(float current_filter, float voltage_filter);
ret_t motor_debug_set_limit_params(float max_current, float max_voltage, float max_duty);
ret_t motor_debug_set_motor_params(float rs, float ld, float lq, float flux_linkage);

// 控制命令函数
ret_t motor_debug_set_current_ref(float id_ref, float iq_ref);
ret_t motor_debug_enable_control(bool_t enable);
ret_t motor_debug_reset_controller(void);
ret_t motor_debug_clear_fault(void);

// 性能监控函数
ret_t motor_debug_performance_monitor_init(void);
ret_t motor_debug_performance_monitor_update(void);
ret_t motor_debug_performance_monitor_reset(void);

// 数据记录函数
ret_t motor_debug_data_logging_start(void);
ret_t motor_debug_data_logging_stop(void);
ret_t motor_debug_data_logging_save(void);

// AnoPTv8协议接口函数
ret_t motor_debug_anopt_init(void);
ret_t motor_debug_anopt_send_data(void);
ret_t motor_debug_anopt_process_command(uint8_t *data, uint16_t length);

// 调试信息打印函数
void motor_debug_print_status(void);
void motor_debug_print_performance(void);
void motor_debug_print_parameters(void);

/*============================ 调试宏定义 ============================*/

#if MOTOR_DEBUG_ENABLE

// 调试信息打印宏
#define MOTOR_DEBUG_PRINTF(fmt, ...)    printf("[MOTOR_DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define MOTOR_INFO_PRINTF(fmt, ...)     printf("[MOTOR_INFO] " fmt "\r\n", ##__VA_ARGS__)
#define MOTOR_WARN_PRINTF(fmt, ...)     printf("[MOTOR_WARN] " fmt "\r\n", ##__VA_ARGS__)
#define MOTOR_ERROR_PRINTF(fmt, ...)    printf("[MOTOR_ERROR] " fmt "\r\n", ##__VA_ARGS__)

// 性能监控宏
#define MOTOR_PERF_START()              motor_debug_performance_start_measure()
#define MOTOR_PERF_END()                motor_debug_performance_end_measure()

// 参数检查宏
#define MOTOR_CHECK_PARAM(param, min, max, name) \
    do { \
        if ((param) < (min) || (param) > (max)) { \
            MOTOR_WARN_PRINTF("Parameter %s out of range: %.3f (valid: %.3f - %.3f)", \
                             (name), (param), (min), (max)); \
            return RET_ERROR; \
        } \
    } while(0)

// 故障检查宏
#define MOTOR_CHECK_FAULT(condition, fault_code, message) \
    do { \
        if (condition) { \
            MOTOR_ERROR_PRINTF("Fault detected: %s (code: 0x%08X)", (message), (fault_code)); \
            motor_debug_trigger_fault(fault_code); \
            return RET_ERROR; \
        } \
    } while(0)

#else

// 调试功能禁用时的空宏
#define MOTOR_DEBUG_PRINTF(fmt, ...)
#define MOTOR_INFO_PRINTF(fmt, ...)
#define MOTOR_WARN_PRINTF(fmt, ...)
#define MOTOR_ERROR_PRINTF(fmt, ...)
#define MOTOR_PERF_START()
#define MOTOR_PERF_END()
#define MOTOR_CHECK_PARAM(param, min, max, name)
#define MOTOR_CHECK_FAULT(condition, fault_code, message)

#endif

/*============================ 故障代码定义 ============================*/

#define MOTOR_FAULT_NONE                0x00000000  // 无故障
#define MOTOR_FAULT_OVERCURRENT         0x00000001  // 过流故障
#define MOTOR_FAULT_OVERVOLTAGE         0x00000002  // 过压故障
#define MOTOR_FAULT_UNDERVOLTAGE        0x00000004  // 欠压故障
#define MOTOR_FAULT_OVERTEMP            0x00000008  // 过温故障
#define MOTOR_FAULT_ENCODER_ERROR       0x00000010  // 编码器故障
#define MOTOR_FAULT_CURRENT_SENSOR      0x00000020  // 电流传感器故障
#define MOTOR_FAULT_VOLTAGE_SENSOR      0x00000040  // 电压传感器故障
#define MOTOR_FAULT_CONTROL_TIMEOUT     0x00000080  // 控制超时故障
#define MOTOR_FAULT_PARAMETER_ERROR     0x00000100  // 参数错误故障
#define MOTOR_FAULT_HARDWARE_ERROR      0x00000200  // 硬件错误故障

/*============================ 内联函数 ============================*/

/**
 * @brief 触发故障
 * @param fault_code 故障代码
 */
static inline void motor_debug_trigger_fault(uint32_t fault_code)
{
    current_loop_t *loop = current_loop_get_instance();
    if (loop != NULL) {
        loop->fault_flag = TRUE;
        // 用户可以在这里添加故障处理逻辑
    }
}

/**
 * @brief 开始性能测量
 */
static inline void motor_debug_performance_start_measure(void)
{
#if MOTOR_PERFORMANCE_MONITOR
    // 用户可以在这里添加性能测量开始代码
    // 例如记录时间戳
#endif
}

/**
 * @brief 结束性能测量
 */
static inline void motor_debug_performance_end_measure(void)
{
#if MOTOR_PERFORMANCE_MONITOR
    // 用户可以在这里添加性能测量结束代码
    // 例如计算执行时间
#endif
}

#endif /* _MOTOR_CONFIG_DEBUG_H */
