/**********************************************************
 * @file     Sys_TimerEvent.c
 * @brief    系统定时器事件驱动实现
 * <AUTHOR>
 * @date     2025-01-20
 * @version  V1.0.0
 *
 * 设计与功能:
 * ----------------------------------------------------------
 * 本模块实现了一个基于500us基准定时的事件驱动系统，支持多任务
 * 定时调度。主要功能包括：
 * 1. 基于500us硬件定时器中断的时间基准
 * 2. 提供1ms、1s、1min多级时间标志
 * 3. 支持动态添加、移除和管理定时任务
 * 4. 提供系统运行时间(毫秒级)计数
 * 技术实现:
 * ----------------------------------------------------------
 * 1. 时间管理: 采用分级计数方式，从500us基准累加生成各级时间标志
 * 2. 任务调度: 基于回调函数的非阻塞式任务调度
 * 3. 内存管理: 动态内存分配任务数组，支持运行时扩展
 * 4. 任务控制: 支持独立任务的使能/禁用与周期调整
 * 使用方法:
 * ----------------------------------------------------------
 * 1. 初始化: TimerEvent_Init(最大任务数)
 * 2. 添加任务: taskId = TimerEvent_AddTask(回调函数, 周期)
 *             周期单位为500us，如设置为2表示1ms执行一次
 * 3. 任务控制: TimerEvent_EnableTask/DisableTask(taskId)
 * 4. 周期调整: TimerEvent_SetTaskPeriod(taskId, 新周期)
 * 5. 移除任务: TimerEvent_RemoveTask(taskId)
 * 6. 在硬件定时器中断(500us)中调用: TimerEvent_Handler()
 * 7. 获取系统时间: Get_Runtime_Ms()
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 任务回调函数应尽量简短，避免阻塞中断处理
 * 2. 初始化时需合理设置最大任务数，避免内存浪费
 * 3. 资源受限环境下可改用静态任务数组替代动态分配
 * 4. 任务ID在0到taskCount-1之间
 **********************************************************/

#include "Sys_TimerEvent.h"
#include <string.h>
#include <stdlib.h>

// 全局变量定义
TimerFlag_t gTimerFlag = {0};
static TimerCount_t sTimerCount = {0};
static TimerTaskManager_t sTaskManager = {0};


volatile uint32_t sys_runtime_ms;

/**
 * @brief 定时器事件初始化
 * @param maxTasks 最大任务数量
 */
void TimerEvent_Init(uint16_t maxTasks)
{
    // 清空标志和计数器 
    memset(&gTimerFlag, 0, sizeof(TimerFlag_t));
    memset(&sTimerCount, 0, sizeof(TimerCount_t));
    
    // 分配任务数组内存
    sTaskManager.tasks = (TimerTask_t*)malloc(sizeof(TimerTask_t) * maxTasks);
    if(sTaskManager.tasks != NULL) {
        memset(sTaskManager.tasks, 0, sizeof(TimerTask_t) * maxTasks);
        sTaskManager.maxTasks = maxTasks;
        sTaskManager.taskCount = 0;
    }
}

/**
 * @brief 定时器事件反初始化
 */
void TimerEvent_DeInit(void)
{
    if(sTaskManager.tasks != NULL) {
        free(sTaskManager.tasks);
        sTaskManager.tasks = NULL;
    }
    sTaskManager.maxTasks = 0;
    sTaskManager.taskCount = 0;
}

/**
 * @brief 添加定时任务
 * @param callback 任务回调函数
 * @param period 任务周期(500us的倍数)
 * @return 任务ID,失败返回0xFFFF
 */
uint16_t TimerEvent_AddTask(TimerCallback_t callback, uint16_t period)
{
    if(callback == NULL || period == 0 || 
       sTaskManager.tasks == NULL || 
       sTaskManager.taskCount >= sTaskManager.maxTasks) {
       return 0xFFFF;
    }
    
    uint16_t taskId = sTaskManager.taskCount++;
    sTaskManager.tasks[taskId].callback = callback;
    sTaskManager.tasks[taskId].period = period;
    sTaskManager.tasks[taskId].counter = 0;
    sTaskManager.tasks[taskId].enable = 1;
    
    return taskId;
}

/**
 * @brief 移除定时任务
 * @param taskId 任务ID
 */
void TimerEvent_RemoveTask(uint16_t taskId)
{
    if(taskId >= sTaskManager.taskCount || sTaskManager.tasks == NULL) {
        return;
    }
    
    // 移动后续任务
    for(uint16_t i = taskId; i < sTaskManager.taskCount - 1; i++) {
        sTaskManager.tasks[i] = sTaskManager.tasks[i + 1];
    }
    sTaskManager.taskCount--;
}

/**
 * @brief 使能定时任务
 * @param taskId 任务ID
 */
void TimerEvent_EnableTask(uint16_t taskId)
{
    if(taskId < sTaskManager.taskCount && sTaskManager.tasks != NULL) {
        sTaskManager.tasks[taskId].enable = 1;
    }
}

/**
 * @brief 禁用定时任务
 * @param taskId 任务ID
 */
void TimerEvent_DisableTask(uint16_t taskId)
{
    if(taskId < sTaskManager.taskCount && sTaskManager.tasks != NULL) {
        sTaskManager.tasks[taskId].enable = 0;
    }
}

/**
 * @brief 设置任务周期
 * @param taskId 任务ID
 * @param period 新的周期值
 */
void TimerEvent_SetTaskPeriod(uint16_t taskId, uint16_t period)
{
    if(taskId < sTaskManager.taskCount && sTaskManager.tasks != NULL && period > 0) {
        sTaskManager.tasks[taskId].period = period;
        sTaskManager.tasks[taskId].counter = 0;
    }
}

/**
 * @brief 定时器中断处理函数(500us调用一次)
 */
void TimerEvent_Handler(void)
{
    // 设置500us标志
    gTimerFlag.flag_500us = 1;
    
    // 1ms计数和标志
    if(++sTimerCount.cnt_1ms >= 2) {
        sys_runtime_ms++;
        sTimerCount.cnt_1ms = 0;
        gTimerFlag.flag_1ms = 1;
        
        // 1s计数和标志
        if(++sTimerCount.cnt_1s >= 1000) {
            sTimerCount.cnt_1s = 0;
            gTimerFlag.flag_1s = 1;
            
            // 1min计数和标志
            if(++sTimerCount.cnt_1min >= 60) {
                sTimerCount.cnt_1min = 0;
                gTimerFlag.flag_1min = 1;
            }
        }
    }
    
    // 任务处理
    if(sTaskManager.tasks != NULL) {
        for(uint16_t i = 0; i < sTaskManager.taskCount; i++) {
            if(sTaskManager.tasks[i].enable) {
                if(++sTaskManager.tasks[i].counter >= sTaskManager.tasks[i].period) {
                    sTaskManager.tasks[i].counter = 0;
                    if(sTaskManager.tasks[i].callback != NULL) {
                        sTaskManager.tasks[i].callback();
                    }
                }
            }
        }
    }
} 

