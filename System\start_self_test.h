/**********************************************************
  * @file     start_self_test.h
  * @brief    系统自检功能头文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  * @note     包含系统自检相关的数据结构定义和函数声明
***********************************************************/


#ifndef _START_SELF_TEST_H
#define _START_SELF_TEST_H

#include "at32a423.h"
#include "Sensor_Drive.h"

/************************** 总体自检控制相关定义 START*********************************/

/* 自检操作类型定义 */
typedef enum {
    SELF_TEST_OP_INIT,      // 初始化自检系统
    SELF_TEST_OP_EXECUTE,   // 执行自检
    SELF_TEST_OP_RESET      // 复位自检状态
} SelfTestOperation_t;

/* 自检状态定义 */
typedef enum {
    SELF_TEST_GROUND = 0,    // 地面自检(无初速度)
    SELF_TEST_AIR = 1        // 空中重启(有初速度)
} SelfTestMode_t;

/* 内部状态机状态定义 */
typedef enum {
    SELF_TEST_STATE_IDLE,          // 空闲状态
    SELF_TEST_STATE_INIT,          // 初始化状态  
    SELF_TEST_STATE_CLOCK,         // 时钟自检
    SELF_TEST_STATE_POWER,         // 电源自检(28V/700V)
    SELF_TEST_STATE_ADC_VOLTAGE,   // ADC电压自检(母线/线电压)
    SELF_TEST_STATE_ADC_TEMP,      // ADC温度自检
    SELF_TEST_STATE_COMM,          // 通信自检(RS422/CAN)
    SELF_TEST_STATE_SIC,           // SiC功率模块自检
    SELF_TEST_STATE_COMPLETE,      // 自检完成
    SELF_TEST_STATE_ERROR          // 自检错误
} SelfTestState_t;

/* 自检项目位定义 */
typedef union {
    struct {
        uint16_t clock       : 1;    // 时钟自检
        uint16_t io          : 1;    // IO自检
        uint16_t speed       : 1;    // 速度自检
        uint16_t bus_voltage : 1;    // 母线电压自检
        uint16_t cap_temp    : 1;    // 电容温度自检
        uint16_t motor_temp  : 1;    // 电机温度自检
        uint16_t coolant_temp: 1;    // 冷却液温度自检
        uint16_t sic_temp    : 1;    // 碳化硅温度自检
        uint16_t v28         : 1;    // 28V供电自检
        uint16_t sic         : 1;    // 碳化硅自检
        uint16_t ext_flash   : 1;    // 外部flash自检
        uint16_t rs422       : 1;    // 422通信自检
        uint16_t can         : 1;    // CAN通信自检
        uint16_t reserved    : 1;    // 保留位
    } bits;
    uint16_t all;                    // 所有位的值
} SelfTestItems_t;

/* 自检输出状态位定义 */
typedef union {
    struct {
        uint8_t pass         : 1;    // 自检通过
        uint8_t warning      : 1;    // 轻度故障告警运行
        uint8_t power_limit  : 1;    // 功率限制模式
        uint8_t fault        : 1;    // 自检故障
        uint8_t reserved     : 4;    // 保留位
    } bits;
    uint8_t all;                     // 所有位的值
} SelfTestOutput_t;

/* 传感器失效标志位定义 */
typedef union {
    struct {
        uint16_t motor_temp1    : 1;    // 电机温度传感器1失效
        uint16_t motor_temp2    : 1;    // 电机温度传感器2失效
        uint16_t cap_temp1      : 1;    // 电容温度传感器1失效
        uint16_t cap_temp2      : 1;    // 电容温度传感器2失效
        uint16_t sic_temp_u     : 1;    // U相SiC温度传感器失效
        uint16_t sic_temp_v     : 1;    // V相SiC温度传感器失效
        uint16_t sic_temp_w     : 1;    // W相SiC温度传感器失效
        uint16_t resolver       : 1;    // 速度传感器失效
        uint16_t bus_voltage    : 1;    // 母线电压传感器失效
        uint16_t uv_voltage     : 1;    // UV线电压传感器失效
        uint16_t uw_voltage     : 1;    // UW线电压传感器失效
        uint16_t v28_voltage    : 1;    // 28V电压传感器失效
        uint16_t reserved       : 4;    // 预留位
    } bits;
    uint16_t all;                       // 所有位的值
} SensorFault_t;

/* 电源状态标志位定义 */
typedef union {
    struct {
        uint8_t bus_power_ok    : 1;    // 母线电供电正常
        uint8_t v28_power_ok    : 1;    // 28V控制电压正常
        uint8_t wait_hv_power   : 1;    // 等待高压上电
        uint8_t soft_start      : 1;    // 缓启动标志
        uint8_t soft_stop       : 1;    // 缓停机标志
        uint8_t reserved        : 3;    // 预留位
    } bits;
    uint8_t all;                        // 所有位的值
} PowerStatus_t;

/* 自检管理结构体 */
typedef struct {
    /* 自检状态 */
    SelfTestMode_t mode;             // 当前自检模式
    SelfTestState_t state;           // 内部状态机状态
    uint8_t initialized;             // 初始化标志
    /* 自检项目状态 */
    SelfTestItems_t items;           // 各项自检结果(0:通过 1:不通过)
    /* 自检输出 */
    SelfTestOutput_t output;         // 自检输出状态
    /* 传感器状态 */
    SensorFault_t sensor_fault;      // 传感器失效标志
    /* 电源状态 */
    PowerStatus_t power_status;      // 电源管理状态
    /* 自检控制函数指针 */
    void (*Init)(void);                     // 初始化函数
    void (*Reset)(void);                    // 复位自检状态
    
} SelfTest_Manager_t;

/* 外部变量声明 */
extern SelfTest_Manager_t gSelfTest_Manager;

/****************************自检管理器定义 END*******************************/

/****************************ADC测试数据定义 START****************************/
/**
 * @brief  ADC测试数据结构体
 * @note   用于自检过程中传递ADC采样值
 */
typedef struct {
    float bus_voltage;       // 母线电压
    float uv_voltage;        // UV线电压
    float uw_voltage;        // UW线电压
    float v28_voltage;       // 28V电压
    float temp_mot1;         // 电机温度1
    float temp_mot2;         // 电机温度2
    float temp_cap1;         // 电容温度1
    float temp_cap2;         // 电容温度2
    float temp_bmf_u;        // U相温度
    float temp_bmf_v;        // V相温度
    float temp_bmf_w;        // W相温度
    float temp_PT100_6;      // 预留PT100温度6
    float temp_PT1000_7;     // 预留PT1000温度7
    float u_current;         // U相电流
    float v_current;         // V相电流
    float w_current;         // W相电流
} ADC_TestValue_t;

/****************************ADC测试数据定义 END******************************/

/****************************自检功能函数声明 START***************************/
/**
 * @brief  自检管理器主函数
 * @param  operation: 操作类型
 * @param  p: 自检管理器指针
 * @param  v: ADC测试数据指针
 * @retval SelfTestResult: 自检结果
 */
SelfTestResult SelfTest_Manager(SelfTestOperation_t operation, 
                               SelfTest_Manager_t* p, 
                               const ADC_TestValue_t* v);

/* 原有函数声明(保持兼容性) */
void SelfTest_Init(void);
void SelfTest_Reset(void);

/************************** 总体自检控制相关定义 END*********************************/

/************************** 时钟自检相关定义 START*********************************/

#define SYSTEM_CLOCK_EXPECTED  150000000  // 预期系统时钟频率 150MHz
#define CLOCK_ERROR_THRESHOLD  1000    // 允许的时钟误差范围 ±1000Hz

/****************************时钟自检定义 START*******************************/
/**
 * @brief  时钟自检函数
 * @note   检查系统时钟是否正常工作
 * @param  None
 * @retval SelfTestResult: 自检结果
 */
SelfTestResult ClockSelfTest(void);

/****************************时钟自检定义 END*********************************/

/************************** ADC自检相关定义 START*********************************/
#define ADC_SAMPLE_COUNT       18      // 每个通道采样次数
#define ADC_DELAY_MS           20      // 每次采样延时(ms)

/************************** AD2S1210自检相关定义 START*********************************/

/* AD2S1210采样参数定义 */
#define AD2S1210_TIMEOUT_MS          100     // 读取超时时间

/* 空中启动速度阈值定义 */
#define AIR_SPEED_THRESHOLD          100  // 空中启动判定阈值(rpm)

/* AD2S1210W采样数据类型定义 */
typedef struct {
    uint16_t max_speed;              // 最大速度值
    float filtered_speed;            // 滤波后的速度值(来自ENC_Speed模块)
    uint8_t sensor_status;           // 传感器状态: 0=正常，非0=故障
} AD2S1210W_Sample_TypeDef;

/* AD2S1210W采样数据结构初始化宏定义 */
#define AD2S1210W_SAMPLE_INIT { \
    .max_speed = 0, \
    .filtered_speed = 0, \
    .sensor_status = 0 \
}

/* AD2S1210W故障位定义 */
typedef union {
    struct {
        uint8_t parity_error    : 1;    // [D0] 配置奇偶校验错误
        uint8_t phase_error     : 1;    // [D1] 相位误差超过锁相范围
        uint8_t tracking_rate   : 1;    // [D2] 速度超过最大跟踪速率
        uint8_t tracking_error  : 1;    // [D3] 跟踪误差超过LOT阈值
        uint8_t dos_mismatch    : 1;    // [D4] 正弦/余弦输入超过DOS失配阈值
        uint8_t dos_overrange   : 1;    // [D5] 正弦/余弦输入超过DOS超量程阈值
        uint8_t los_threshold   : 1;    // [D6] 正弦/余弦输入低于LOS阈值
        uint8_t signal_clip     : 1;    // [D7] 正弦/余弦输入削波
    } bits;
    uint8_t all;                       // 所有位的值
} AD2S1210W_Fault_t;

/* AD2S1210W故障位初始化宏定义 */
#define AD2S1210W_FAULT_INIT { \
    .all = 0 \
}

/* AD2S1210W自检数据结构 */
typedef struct {
    AD2S1210W_Fault_t fault;           // 故障状态
    AD2S1210W_Sample_TypeDef speed_data;         // 速度采样数据
} AD2S1210W_SelfTest_TypeDef;

/* AD2S1210W自检数据结构初始化宏定义 */
#define AD2S1210W_SELFTEST_INIT { \
    .fault = AD2S1210W_FAULT_INIT, \
    .speed_data = AD2S1210W_SAMPLE_INIT \
}

// Ad2s1210自检函数声明
uint8_t Ad2s_ReadFault(AD2S1210W_Fault_t *fault_status);        // 读取故障状态
uint16_t Ad2s_ReadSpeed(uint16_t *speed);                       // 使用ENC_Speed模块获取速度
SelfTestResult AD2S1210W_SelfTest(AD2S1210W_Sample_TypeDef* resolver_data);

/* 外部变量声明 */
extern AD2S1210W_SelfTest_TypeDef gAD2S1210W_Sample;

/************************** AD2S1210自检相关定义 END*********************************/

/************************** 母线电压自检相关定义 START*********************************/

/* 母线电压阈值定义 */
#define BUS_VOLTAGE_SENSOR_FAULT   1150.0f    // 传感器失效电压阈值
#define BUS_VOLTAGE_MAX_NORMAL     780.0f     // 正常工作最高电压
#define BUS_VOLTAGE_MIN_NORMAL     380.0f     // 正常工作最低电压
#define BUS_VOLTAGE_MAX_WARNING    850.0f     // 告警工作最高电压
#define BUS_VOLTAGE_MIN_WARNING    50.0f      // 告警工作最低电压
#define BUS_VOLTAGE_POWEROFF       10.0f      // 断电电压阈值

// 母线电压自检函数声明
SelfTestResult BusVoltageSelfTest(float bus_voltage);

/************************** 母线电压自检相关定义 END*********************************/

/************************** 相电压自检相关定义 START*********************************/


/* 相电压自检阈值定义 */
#define LINE_VOLTAGE_OFFSET_Vcnt    10.0f     // 相电压初始偏置CNT（默认0V对应1956，允许偏置10个计数）
#define LINE_VOLTAGE_THRESHOLD      (ADC_LINE_COEFF * LINE_VOLTAGE_OFFSET_Vcnt)  // 相电压允许偏置阈值


// 相电压自检函数声明
SelfTestResult LineVoltageSelfTest(float uv_voltage, float uw_voltage);

/************************** 相电压自检相关定义 END*********************************/

/************************** 温度自检相关定义 START*********************************/

/* 温度边界定义 */
#define TEMP_SENSOR_MIN        -40.0f    // 温度传感器最小值
#define TEMP_SENSOR_MAX        209.0f    // 温度传感器最大值

/* 电机温度定义 */
#define MOTOR_TEMP_MAX         200.0f    // 电机最高工作温度
#define MOTOR_TEMP_WARNING     180.0f    // 电机告警温度

/* 电容温度定义 */
#define CAP_TEMP_MAX          105.0f    // 电容最高工作温度
#define CAP_TEMP_WARNING      90.0f     // 电容告警温度

/* 碳化硅温度定义 */
#define SIC_TEMP_MAX          125.0f    // 碳化硅最高工作温度
#define SIC_TEMP_WARNING      100.0f     // 碳化硅告警温度

// 温度自检函数声明
SelfTestResult MotorTempSelfTest(float temp_mot1, float temp_mot2);
SelfTestResult CapTempSelfTest(float temp_cap1, float temp_cap2);
SelfTestResult SicTempSelfTest(float temp_u, float temp_v, float temp_w);

/************************** 温度自检相关定义 END*********************************/

/************************** 28V控制电压自检相关定义 START*********************************/

/* 28V控制电压阈值定义 */
#define V28_VOLTAGE_MAX          32.0f    // 最高允许电压
#define V28_VOLTAGE_MIN          19.0f    // 最低允许电压

// 28V控制电压自检函数声明
SelfTestResult V28VoltageSelfTest(float v28_voltage);

/************************** 28V控制电压自检相关定义 END*********************************/

/************************** RS422通信自检相关定义 START*********************************/

/* RS422自检参数定义 */
#define RS422_HANDSHAKE_CODE    0x5A5AA55A    // RS422自检握手代码
#define RS422_TIMEOUT_MS        100           // 每次通信超时时间(ms)
#define RS422_RETRY_COUNT       3             // 重试次数
#define RS422_RETRY_DELAY_MS    100           // 重试间隔时间(ms)

/* RS422自检状态定义 */
typedef struct {
    uint32_t send_code;          // 发送的握手代码
    uint32_t recv_code;          // 接收的握手代码
    uint8_t retry_times;         // 当前重试次数
    uint8_t handshake_ok;        // 握手成功标志
} RS422_SelfTest_TypeDef;

/* RS422自检数据结构初始化宏定义 */
#define RS422_SELFTEST_INIT { \
    .send_code = RS422_HANDSHAKE_CODE, \
    .recv_code = 0, \
    .retry_times = 0, \
    .handshake_ok = 0 \
}
// RS422自检函数声明
SelfTestResult RS422SelfTest(void);

/* 外部变量声明 */
extern RS422_SelfTest_TypeDef gRS422_SelfTest;

/************************** RS422通信自检相关定义 END*********************************/

/************************** CAN通信自检相关定义 START*********************************/

/* CAN自检相关定义 */
#define CAN_SELFTEST_ID1          0x7FE     // CAN1发送的测试ID
#define CAN_SELFTEST_ID2          0x7FF     // CAN2发送的测试ID
#define CAN_SELFTEST_DATA_LEN     8         // 测试数据长度
#define CAN_SELFTEST_TIMEOUT_MS   100       // 超时时间(ms)
#define CAN_SELFTEST_PATTERN      0xA5      // 测试数据模式
#define CAN_SELFTEST_FILTER1      13        // CAN1使用的过滤器编号
#define CAN_SELFTEST_FILTER2      14        // CAN2使用的过滤器编号

/* CAN自检全局变量结构体定义 */
typedef struct {
    uint8_t can1_tx_done;     // CAN1发送完成标志
    uint8_t can2_tx_done;     // CAN2发送完成标志
    uint8_t can1_rx_done;     // CAN1接收完成标志
    uint8_t can2_rx_done;     // CAN2接收完成标志
    uint8_t can1_tx_data[8];  // CAN1发送数据
    uint8_t can2_tx_data[8];  // CAN2发送数据
    uint8_t can1_rx_data[8];  // CAN1接收数据
    uint8_t can2_rx_data[8];  // CAN2接收数据
} CAN_SelfTest_TypeDef;

/* 函数声明 */
SelfTestResult CANSelfTest(void);

/************************** CAN通信自检相关定义 END*********************************/


check_t Check_ADC_Results_For_Ready(const ADC_Result_t* adc_result);


#endif

