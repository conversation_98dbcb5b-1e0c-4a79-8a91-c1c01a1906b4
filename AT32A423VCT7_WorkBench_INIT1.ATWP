<?xml version="1.0" encoding="UTF-8"?>
<Root>
    <WorkBenchVersion>
        <Version>V1.1.09</Version>
    </WorkBenchVersion>
    <MCUInfo>
        <MCUSerials>AT32A423</MCUSerials>
        <MCUName>AT32A423VCT7</MCUName>
        <MCUPackage>LQFP100</MCUPackage>
    </MCUInfo>
    <ACC>
        <Mode>
            <ModeSub name="ACC_State" value="TRUE"/>
        </Mode>
    </ACC>
    <ADC1>
        <Mode>
            <ModeSub name="IN0" value="TRUE"/>
            <ModeSub name="IN1" value="TRUE"/>
            <ModeSub name="IN2" value="TRUE"/>
            <ModeSub name="IN3" value="TRUE"/>
            <ModeSub name="IN4" value="TRUE"/>
            <ModeSub name="IN5" value="TRUE"/>
            <ModeSub name="IN6" value="TRUE"/>
            <ModeSub name="IN7" value="TRUE"/>
            <ModeSub name="IN11" value="TRUE"/>
            <ModeSub name="IN12" value="TRUE"/>
            <ModeSub name="IN13" value="TRUE"/>
            <ModeSub name="IN20" value="TRUE"/>
            <ModeSub name="IN21" value="TRUE"/>
            <ModeSub name="IN22" value="TRUE"/>
            <ModeSub name="IN23" value="TRUE"/>
            <ModeSub name="IN24" value="TRUE"/>
            <ModeSub name="IN25" value="TRUE"/>
            <ModeSub name="IN26" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="ClockPrescaler" value="ADC_HCLK_DIV_6"/>
            <ParametersSub name="Clock" value="25000000"/>
            <ParametersSub name="OrdinaryDMARequestContinuation" value="TRUE"/>
            <ParametersSub name="NbrOfOrdinarySequence" value="15"/>
            <ParametersSub name="Channel_OrdinarySequence_1" value="ADC_CHANNEL_0"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_1" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_2" value="ADC_CHANNEL_1"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_2" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_3" value="ADC_CHANNEL_2"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_3" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_4" value="ADC_CHANNEL_3"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_4" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_5" value="ADC_CHANNEL_4"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_5" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_6" value="ADC_CHANNEL_5"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_6" value="ADC_SAMPLETIME_24_5"/>
            <ParametersSub name="Channel_OrdinarySequence_7" value="ADC_CHANNEL_6"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_7" value="ADC_SAMPLETIME_24_5"/>
            <ParametersSub name="Channel_OrdinarySequence_8" value="ADC_CHANNEL_7"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_8" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_9" value="ADC_CHANNEL_21"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_9" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_10" value="ADC_CHANNEL_22"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_10" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_11" value="ADC_CHANNEL_23"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_11" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_12" value="ADC_CHANNEL_24"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_12" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_13" value="ADC_CHANNEL_25"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_13" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_14" value="ADC_CHANNEL_26"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_14" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_OrdinarySequence_15" value="ADC_CHANNEL_20"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_15" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="EnablePreemptedConversion" value="ENABLE"/>
            <ParametersSub name="PreemptedInterrupt" value="ENABLE"/>
            <ParametersSub name="NbrOfPreemptedSequence" value="3"/>
            <ParametersSub name="Channel_PreemptedSequence_1" value="ADC_CHANNEL_11"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_1" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_PreemptedSequence_2" value="ADC_CHANNEL_12"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_2" value="ADC_SAMPLETIME_12_5"/>
            <ParametersSub name="Channel_PreemptedSequence_3" value="ADC_CHANNEL_13"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_3" value="ADC_SAMPLETIME_12_5"/>
        </Parameters>
    </ADC1>
    <CAN1>
        <Mode>
            <ModeSub name="CAN_Activate" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="BaudRate" value="500"/>
            <ParametersSub name="Sample_Point" value="90.0"/>
            <ParametersSub name="BaudRate_Division" value="15"/>
            <ParametersSub name="BTS1" value="CAN_BTS1_8TQ"/>
            <ParametersSub name="BTS2" value="CAN_BTS2_1TQ"/>
            <ParametersSub name="RSAW" value="CAN_RSAW_1TQ"/>
            <ParametersSub name="Transmit_Sequence_Rule" value="CAN_SENDING_BY_REQUEST"/>
            <ParametersSub name="Filter_0_Identifier_Type" value="CAN_ID_EXTENDED"/>
            <ParametersSub name="Filter_0_Frame_Type" value="DATA_FRAME"/>
            <ParametersSub name="Filter_0_ID1" value="0x02642000"/>
            <ParametersSub name="Filter_0_Mask1" value="0x01FFF000"/>
            <ParametersSub name="Filter_13_Enable" value="TRUE"/>
            <ParametersSub name="Filter_13_FIFO_select" value="CAN_FILTER_FIFO1"/>
            <ParametersSub name="Filter_13_Identifier_Type" value="CAN_ID_EXTENDED"/>
            <ParametersSub name="Filter_13_Mode" value="CAN_FILTER_MODE_ID_LIST"/>
            <ParametersSub name="Filter_13_Frame_Type" value="DATA_FRAME"/>
        </Parameters>
    </CAN1>
    <CAN2>
        <Mode>
            <ModeSub name="CAN_Activate" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="BaudRate" value="500"/>
            <ParametersSub name="Sample_Point" value="90.0"/>
            <ParametersSub name="BaudRate_Division" value="15"/>
            <ParametersSub name="BTS1" value="CAN_BTS1_8TQ"/>
            <ParametersSub name="BTS2" value="CAN_BTS2_1TQ"/>
            <ParametersSub name="RSAW" value="CAN_RSAW_1TQ"/>
            <ParametersSub name="Transmit_Sequence_Rule" value="CAN_SENDING_BY_REQUEST"/>
            <ParametersSub name="Filter_0_Identifier_Type" value="CAN_ID_EXTENDED"/>
            <ParametersSub name="Filter_0_Frame_Type" value="DATA_FRAME"/>
            <ParametersSub name="Filter_0_ID1" value="0x02642000"/>
            <ParametersSub name="Filter_0_Mask1" value="0x01FFF000"/>
            <ParametersSub name="Filter_13_Enable" value="TRUE"/>
            <ParametersSub name="Filter_13_FIFO_select" value="CAN_FILTER_FIFO1"/>
            <ParametersSub name="Filter_13_Identifier_Type" value="CAN_ID_EXTENDED"/>
            <ParametersSub name="Filter_13_Mode" value="CAN_FILTER_MODE_ID_LIST"/>
            <ParametersSub name="Filter_13_Frame_Type" value="DATA_FRAME"/>
        </Parameters>
    </CAN2>
    <CRC>
        <Mode>
            <ModeSub name="CRC_State" value="TRUE"/>
        </Mode>
    </CRC>
    <CRM>
        <Mode>
            <ModeSub name="HEXT" value="HEXT-External-Oscillator"/>
            <ModeSub name="Clock-out" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="HEXT" value="HEXT_CRYSTAL"/>
            <ParametersSub name="Clock-out" value="CLKOUT_ENABLE"/>
        </Parameters>
    </CRM>
    <DEBUG>
        <Mode>
            <ModeSub name="Debug interface" value="SWD"/>
        </Mode>
    </DEBUG>
    <EXINT>
        <Mode>
            <ModeSub name="EXINT8" value="TRUE"/>
        </Mode>
    </EXINT>
    <SPI2>
        <Mode>
            <ModeSub name="Mode" value="Full_Duplex_Master"/>
        </Mode>
        <Parameters>
            <ParametersSub name="TransmissionMode" value="SPI_TRANSMIT_FULL_DUPLEX"/>
            <ParametersSub name="Mode" value="SPI_MODE_MASTER"/>
            <ParametersSub name="FirstBit" value="SPI_FIRST_BIT_MSB"/>
            <ParametersSub name="ClkDivision" value="SPI_MCLK_DIV_4"/>
            <ParametersSub name="ClockFrequency" value="18750000"/>
            <ParametersSub name="CLKPhase" value="SPI_CLOCK_PHASE_2EDGE"/>
        </Parameters>
    </SPI2>
    <SPI3>
        <Mode>
            <ModeSub name="Mode" value="Full_Duplex_Master"/>
        </Mode>
        <Parameters>
            <ParametersSub name="TransmissionMode" value="SPI_TRANSMIT_FULL_DUPLEX"/>
            <ParametersSub name="Mode" value="SPI_MODE_MASTER"/>
            <ParametersSub name="FirstBit" value="SPI_FIRST_BIT_MSB"/>
            <ParametersSub name="ClockFrequency" value="25000000"/>
            <ParametersSub name="CLKPolarity" value="SPI_CLOCK_POLARITY_HIGH"/>
            <ParametersSub name="CLKPhase" value="SPI_CLOCK_PHASE_2EDGE"/>
        </Parameters>
    </SPI3>
    <SYSTEM>
        <Mode>
            <ModeSub name="Time_Base" value="SysTick"/>
        </Mode>
    </SYSTEM>
    <TMR1>
        <Mode>
            <ModeSub name="Channel1 mode" value="Output_CH1_CH1C"/>
            <ModeSub name="Channel2 mode" value="Output_CH2_CH2C"/>
            <ModeSub name="Channel3 mode" value="Output_CH3_CH3C"/>
            <ModeSub name="Channel4 mode" value="Output_without_pin"/>
            <ModeSub name="Clock selection" value="Internal"/>
            <ModeSub name="Activated" value="TRUE"/>
            <ModeSub name="Enable BRK pin" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="CounterDirection" value="TMR_COUNT_TWO_WAY_1"/>
            <ParametersSub name="Period" value="3749"/>
            <ParametersSub name="RepetitionCounter" value="1"/>
            <ParametersSub name="PeriodBufferEnable" value="TRUE"/>
            <ParametersSub name="Primary_TMR_output" value="TMR_PRIMARY_SEL_OVERFLOW"/>
            <ParametersSub name="BreakState" value="TRUE"/>
            <ParametersSub name="BreakInputFilter" value="2"/>
            <ParametersSub name="BreakInputFilter_Frequency" value="18750000"/>
            <ParametersSub name="DeadTime" value="139"/>
            <ParametersSub name="DeadTimeValue" value="1000"/>
            <ParametersSub name="OCMode_1" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC1Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_2" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC2Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_3" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC3Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_4" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="ChannelData_4" value="500"/>
        </Parameters>
    </TMR1>
    <TMR3>
        <Mode>
            <ModeSub name="Multi-Channels" value="Encoder_Mode"/>
            <ModeSub name="Activated" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="Period" value="16383"/>
            <ParametersSub name="Overflow_Event" value="From_C"/>
            <ParametersSub name="EncoderMode" value="TMR_ENCODER_MODE_C"/>
            <ParametersSub name="IC2Filter" value="0x0"/>
        </Parameters>
    </TMR3>
    <TMR13>
        <Mode>
            <ModeSub name="Activated" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="DividerValue" value="149"/>
            <ParametersSub name="Period" value="499"/>
        </Parameters>
    </TMR13>
    <USART1>
        <Mode>
            <ModeSub name="Mode" value="Asynchronous"/>
        </Mode>
        <Parameters>
            <ParametersSub name="BaudRate" value="460800"/>
            <ParametersSub name="RealBaudRate" value="460123"/>
        </Parameters>
    </USART1>
    <USB_OTGFS1>
        <Mode>
            <ModeSub name="Mode" value="Device_Only"/>
        </Mode>
    </USB_OTGFS1>
    <WDT>
        <Mode>
            <ModeSub name="WDT_State" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="Reload" value="0x4FF"/>
        </Parameters>
    </WDT>
    <USB_DEVICE>
        <Mode>
            <ModeSub name="OTGFS1_Device" value="CDC"/>
        </Mode>
        <Parameters>
            <ParametersSub name="DEVICE_FS1_Manufacturer" value="YMS"/>
        </Parameters>
    </USB_DEVICE>
    <DMA>
        <ADC1>
            <ParametersSub name="Instance" value="DMA1_Channel1"/>
            <ParametersSub name="Priority" value="DMA_PRIORITY_VERY_HIGH"/>
            <ParametersSub name="SyncRequestCount" value="1"/>
            <ParametersSub name="RequestCount" value="1"/>
        </ADC1>
    </DMA>
    <NVIC>
        <PriorityGroup>NVIC_PRIORITY_GROUP_2</PriorityGroup>
        <SysTick_Handler>1;3;1;0;0</SysTick_Handler>
        <FLASH_IRQHandler>0;0;0;0;0</FLASH_IRQHandler>
        <CRM_IRQHandler>0;0;0;0;0</CRM_IRQHandler>
        <DMA1_Channel1_IRQHandler>1;2;1;0;0</DMA1_Channel1_IRQHandler>
        <ADC1_IRQHandler>1;0;1;0;0</ADC1_IRQHandler>
        <CAN1_TX_IRQHandler>0;2;1;0;0</CAN1_TX_IRQHandler>
        <CAN1_RX0_IRQHandler>1;2;1;0;0</CAN1_RX0_IRQHandler>
        <CAN1_RX1_IRQHandler>0;2;1;0;0</CAN1_RX1_IRQHandler>
        <CAN1_SE_IRQHandler>0;2;1;0;0</CAN1_SE_IRQHandler>
        <EXINT9_5_IRQHandler>1;1;3;0;0</EXINT9_5_IRQHandler>
        <TMR1_BRK_TMR9_IRQHandler>1;0;0;0;0</TMR1_BRK_TMR9_IRQHandler>
        <TMR1_OVF_TMR10_IRQHandler>0;1;2;0;0</TMR1_OVF_TMR10_IRQHandler>
        <TMR1_TRG_HALL_TMR11_IRQHandler>0;0;0;0;0</TMR1_TRG_HALL_TMR11_IRQHandler>
        <TMR1_CH_IRQHandler>1;0;2;0;0</TMR1_CH_IRQHandler>
        <TMR3_GLOBAL_IRQHandler>0;1;3;0;0</TMR3_GLOBAL_IRQHandler>
        <SPI2_IRQHandler>0;0;0;0;0</SPI2_IRQHandler>
        <USART1_IRQHandler>1;2;2;0;0</USART1_IRQHandler>
        <OTGFS1_WKUP_IRQHandler>0;0;0;0;0</OTGFS1_WKUP_IRQHandler>
        <TMR13_GLOBAL_IRQHandler>1;2;0;0;0</TMR13_GLOBAL_IRQHandler>
        <SPI3_IRQHandler>0;0;0;0;0</SPI3_IRQHandler>
        <CAN2_TX_IRQHandler>0;2;2;0;0</CAN2_TX_IRQHandler>
        <CAN2_RX0_IRQHandler>1;2;2;0;0</CAN2_RX0_IRQHandler>
        <CAN2_RX1_IRQHandler>0;2;2;0;0</CAN2_RX1_IRQHandler>
        <CAN2_SE_IRQHandler>0;0;2;0;0</CAN2_SE_IRQHandler>
        <OTGFS1_IRQHandler>1;2;1;0;0</OTGFS1_IRQHandler>
        <FPU_IRQHandler>0;0;0;0;0</FPU_IRQHandler>
        <DMAMUX_IRQHandler>0;0;0;0;0</DMAMUX_IRQHandler>
        <ACC_IRQHandler>0;0;0;0;0</ACC_IRQHandler>
    </NVIC>
    <GPIO>
        <ShowType>1</ShowType>
        <Signal SignalName="GPIO_Output" PinName="PE2">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="LED_G"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PE3">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="LED_R"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PE4">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="AD2S_SAMPLE"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PE5">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="AD2S_A0"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PE6">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="AD2S_A1"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PC13">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TEST_PC13"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PF9">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="AD2S_CS"/>
        </Signal>
        <Signal SignalName="ADC1_IN11" PinName="PC1">
            <Parameters name="Label" value="ADC_UI_Ia"/>
        </Signal>
        <Signal SignalName="ADC1_IN12" PinName="PC2">
            <Parameters name="Label" value="ADC_VI_Ib"/>
        </Signal>
        <Signal SignalName="ADC1_IN13" PinName="PC3">
            <Parameters name="Label" value="ADC_WI_Ic"/>
        </Signal>
        <Signal SignalName="ADC1_IN0" PinName="PA0">
            <Parameters name="Label" value="ADC_TEM_CAP1"/>
        </Signal>
        <Signal SignalName="ADC1_IN1" PinName="PA1">
            <Parameters name="Label" value="ADC_UV"/>
        </Signal>
        <Signal SignalName="ADC1_IN2" PinName="PA2">
            <Parameters name="Label" value="ADC_UW"/>
        </Signal>
        <Signal SignalName="ADC1_IN3" PinName="PA3">
            <Parameters name="Label" value="ADC_TEM_OLL"/>
        </Signal>
        <Signal SignalName="ADC1_IN4" PinName="PA4">
            <Parameters name="Label" value="ADC_TEM_CAP2"/>
        </Signal>
        <Signal SignalName="ADC1_IN5" PinName="PA5">
            <Parameters name="Label" value="ADC_TEM_MOT1"/>
        </Signal>
        <Signal SignalName="ADC1_IN6" PinName="PA6">
            <Parameters name="Label" value="ADC_TEM_MOT2"/>
        </Signal>
        <Signal SignalName="ADC1_IN7" PinName="PA7">
            <Parameters name="Label" value="ADC_700V"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PB0">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="Label" value="IO_IN1"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PB1">
            <Parameters name="Label" value="IO_IN2"/>
        </Signal>
        <Signal SignalName="ADC1_IN20" PinName="PB2">
            <Parameters name="Label" value="ADC_VCC28V2"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PE7">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="CON_RST"/>
        </Signal>
        <Signal SignalName="TMR1_CH1C" PinName="PE8">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_U_CH1C"/>
        </Signal>
        <Signal SignalName="TMR1_CH1" PinName="PE9">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_U_CH1"/>
        </Signal>
        <Signal SignalName="TMR1_CH2C" PinName="PE10">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_V_CH2C"/>
        </Signal>
        <Signal SignalName="TMR1_CH2" PinName="PE11">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_V_CH2"/>
        </Signal>
        <Signal SignalName="TMR1_CH3C" PinName="PE12">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_W_CH3C"/>
        </Signal>
        <Signal SignalName="TMR1_CH3" PinName="PE13">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_DOWN"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR1_W_CH3"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PE14">
            <Parameters name="Label" value="Fail_RDY_MCU"/>
        </Signal>
        <Signal SignalName="TMR1_BRK" PinName="PE15">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
        </Signal>
        <Signal SignalName="ADC1_IN21" PinName="PB10">
            <Parameters name="Label" value="ADC_NTC_Temp6"/>
        </Signal>
        <Signal SignalName="ADC1_IN22" PinName="PB11">
            <Parameters name="Label" value="ADC_NTC_Temp7"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PF8">
            <Parameters name="Label" value="Fail_FLT_MCU"/>
        </Signal>
        <Signal SignalName="ADC1_IN23" PinName="PB12">
            <Parameters name="Label" value="ADC_Temp_BMF_U"/>
        </Signal>
        <Signal SignalName="ADC1_IN24" PinName="PB13">
            <Parameters name="Label" value="ADC_Temp_BMF_V"/>
        </Signal>
        <Signal SignalName="ADC1_IN25" PinName="PB14">
            <Parameters name="Label" value="ADC_Temp_BMF_W"/>
        </Signal>
        <Signal SignalName="ADC1_IN26" PinName="PB15">
            <Parameters name="Label" value="ADC_VCC28V"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD8">
            <Parameters name="Label" value="OVER_WI_CUR1"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD9">
            <Parameters name="Label" value="OVER_WI_CUR0"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD10">
            <Parameters name="Label" value="OVER_VOL_BUS"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD11">
            <Parameters name="Label" value="OVER_VOL_UV1"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD12">
            <Parameters name="Label" value="OVER_VOL_UV0"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD13">
            <Parameters name="Label" value="OVER_VI_CUR1"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD14">
            <Parameters name="Label" value="OVER_VI_CUR0"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD15">
            <Parameters name="Label" value="OVER_UI_CUR1"/>
        </Signal>
        <Signal SignalName="TMR3_CH1" PinName="PC6">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR_encoder_A"/>
        </Signal>
        <Signal SignalName="TMR3_CH2" PinName="PC7">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="TMR_encoder_B"/>
        </Signal>
        <Signal SignalName="EXINT8" PinName="PC8">
            <Parameters name="Label" value="EXTI_encoder_Z"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PA8">
            <Parameters name="Label" value="OVER_UI_CUR0"/>
        </Signal>
        <Signal SignalName="USART1_TX" PinName="PA9">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
        </Signal>
        <Signal SignalName="USART1_RX" PinName="PA10">
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
        </Signal>
        <Signal SignalName="DEBUG_JTMS_SWDIO" PinName="PA13">
            <Parameters name="Label" value="SWDIO"/>
        </Signal>
        <Signal SignalName="DEBUG_JTCK_SWCLK" PinName="PA14">
            <Parameters name="Label" value="SWCLK"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PA15">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="SPI3_CS"/>
        </Signal>
        <Signal SignalName="SPI3_SCK" PinName="PC10">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
        </Signal>
        <Signal SignalName="SPI3_MISO" PinName="PC11">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
        </Signal>
        <Signal SignalName="SPI3_MOSI" PinName="PC12">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PD0">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_PullType_Set" value="GPIO_PULL_UP"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="SPI2_CS"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD6">
            <Parameters name="Label" value="OVER_VOL_UW0"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PD7">
            <Parameters name="Label" value="OVER_VOL_UW1"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PB3">
            <Parameters name="Label" value="AT_LOT"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PB4">
            <Parameters name="Label" value="AT_DOS"/>
        </Signal>
    </GPIO>
    <ClockConfiguration>
        <rtcsel>0</rtcsel>
        <rtcdiv>2</rtcdiv>
        <hext>8.000000</hext>
        <sclkhextdiv>1</sclkhextdiv>
        <sclkhickdiv>1</sclkhickdiv>
        <pllrcs>0</pllrcs>
        <pllms>1</pllms>
        <pllns>75</pllns>
        <pllfr>2</pllfr>
        <sclkselect>1</sclkselect>
        <ahbdiv>1</ahbdiv>
        <apb1div>2</apb1div>
        <apb2div>1</apb2div>
        <usbdiv>6.0</usbdiv>
        <hicktousb>1</hicktousb>
        <hicktosclk>0</hicktosclk>
        <clkout>5</clkout>
        <clkoutdiv1>1</clkoutdiv1>
        <clkoutdiv2>1</clkoutdiv2>
        <clkoutpinselect>4</clkoutpinselect>
        <usart1clocksel>0</usart1clocksel>
        <usart2clocksel>0</usart2clocksel>
        <usart3clocksel>0</usart3clocksel>
        <i2c1clocksel>0</i2c1clocksel>
        <adcclocksel>0</adcclocksel>
        <systicsel>8</systicsel>
    </ClockConfiguration>
    <PINInfo>
        <PinSub pinname="PE2" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PE3" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PE4" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PE5" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PE6" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PC13" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PF9" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PF0" signalname="CRM_HEXT_IN" signaltype="2"/>
        <PinSub pinname="PF1" signalname="CRM_HEXT_OUT" signaltype="2"/>
        <PinSub pinname="PC1" signalname="ADC1_IN11" signaltype="3"/>
        <PinSub pinname="PC2" signalname="ADC1_IN12" signaltype="3"/>
        <PinSub pinname="PC3" signalname="ADC1_IN13" signaltype="3"/>
        <PinSub pinname="PA0" signalname="ADC1_IN0" signaltype="3"/>
        <PinSub pinname="PA1" signalname="ADC1_IN1" signaltype="3"/>
        <PinSub pinname="PA2" signalname="ADC1_IN2" signaltype="3"/>
        <PinSub pinname="PA3" signalname="ADC1_IN3" signaltype="3"/>
        <PinSub pinname="PA4" signalname="ADC1_IN4" signaltype="3"/>
        <PinSub pinname="PA5" signalname="ADC1_IN5" signaltype="3"/>
        <PinSub pinname="PA6" signalname="ADC1_IN6" signaltype="3"/>
        <PinSub pinname="PA7" signalname="ADC1_IN7" signaltype="3"/>
        <PinSub pinname="PB0" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB1" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB2" signalname="ADC1_IN20" signaltype="3"/>
        <PinSub pinname="PE7" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PE8" signalname="TMR1_CH1C" signaltype="2"/>
        <PinSub pinname="PE9" signalname="TMR1_CH1" signaltype="2"/>
        <PinSub pinname="PE10" signalname="TMR1_CH2C" signaltype="2"/>
        <PinSub pinname="PE11" signalname="TMR1_CH2" signaltype="2"/>
        <PinSub pinname="PE12" signalname="TMR1_CH3C" signaltype="2"/>
        <PinSub pinname="PE13" signalname="TMR1_CH3" signaltype="2"/>
        <PinSub pinname="PE14" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PE15" signalname="TMR1_BRK" signaltype="3"/>
        <PinSub pinname="PB10" signalname="ADC1_IN21" signaltype="3"/>
        <PinSub pinname="PB11" signalname="ADC1_IN22" signaltype="3"/>
        <PinSub pinname="PF8" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB12" signalname="ADC1_IN23" signaltype="3"/>
        <PinSub pinname="PB13" signalname="ADC1_IN24" signaltype="3"/>
        <PinSub pinname="PB14" signalname="ADC1_IN25" signaltype="3"/>
        <PinSub pinname="PB15" signalname="ADC1_IN26" signaltype="3"/>
        <PinSub pinname="PD8" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD9" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD10" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD11" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD12" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD13" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD14" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD15" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PC6" signalname="TMR3_CH1" signaltype="3"/>
        <PinSub pinname="PC7" signalname="TMR3_CH2" signaltype="3"/>
        <PinSub pinname="PC8" signalname="EXINT8" signaltype="3"/>
        <PinSub pinname="PC9" signalname="CRM_CLKOUT" signaltype="2"/>
        <PinSub pinname="PA8" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PA9" signalname="USART1_TX" signaltype="2"/>
        <PinSub pinname="PA10" signalname="USART1_RX" signaltype="2"/>
        <PinSub pinname="PA11" signalname="USB_OTGFS1_D-" signaltype="2"/>
        <PinSub pinname="PA12" signalname="USB_OTGFS1_D+" signaltype="2"/>
        <PinSub pinname="PA13" signalname="DEBUG_JTMS_SWDIO" signaltype="2"/>
        <PinSub pinname="PA14" signalname="DEBUG_JTCK_SWCLK" signaltype="2"/>
        <PinSub pinname="PA15" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PC10" signalname="SPI3_SCK" signaltype="3"/>
        <PinSub pinname="PC11" signalname="SPI3_MISO" signaltype="3"/>
        <PinSub pinname="PC12" signalname="SPI3_MOSI" signaltype="3"/>
        <PinSub pinname="PD0" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PD1" signalname="SPI2_SCK" signaltype="2"/>
        <PinSub pinname="PD3" signalname="SPI2_MISO" signaltype="2"/>
        <PinSub pinname="PD4" signalname="SPI2_MOSI" signaltype="2"/>
        <PinSub pinname="PD6" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD7" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB3" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB4" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PB5" signalname="CAN2_RX" signaltype="2"/>
        <PinSub pinname="PB6" signalname="CAN2_TX" signaltype="2"/>
        <PinSub pinname="PB8" signalname="CAN1_RX" signaltype="3"/>
        <PinSub pinname="PB9" signalname="CAN1_TX" signaltype="3"/>
    </PINInfo>
    <ProjectInfomation>
        <ProjectName>AT32A423VCT7_WorkBench_INIT1</ProjectName>
        <ProjectLocation>D:/AT32_Work/Gitee/CDM_602_AT32A423_PMSM</ProjectLocation>
        <ToolchainIDE>MDK_V5</ToolchainIDE>
        <ARMCompiler>1</ARMCompiler>
        <KeepUserCode>false</KeepUserCode>
        <NotUsedPinAnalog>false</NotUsedPinAnalog>
        <CodeSplitIP>false</CodeSplitIP>
        <AddNecessaryFileFlag>true</AddNecessaryFileFlag>
        <MinHeapSize>0x400</MinHeapSize>
        <MinStackSize>0x400</MinStackSize>
        <UseFirmware>true</UseFirmware>
        <PackageVersion>V2.0.2</PackageVersion>
    </ProjectInfomation>
</Root>
