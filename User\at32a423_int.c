/* includes ------------------------------------------------------------------*/
#include "at32a423_int.h"
#include "usb_app.h"
#include "main.h"
#include "Sys_TimerEvent.h"
#include "Sensor_Drive.h"
#include "Sys_Can.h"
#include "HWInterface.h"
#include "MotorData.h"
#include "Sys_Protect.h"
#include "SysCtl_AllHeaders.h"

/**
  * @brief  处理NMI异常
  * @param  无
  * @retval 无
  */
void NMI_Handler(void)
{
	
}

/**
  * @brief  处理硬件故障异常
  * @param  无
  * @retval 无
  */
void HardFault_Handler(void)
{
  /* 当发生硬件故障异常时进入死循环 */
  while (1)
  {
  }
} 

/**
  * @brief  处理内存管理异常
  * @param  无
  * @retval 无
  */
void MemManage_Handler(void)
{
  /* 当发生内存管理异常时进入死循环 */
  while (1)
  {
  }
}

/**
  * @brief  处理总线故障异常
  * @param  无
  * @retval 无
  */
void BusFault_Handler(void)
{
  /* 当发生总线故障异常时进入死循环 */
  while (1)
  {
  }
}

/**
  * @brief  处理用法故障异常
  * @param  无
  * @retval 无
  */
void UsageFault_Handler(void)
{
  /* 当发生用法故障异常时进入死循环 */
  while (1)
  {
  }
}

/**
  * @brief  处理SVC调用异常
  * @param  无
  * @retval 无
  */
void SVC_Handler(void)
{
	
}

/**
  * @brief  处理调试监视器异常
  * @param  无
  * @retval 无
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  处理PendSV异常
  * @param  无
  * @retval 无
  */
void PendSV_Handler(void)
{

}

/**
  * @brief  处理系统滴答定时器中断
  * @param  无
  * @retval 无
  */
void SysTick_Handler(void)
{
    
}

/**
  * @brief  处理DMA1通道1中断
  * @param  无
  * @retval 无
  */
void DMA1_Channel1_IRQHandler(void)
{
  if(dma_interrupt_flag_get(DMA1_FDT1_FLAG))
  {
    ADC_DMA_Complete_Callback();
    ADC_Process_500us();

    if(adc_flag_get(ADC1, ADC_OCCE_FLAG))
    {
      adc_flag_clear(ADC1, ADC_OCCE_FLAG);  // 清除ADC普通通道转换完成标志位
    }
    dma_flag_clear(DMA1_FDT1_FLAG);  // 清除DMA1通道1传输完成标志位
  }
  if(dma_interrupt_flag_get(DMA1_DTERR1_FLAG))
  {
    dma_flag_clear(DMA1_DTERR1_FLAG);  // 清除DMA1通道1错误标志位
  }
}

/**
  * @brief  处理ADC1中断
  * @param  无
  * @retval 无
  */
__WEAK void ADC1_IRQHandler(void)
{
  
}

/**
  * @brief  处理外部中断线[9:5]中断
  * @param  无
  * @retval 无
  */
void EXINT9_5_IRQHandler(void)
{
  if(exint_flag_get(EXINT_LINE_8) != RESET)
  {
    exint_flag_clear(EXINT_LINE_8);  // 清除外部中断线8标志位
  }
}

/**
  * @brief  处理定时器1刹车和定时器9中断
  * @param  无
  * @retval 无
  */
void TMR1_BRK_TMR9_IRQHandler(void)
{
  if(tmr_interrupt_flag_get(TMR1,TMR_BRK_FLAG))
  {
    tmr_flag_clear(TMR1, TMR_BRK_FLAG);  // 清除刹车标志位
  }
}

/**
  * @brief  处理定时器1溢出和定时器10中断
  * @param  无
  * @retval 无
  */
void TMR1_OVF_TMR10_IRQHandler(void)
{
  if (tmr_interrupt_flag_get(TMR1,TMR_OVF_FLAG))
  {
    tmr_flag_clear(TMR1, TMR_OVF_FLAG);  // 清除溢出标志位
  }
}

/**
  * @brief  处理定时器3中断
  * @param  无
  * @retval 无
  */
void TMR3_GLOBAL_IRQHandler(void)
{
  if(tmr_interrupt_flag_get(TMR3,TMR_OVF_FLAG))
  {
    tmr_flag_clear(TMR3, TMR_OVF_FLAG);  // 清除溢出标志位
  }
}

// /**
//   * @brief  处理USART1中断---->RS422_CU.c
//   * @param  无
//   * @retval 无
//   */
// void USART1_IRQHandler(void)
// {
//   if(usart_interrupt_flag_get(USART1, USART_RDBF_FLAG))
//   {
//     usart_flag_clear(USART1, USART_RDBF_FLAG);  // 清除接收标志位
//   }
// }

/**
  * @brief  处理定时器13中断
  * @param  无
  * @retval 无
  */
void TMR13_GLOBAL_IRQHandler(void)
{
  if(tmr_interrupt_flag_get(TMR13,TMR_OVF_FLAG))
  {
    tmr_flag_clear(TMR13, TMR_OVF_FLAG);  // 清除溢出标志位
    if (ADC_Get_Enable_Flag())
    {
      adc_ordinary_software_trigger_enable(ADC1,TRUE);
    }
    TimerEvent_Handler();
    wdt_counter_reload();       // 重载看门狗
  }
}

/**
  * @brief  处理OTGFS1中断
  * @param  无
  * @retval 无
  */
void OTGFS1_IRQHandler(void)
{
  wk_otgfs1_irq_handler();
}

/**
  * @brief  处理CAN1接收中断
  * @param  无
  * @retval 无
  */
void CAN1_RX0_IRQHandler(void)
{
    if(can_interrupt_flag_get(CAN1, CAN_RF0MN_FLAG) != RESET)
    {
        can_flag_clear(CAN1, CAN_RF0MN_FLAG);
        CAN1_RX0_IRQ_Callback();
        
    }
}

/**
  * @brief  处理CAN2接收中断
  * @param  无
  * @retval 无
  */
void CAN2_RX0_IRQHandler(void)
{
    if(can_interrupt_flag_get(CAN2, CAN_RF0MN_FLAG) != RESET)
    {
        CAN2_RX0_IRQ_Callback();
        can_flag_clear(CAN2, CAN_RF0MN_FLAG);
    }
}


uint16_t test_cnt = 0;

void TMR1_CH_IRQHandler(void)
{
	test_cnt++;
	if(tmr_interrupt_flag_get(TMR1,TMR_C4_FLAG)!=RESET)
	{
    
    adc_preempt_software_trigger_enable(ADC1, TRUE);   // 软件触发ADC1抢占通道采样
    tmr_flag_clear(TMR1, TMR_C4_FLAG);                 // 清TMR1 通道4中断标志
	}
}

