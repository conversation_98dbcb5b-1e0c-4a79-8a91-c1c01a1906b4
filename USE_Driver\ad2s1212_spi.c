#include "ad2s1212_spi.h"

/**
 * @brief 配置AD2S1210的工作模式
 * @param ModeConfig 模式配置参数
 *        - Mode_POSIT: 位置读取模式 (A0=0, A1=0)
 *        - Mode_SPEED: 速度读取模式 (A0=0, A1=1)
 *        - Mode_COFIG: 寄存器配置模式 (A0=1, A1=1)
 * @note 通过设置A0和A1引脚电平来配置工作模式，并执行采样操作
 */
void AD2S2S1210_ModeCfg(uint8_t ModeConfig)
{
  //A0,A1配置
  switch(ModeConfig)
  {
    case Mode_POSIT:
            A0_L;A1_L;    // 位置读取模式 (A0=0, A1=0)
            break;
    case Mode_SPEED:
            A0_L;A1_H;    // 速度读取模式 (A0=0, A1=1)
            break;
    case Mode_COFIG:
            A0_H;A1_H;    // 寄存器配置模式 (A0=1, A1=1)
            break;		
    default:
            A0_L;A1_L;    // 默认为配置模式
            break;	
  }
  AD2S1210_Delay(4);
  SMAPLE_H;
  AD2S1210_Delay(1);
  SMAPLE_L;
  AD2S1210_Delay(1);
}

/**
 * @brief 初始化AD2S1210旋变数字转换器
 * @note 配置分辨率为12位，设置各种阈值参数，并初始化为位置读取模式
 *       包括：控制寄存器、激励频率、DOS阈值、LOS阈值、LOT阈值等配置
 */
void AD2S1210_Init()
{
  uint8_t readValue = 0;
  //电阻配置
  RES0_L;RES1_H;  //配置为12位
  A0_H;A1_H;      //配置模式
  
  PCS_L;
  SMAPLE_L;
  NWR_H;
  AD2S1210_Delay(0x0F);
  
  // 根据寄存器映射表更新寄存器配置
  AD2S1210_WRITE(CONTROL, 0x7A);         // 控制寄存器配置，设置分辨率为12位
  readValue = AD2S1210_READ(CONTROL);    // 读取控制寄存器验证写入

  AD2S1210_WRITE(EXFREQUENCY, 0x28);     // 设置激励频率 40*250Hz = 10KHz
  readValue = AD2S1210_READ(EXFREQUENCY); // 读取激励频率寄存器验证写入

  AD2S1210_WRITE(DOSRSTMXTHRES, 0x7E);   // DOS复位最大阈值
  readValue = AD2S1210_READ(DOSRSTMXTHRES); // 读取验证写入

  AD2S1210_WRITE(DOSRSTMITHRES, 0x02);   // DOS复位最小阈值
  readValue = AD2S1210_READ(DOSRSTMITHRES); // 读取验证写入

  AD2S1210_WRITE(LOSTHRES, 0x01);        // LOS门限值
  readValue = AD2S1210_READ(LOSTHRES);   // 读取验证写入

  AD2S1210_WRITE(DOSORTHRES, 0x7F);      // DOS超量程阈值
  readValue = AD2S1210_READ(DOSORTHRES); // 读取验证写入

  AD2S1210_WRITE(DOSMISTHRES, 0x7F);     // DOS失配阈值
  readValue = AD2S1210_READ(DOSMISTHRES); // 读取验证写入

  AD2S1210_WRITE(LOTHITHRES, 0x7F);      // LOT上限
  readValue = AD2S1210_READ(LOTHITHRES); // 读取验证写入

  AD2S1210_WRITE(LOTLOTHRES, 0x01);      // LOT下限
  readValue = AD2S1210_READ(LOTLOTHRES); // 读取验证写入

  AD2S1210_Delay(0xFFF);
  
  SMAPLE_H;
  SMAPLE_L;
  AD2S1210_Delay(0x1FF);
  AD2S1210_REFAULT();                    // 复位错误标志
  AD2S1210_Delay(0xFF);
  AD2S2S1210_ModeCfg(Mode_POSIT);        // 切换到普通位置读取模式  
  PCS_H;
}
 
/**
 * @brief AD2S1210硬件复位函数
 * @note 通过SAMPLE信号的高低电平切换实现硬件复位
 *       当前RESET引脚操作被注释掉，仅通过延时实现复位过程
 */
void AD2S1210_RESET(void)
{
  SMAPLE_H;
  SMAPLE_L;
  //RESET_L;
  AD2S1210_Delay(0x5FF);
  //RESET_H;
  AD2S1210_Delay(0xFF);
}
 
/**
 * @brief AD2S1210错误标志复位函数
 * @note 通过读取故障寄存器清除故障标志
 *       执行时间约为3us
 *       切换到配置模式，执行SAMPLE信号采样，读取FAULT寄存器
 */
void AD2S1210_REFAULT()
{
  AD2S2S1210_ModeCfg(Mode_COFIG);        // 切换到配置模式
  SMAPLE_H;
  AD2S1210_Delay(0x1);
  SMAPLE_L;
  AD2S1210_Delay(0x1);
  AD2S1210_READ(FAULT);                  // 读取故障寄存器清除故障
  SMAPLE_H;
  AD2S1210_Delay(0x1);
  SMAPLE_L;
  AD2S1210_Delay(6);
}
 
/**
 * @brief 延时函数
 * @param nCount 延时计数值
 * @note nCount=1时执行时间为183ns，步长22ns
 *       通过简单循环实现延时
 */
void AD2S1210_Delay(uint32_t nCount)     // nCount=1时执行时间为183ns步长22ns
{ 
  for(; nCount != 0; nCount--); 
} 
 
/**
 * @brief 向AD2S1210寄存器写入数据
 * @param addr 寄存器地址
 * @param data 要写入的数据
 * @note 通过SPI接口向指定寄存器写入数据
 *       先发送寄存器地址，再发送数据
 */
void AD2S1210_WRITE(uint8_t addr, uint8_t data)
{
  uint8_t temp = addr;
  uint8_t rData = 0;
  PCS_L;
  NWR_L;
  NOP1;
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器地址
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NWR_H;
  NOP38;
  NWR_L;
  NOP1;
  temp = data;
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器数据
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NOP1;
  NWR_H;
  PCS_H;
  NOP38;
  
}
 
/**
 * @brief 读取AD2S1210寄存器数据
 * @param addr 要读取的寄存器地址
 * @return 读取到的寄存器数据
 * @note 先切换到配置模式，通过SPI接口读取指定寄存器的数据
 *       读取完成后切换回位置读取模式
 */
uint8_t AD2S1210_READ(uint8_t addr)
{
  AD2S2S1210_ModeCfg(Mode_COFIG);
  uint8_t temp = addr;
  uint8_t buff = 0;
  uint8_t rData = 0;
  PCS_L;
  NWR_L;
  AD2S1210_Delay(0x01);
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器地址
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NWR_H;
  AD2S1210_Delay(0x02);
  
  buff = 0xFF;                          // 发送空数据读取寄存器内容
  NWR_L;
  spi_i2s_data_transmit(SPI2, buff);
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);   // 读取寄存器内容
  NWR_H;
  AD2S1210_Delay(0x2);
  PCS_H;

  return rData;
}
 
// /**
//  * @brief 读取AD2S1210位置或速度数据
//  * @return 读取到的12位数据结果
//  * @note 根据当前配置的模式读取位置或速度数据
//  *       严格按照时序要求执行，使用NOP指令精确控制时序
//  *       读取MSB和LSB两个字节，合成12位结果
//  */
// uint16_t AD2S1210_CommRead(void)
// {
//     uint16_t result = 0;
//     uint8_t msb = 0, lsb = 0;
    
//     SMAPLE_L;
//     INSERT_NOP(38); // t16: SAMPLE脉冲宽度
    
//     PCS_L;
//     INSERT_NOP(2);  // t6: CS上升到下降
    
//     NWR_L;
//     INSERT_NOP(1);  // t31: CS下降到WR/FSYNC下降
//     INSERT_NOP(3);  // t23: WR/FSYNC下降到SDO解除高阻
    
//     SPI2->dt = 0xFF;
//     while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
//     msb = SPI2->dt;
    
//     NWR_H;
//     INSERT_NOP(2);  // t34: WR/FSYNC上升到下降
    
//     NWR_L;
//     INSERT_NOP(3);  // t23: WR/FSYNC下降到SDO解除高阻
    
//     SPI2->dt = 0xFF;
//     while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
//     lsb = SPI2->dt;
    
//     NWR_H;
//     SMAPLE_H;
//     PCS_H;
    
//     result = ((uint16_t)msb << 4) | (lsb >> 4);
//     return result;
// }

/**
 * @brief 连续读取AD2S1210位置
 * @return 读取到的12位数据结果
 * @note 与普通读取不同，此函数在读取MSB和LSB之间不拉高NWR信号
 *       实现连续读取两个字节，提高读取效率
 */
uint16_t AD2S1210_CommRead(void)
{
    uint16_t result = 0;
    uint8_t msb = 0, lsb = 0;
    uint32_t timeout = 0;

    SMAPLE_L;
    INSERT_NOP(4);  // T16_NOP值

    PCS_L;
    NWR_L;
    // T31_NOP值为0，不需要延时 (<5ns)
    INSERT_NOP(1);   // T23_NOP值

    // 连续读取两个字节，中间不拉高NWR
    SPI2->dt = 0xFF;
    timeout = 0;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG)) {
        timeout++;
        if (timeout >= 1000) {
            // 超时处理：复位控制线并返回错误值
            SMAPLE_H;
            NWR_H;
            PCS_H;
            return 0xFFFF; // 错误值
        }
    }
    msb = SPI2->dt;

    // 直接继续保持NWR_L
    // INSERT_NOP(1);  // 额外延迟

    SPI2->dt = 0xFF;
    timeout = 0;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG)) {
        timeout++;
        if (timeout >= 1000) {
            // 超时处理：复位控制线并返回错误值
            SMAPLE_H;
            NWR_H;
            PCS_H;
            return 0xFFFF; // 错误值
        }
    }
    lsb = SPI2->dt;

    SMAPLE_H;
    NWR_H;
    PCS_H;

    result = ((uint16_t)msb << 4) | (lsb >> 4);
    return result;
}



/**
 * @brief 读取AD2S1210角速度数据
 * @return 读取到的12位角速度数据(二进制补码格式)
 * @note 该函数会临时切换到速度读取模式，读取完成后恢复到位置读取模式
 *       速度数据格式为二进制补码，MSB代表旋转方向
 */
uint16_t AD2S1210_ReadVelocity(void)
{
    uint16_t result = 0;
    uint8_t msb = 0, lsb = 0;
    
    // 使用SAMPLE更新寄存器数据
    INSERT_NOP(1);  // 短暂延迟
    SMAPLE_L;
    INSERT_NOP(4); // T16_NOP值
    // 切换到速度读取模式 (A0=0, A1=1)
    A0_L;
    A1_H;
    // 片选
    PCS_L;
    INSERT_NOP(1);  // T6_NOP值
    
    // 读取MSB
    NWR_L;
    // T31_NOP值为0，不需要延时 (<5ns)
    INSERT_NOP(1);  // T23_NOP值
    
    SPI2->dt = 0xFF;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
    msb = SPI2->dt;
    
    SPI2->dt = 0xFF;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
    lsb = SPI2->dt;
    
    NWR_H;
    SMAPLE_H;
    PCS_H;
    
    // 恢复到位置读取模式 (A0=0, A1=0)
    A0_L;
    A1_L;
    
    // 合成12位结果
    result = ((uint16_t)msb << 8) | lsb;
    return result;
}

/**
 * @brief 获取转子旋转速度，支持转/分(RPM)或转/秒(RPS)
 * @param unit 速度单位：0=转/秒(RPS), 1=转/分(RPM)
 * @return 转换后的旋转速度，带符号值(正值表示正向旋转，负值表示反向旋转)
 * @note 对于12位分辨率、8.192MHz时钟:
 *       - 最大跟踪速率: ±1000 rps
 *       - 有效数据位: D15至D4 (忽略D3至D0)
 *       - 当速度为+1000 rps时，原始值为0x7FF0
 *       - 当速度为-1000 rps时，原始值为0x8000
 */
float AD2S1210_GetRotationSpeed(uint8_t unit)
{
    int16_t raw_velocity;
    int16_t velocity_12bit;
    float rotation_speed;
    
    // 读取原始速度数据，转换为有符号整型保留方向信息
    raw_velocity = (int16_t)AD2S1210_ReadVelocity();
    
    // 右移4位忽略低4位无效数据，得到12位有效值
    velocity_12bit = raw_velocity >> 4;
    
    // 对于12位二进制补码:
    // - 最大正值为0x7FF (+2047)，表示+1000 rps
    // - 最小负值为0x800 (-2048)，表示-1000 rps
    const float LSB_VALUE = 1000.0f / 2047.0f; // 约0.488 rps
    
    // 计算旋转速度(rps)
    rotation_speed = (float)velocity_12bit * LSB_VALUE;
    
    // 如果需要转/分(RPM)，乘以60
    if(unit == 1) {
        rotation_speed *= 60.0f;
    }
    
    return rotation_speed;
}