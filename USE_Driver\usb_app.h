#ifndef __USB_APP_H
#define __USB_APP_H

#ifdef __cplusplus
extern "C" {
#endif

#include "usbd_core.h"
#include "usbd_int.h"

/************************************* USB发送函数 *************************************/
#define USB_BUFFER_SIZE 672    // 缓冲区大小
#define USB_BUFFER_RX   400    // 接收缓冲区大小

// 接收缓冲区状态结构体 - 极简版本
typedef struct rx_buffer_state {
    uint8_t buffer[USB_BUFFER_RX];  // 接收缓冲区
} rx_buffer_state_t;

// 声明USB接收状态结构体
extern rx_buffer_state_t usb_rx_state;

// 函数声明
void usb_drive_init(void);
void usb_send_data(uint8_t *buf, uint16_t len);
void usb_delay_ms(uint32_t ms);

extern otg_core_type otg_core_struct_fs1;
/************************************* USB发送函数 end *********************************/

/************************************* USB接收函数 *************************************/
// 基础USB应用函数
void wk_usb_app_init(void);
void wk_usb_app_task(void);
void wk_otgfs1_irq_handler(void);

#ifdef __cplusplus
}
#endif

#endif
