#include "adc_pmsm.h"
#include <string.h>

/********** 全局变量定义 **********/
ADC_CurrentData_t g_adc_current_data = {0};

/********** 主要功能函数 **********/
/**
 * @brief ADC电流采样系统初始化
 */
void ADC_PMSM_Init(void)
{
    // 清零数据结构
    memset(&g_adc_current_data, 0, sizeof(ADC_CurrentData_t));

    // 初始化滤波器参数
    g_adc_current_data.lpf_alpha = LPF1_ALPHA;

    // 零飘校准
    ADC_PMSM_CalibrateOffset();
}

/**
 * @brief 零飘校准函数
 */
void ADC_PMSM_CalibrateOffset(void)
{
    const int calibration_samples = 1000;
    const float zero_drift_threshold = 10.0f;  // 零飘阈值10A
    float offset_sum[3] = {0};

    // 采集样本计算平均零飘
    for (int sample = 0; sample < calibration_samples; sample++) {
        ADC_TRIGGER_PREEMPT();
        while (!ADC_GET_PCCE_FLAG());
        ADC_CLEAR_PCCE_FLAG();

        offset_sum[0] += (float)ADC_READ_PDT1();
        offset_sum[1] += (float)ADC_READ_PDT2();
        offset_sum[2] += (float)ADC_READ_PDT3();

        for (volatile int i = 0; i < 1000; i++);  // ADC稳定延时
    }

    // 计算平均零飘并转换为电流值
    for (int i = 0; i < 3; i++) {
        float avg_adc = offset_sum[i] / calibration_samples;
        float calculated_offset = ADC_TO_CURRENT_COEFF * avg_adc - ADC_OFFSET_VOLTAGE;

        if (calculated_offset > zero_drift_threshold || calculated_offset < -zero_drift_threshold) {
            g_adc_current_data.offset_compensation[i] = 0.0f;  // 零飘过大，不补偿
        } else {
            g_adc_current_data.offset_compensation[i] = calculated_offset;
        }
    }
}

/**
 * @brief 三相电流采样处理主函数（20K电流环调用）
 * @param ia: A相电流输出指针
 * @param ib: B相电流输出指针
 * @param ic: C相电流输出指针
 */
void ADC_PMSM_ProcessCurrents(float* ia, float* ib, float* ic)
{
    // 读取ADC抢占通道123
    g_adc_current_data.adc_raw[0] = ADC_READ_PDT1();  // A相
    g_adc_current_data.adc_raw[1] = ADC_READ_PDT2();  // B相
    g_adc_current_data.adc_raw[2] = ADC_READ_PDT3();  // C相

    // ADC转电流值：Ir = 0.38671875 * Vcnt - 1000 - 零飘
    float temp_currents[3];
    for (int i = 0; i < 3; i++) {
        temp_currents[i] = ADC_TO_CURRENT_COEFF * (float)g_adc_current_data.adc_raw[i]
                          - ADC_OFFSET_VOLTAGE
                          - g_adc_current_data.offset_compensation[i];
        g_adc_current_data.current_raw[i] = temp_currents[i];
    }

    // 零序电流校正（Ia+Ib+Ic=0）
    float current_sum = temp_currents[0] + temp_currents[1] + temp_currents[2];
    float zero_sequence = current_sum * 0.333333333f;

    g_adc_current_data.current_corrected[0] = temp_currents[0] - zero_sequence;
    g_adc_current_data.current_corrected[1] = temp_currents[1] - zero_sequence;
    g_adc_current_data.current_corrected[2] = temp_currents[2] - zero_sequence;

    // 一阶低通滤波：y[n] = α * x[n] + (1-α) * y[n-1]
    for (int i = 0; i < 3; i++) {
        g_adc_current_data.current_filtered[i] = g_adc_current_data.lpf_alpha * g_adc_current_data.current_corrected[i] +
                                               (1.0f - g_adc_current_data.lpf_alpha) * g_adc_current_data.lpf1_prev[i];
        g_adc_current_data.lpf1_prev[i] = g_adc_current_data.current_filtered[i];
    }

    // 输出滤波后的电流值
    if (ia) *ia = g_adc_current_data.current_filtered[0];
    if (ib) *ib = g_adc_current_data.current_filtered[1];
    if (ic) *ic = g_adc_current_data.current_filtered[2];
}

/********** 备用滤波器函数（备用） **********/
/**
 * @brief 滑动平均滤波器更新
 * @param filter: 滤波器状态指针
 * @param input: 输入值
 * @return 滤波后的输出值
 */
static inline float MovingAvg_Update(ADC_MovingAvgFilter_t* filter, float input)
{
    // 更新缓冲区
    filter->sum -= filter->buffer[filter->index];  // 移除旧值
    filter->buffer[filter->index] = input;         // 存储新值
    filter->sum += input;                          // 加入新值
    
    // 更新索引
    filter->index = (filter->index + 1) % MOVING_AVG_WINDOW_SIZE;
    
    // 返回平均值
    return filter->sum * 0.333333333f;
}


