
/*
 * 电机控制默认配置头文件 (Motor Control Default Configuration Header)
 *
 * 本文件定义了VESC电机控制系统的所有默认配置参数。
 * 这些参数涵盖了电机控制的各个方面，包括：
 * - 基本控制模式和传感器配置
 * - 电流、电压、功率限制
 * - PID控制器参数
 * - FOC算法参数
 * - 安全保护参数
 * - 硬件相关配置
 *
 * 使用方式：
 * - 可通过定义相应宏来覆盖默认值
 * - 适用于不同硬件平台的参数定制
 * - 为系统提供安全的默认工作参数
 */

#ifndef MCCONF_DEFAULT_H_
#define MCCONF_DEFAULT_H_

// 默认设置 (Default Settings)
#ifndef MCCONF_DEFAULT_MOTOR_TYPE
#define MCCONF_DEFAULT_MOTOR_TYPE		MOTOR_TYPE_FOC				// 默认电机类型：FOC控制
#endif
#ifndef MCCONF_PWM_MODE
#define MCCONF_PWM_MODE					PWM_MODE_SYNCHRONOUS		// PWM模式：同步PWM
#endif
#ifndef MCCONF_SENSOR_MODE
#define MCCONF_SENSOR_MODE				SENSOR_MODE_SENSORLESS		// 传感器模式：无传感器
#endif
#ifndef MCCONF_COMM_MODE
#define MCCONF_COMM_MODE				COMM_MODE_INTEGRATE			// 换相模式：积分换相
#endif

// 限制参数 (Limit Parameters)
#ifndef MCCONF_L_CURRENT_MAX
#define MCCONF_L_CURRENT_MAX			60.0	// 电机电流上限 (A) - 正向最大电流
#endif
#ifndef MCCONF_L_CURRENT_MIN
#define MCCONF_L_CURRENT_MIN			-60.0	// 电机电流下限 (A) - 反向最大电流
#endif
#ifndef MCCONF_L_IN_CURRENT_MAX
#define MCCONF_L_IN_CURRENT_MAX			99.0	// 输入电流上限 (A) - 从电池吸取的最大电流
#endif
#ifndef MCCONF_L_IN_CURRENT_MIN
#define MCCONF_L_IN_CURRENT_MIN			-60.0	// 输入电流下限 (A) - 回馈到电池的最大电流
#endif
#ifndef MCCONF_L_MAX_ABS_CURRENT
#define MCCONF_L_MAX_ABS_CURRENT		130.0	// 绝对电流限制 (A) - 超过此值将产生故障
#endif
#ifndef MCCONF_L_MIN_VOLTAGE
#define MCCONF_L_MIN_VOLTAGE			8.0		// 最小输入电压 (V) - 欠压保护阈值
#endif
#ifndef MCCONF_L_MAX_VOLTAGE
#define MCCONF_L_MAX_VOLTAGE			57.0	// 最大输入电压 (V) - 过压保护阈值
#endif
#ifndef MCCONF_L_BATTERY_CUT_START
#define MCCONF_L_BATTERY_CUT_START		10.0	// 电池截止开始电压 (V) - 开始限制正向电流
#endif
#ifndef MCCONF_L_BATTERY_CUT_END
#define MCCONF_L_BATTERY_CUT_END		8.0		// 电池截止结束电压 (V) - 完全限制正向电流
#endif
#ifndef MCCONF_L_RPM_MAX
#define MCCONF_L_RPM_MAX				100000.0	// 最大转速限制 (RPM) - 正转速度上限
#endif
#ifndef MCCONF_L_RPM_MIN
#define MCCONF_L_RPM_MIN				-100000.0	// 最小转速限制 (RPM) - 反转速度下限
#endif
#ifndef MCCONF_L_RPM_START
#define MCCONF_L_RPM_START				0.8		// RPM电流限制开始比例 - 达到此转速比例时开始限流
#endif
#ifndef MCCONF_L_SLOW_ABS_OVERCURRENT
#define MCCONF_L_SLOW_ABS_OVERCURRENT	false	// 慢速过流检测 - 使用滤波电流进行过流故障检测
#endif
#ifndef MCCONF_L_MIN_DUTY
#define MCCONF_L_MIN_DUTY				0.005	// 最小占空比 - PWM最小占空比限制
#endif
#ifndef MCCONF_L_MAX_DUTY
#define MCCONF_L_MAX_DUTY				0.95	// 最大占空比 - PWM最大占空比限制
#endif
#ifndef MCCONF_L_CURR_MAX_RPM_FBRAKE
#define MCCONF_L_CURR_MAX_RPM_FBRAKE	300		// 全制动最大电转速 (ERPM) - 低速时使用全制动电流
#endif
#ifndef MCCONF_L_CURR_MAX_RPM_FBRAKE_CC
#define MCCONF_L_CURR_MAX_RPM_FBRAKE_CC	1500	// 电流控制全制动最大电转速 (ERPM) - 电流控制模式下的全制动转速
#endif
#ifndef MCCONF_L_LIM_TEMP_FET_START
#define MCCONF_L_LIM_TEMP_FET_START		85.0	// MOSFET限流起始温度 (°C) - 开始限制电流的温度
#endif
#ifndef MCCONF_L_LIM_TEMP_FET_END
#define MCCONF_L_LIM_TEMP_FET_END		100.0	// MOSFET关断温度 (°C) - 完全关断的温度
#endif
#ifndef MCCONF_L_LIM_TEMP_MOTOR_START
#define MCCONF_L_LIM_TEMP_MOTOR_START	85.0	// 电机限流起始温度 (°C) - 开始限制电流的温度
#endif
#ifndef MCCONF_L_LIM_TEMP_MOTOR_END
#define MCCONF_L_LIM_TEMP_MOTOR_END		100.0	// 电机关断温度 (°C) - 完全关断的温度
#endif
#ifndef MCCONF_L_LIM_TEMP_ACCEL_DEC
#define MCCONF_L_LIM_TEMP_ACCEL_DEC		0.15	// 加速时温度限制降低量 (°C) - 加速时降低温度阈值
#endif
#ifndef MCCONF_L_WATT_MAX
#define MCCONF_L_WATT_MAX				1500000.0	// 最大功率输出 (W) - 电机模式最大功率
#endif
#ifndef MCCONF_L_WATT_MIN
#define MCCONF_L_WATT_MIN				-1500000.0	// 最小功率输出 (W) - 制动模式最大功率
#endif
#ifndef MCCONF_L_CURRENT_MAX_SCALE
#define MCCONF_L_CURRENT_MAX_SCALE		1.0	// 最大电流缩放因子 - 动态调整最大电流限制
#endif
#ifndef MCCONF_L_CURRENT_MIN_SCALE
#define MCCONF_L_CURRENT_MIN_SCALE		1.0	// 最小电流缩放因子 - 动态调整最小电流限制
#endif
#ifndef MCCONF_L_DUTY_START
#define MCCONF_L_DUTY_START				1.0 // 占空比限流起始点 - 开始限制电流的占空比
#endif

// 通用PID参数 (Common PID Parameters)
#ifndef MCCONF_SP_PID_LOOP_RATE
#define MCCONF_SP_PID_LOOP_RATE			PID_RATE_1000_HZ // PID循环频率 - 1000Hz执行频率
#endif

// 速度PID参数 (Speed PID Parameters)
#ifndef MCCONF_S_PID_KP
#define MCCONF_S_PID_KP					0.004	// 比例增益 Kp - 速度误差的比例响应
#endif
#ifndef MCCONF_S_PID_KI
#define MCCONF_S_PID_KI					0.004	// 积分增益 Ki - 消除稳态误差
#endif
#ifndef MCCONF_S_PID_KD
#define MCCONF_S_PID_KD					0.0001	// 微分增益 Kd - 提高系统稳定性
#endif
#ifndef MCCONF_S_PID_KD_FILTER
#define MCCONF_S_PID_KD_FILTER			0.2	// 微分滤波器 - 减少微分项噪声
#endif
#ifndef MCCONF_S_PID_MIN_RPM
#define MCCONF_S_PID_MIN_RPM			900.0	// 最小允许转速 (RPM) - 速度控制下限
#endif
#ifndef MCCONF_S_PID_ALLOW_BRAKING
#define MCCONF_S_PID_ALLOW_BRAKING		true	// 允许制动 - 速度控制模式下允许再生制动
#endif
#ifndef MCCONF_S_PID_RAMP_ERPMS_S
#define MCCONF_S_PID_RAMP_ERPMS_S		25000.0	// 速度斜坡率 (ERPM/s) - 速度指令变化率限制
#endif

// 位置PID参数 (Position PID Parameters)
#ifndef MCCONF_P_PID_KP
#define MCCONF_P_PID_KP					0.025	// 比例增益 Kp - 位置误差的比例响应
#endif
#ifndef MCCONF_P_PID_KI
#define MCCONF_P_PID_KI					0.0		// 积分增益 Ki - 消除位置稳态误差
#endif
#ifndef MCCONF_P_PID_KD
#define MCCONF_P_PID_KD					0.00000	// 微分增益 Kd - 基于误差变化率的响应
#endif
#ifndef MCCONF_P_PID_KD_PROC
#define MCCONF_P_PID_KD_PROC			0.00035	// 过程微分增益 - 基于反馈值变化率的响应
#endif
#ifndef MCCONF_P_PID_KD_FILTER
#define MCCONF_P_PID_KD_FILTER			0.2		// 微分滤波器 - 减少微分项噪声
#endif
#ifndef MCCONF_P_PID_ANG_DIV
#define MCCONF_P_PID_ANG_DIV			1.0		// 角度除数 - 角度缩放因子
#endif
#ifndef MCCONF_P_PID_GAIN_DEC_ANGLE
#define MCCONF_P_PID_GAIN_DEC_ANGLE		0.0		// 增益递减角度 (rad) - 误差小于此值时降低PID增益
#endif
#ifndef MCCONF_P_PID_OFFSET
#define MCCONF_P_PID_OFFSET				0.0		// 角度偏移 (rad) - 位置零点偏移补偿
#endif

// 电流控制参数 (Current Control Parameters)
#ifndef MCCONF_CC_GAIN
#define MCCONF_CC_GAIN					0.0046	// 电流控制器误差增益 - 电流环控制增益
#endif
#ifndef MCCONF_CC_MIN_CURRENT
#define MCCONF_CC_MIN_CURRENT			0.05	// 最小允许电流 (A) - 电流控制下限
#endif
#ifndef MCCONF_CC_STARTUP_BOOST_DUTY
#define MCCONF_CC_STARTUP_BOOST_DUTY	0.01	// 电流控制启动占空比 - 电流控制模式下的最小占空比
#endif
#ifndef MCCONF_CC_RAMP_STEP
#define MCCONF_CC_RAMP_STEP				0.04	// 电流控制最大斜坡步长 - 占空比变化率限制
#endif

// BLDC参数 (BLDC Parameters)
#ifndef MCCONF_SL_MIN_RPM
#define MCCONF_SL_MIN_RPM				150		// 自动换相最小转速 (RPM) - 低于此转速时自动换相
#endif
#ifndef MCCONF_SL_MIN_ERPM_CYCLE_INT_LIMIT
#define MCCONF_SL_MIN_ERPM_CYCLE_INT_LIMIT	1100.0	// 反电动势耦合计算最小转速 (ERPM) - 计算BEMF耦合的最小转速
#endif
#ifndef MCCONF_SL_CYCLE_INT_LIMIT
#define MCCONF_SL_CYCLE_INT_LIMIT		62.0	// 磁通积分器限制 - 0 ERPM时的磁通积分限制
#endif
#ifndef MCCONF_SL_BEMF_COUPLING_K
#define MCCONF_SL_BEMF_COUPLING_K		600.0	// 反电动势耦合常数 - 输入电压到反电动势的耦合系数
#endif
#ifndef MCCONF_SL_PHASE_ADVANCE_AT_BR
#define MCCONF_SL_PHASE_ADVANCE_AT_BR	0.8		// 边界转速相位超前 - 在边界转速时的磁通积分器限制百分比
#endif
#ifndef MCCONF_SL_CYCLE_INT_BR
#define MCCONF_SL_CYCLE_INT_BR			80000.0	// 转速边界 (ERPM) - START和LOW区间之间的转速边界
#endif
#ifndef MCCONF_SL_MAX_FB_CURR_DIR_CHANGE
#define MCCONF_SL_MAX_FB_CURR_DIR_CHANGE	10.0	// 方向改变最大制动电流 (A) - 允许方向改变时的最大制动电流
#endif

// BLDC霍尔传感器表 (BLDC Hall Sensor Table)
#ifndef MCCONF_HALL_TAB_0
#define MCCONF_HALL_TAB_0				-1		// 霍尔状态0对应的换相序列 - 无效状态
#endif
#ifndef MCCONF_HALL_TAB_1
#define MCCONF_HALL_TAB_1				1		// 霍尔状态1对应的换相序列 - 第1个有效状态
#endif
#ifndef MCCONF_HALL_TAB_2
#define MCCONF_HALL_TAB_2				3		// 霍尔状态2对应的换相序列 - 第2个有效状态
#endif
#ifndef MCCONF_HALL_TAB_3
#define MCCONF_HALL_TAB_3				2		// 霍尔状态3对应的换相序列 - 第3个有效状态
#endif
#ifndef MCCONF_HALL_TAB_4
#define MCCONF_HALL_TAB_4				5		// 霍尔状态4对应的换相序列 - 第4个有效状态
#endif
#ifndef MCCONF_HALL_TAB_5
#define MCCONF_HALL_TAB_5				6		// 霍尔状态5对应的换相序列 - 第5个有效状态
#endif
#ifndef MCCONF_HALL_TAB_6
#define MCCONF_HALL_TAB_6				4		// 霍尔状态6对应的换相序列 - 第6个有效状态
#endif
#ifndef MCCONF_HALL_TAB_7
#define MCCONF_HALL_TAB_7				-1		// 霍尔状态7对应的换相序列 - 无效状态
#endif
#ifndef MCCONF_HALL_ERPM
#define MCCONF_HALL_ERPM				2000.0	// 霍尔混合模式转速阈值 (ERPM) - 超过此转速使用无传感器换相
#endif

// FOC参数 (FOC Parameters)
#ifndef MCCONF_FOC_CURRENT_KP
#define MCCONF_FOC_CURRENT_KP			0.03	// FOC电流环比例增益 - d/q轴电流控制器Kp
#endif
#ifndef MCCONF_FOC_CURRENT_KI
#define MCCONF_FOC_CURRENT_KI			50.0	// FOC电流环积分增益 - d/q轴电流控制器Ki
#endif
#ifndef MCCONF_FOC_F_ZV
#define MCCONF_FOC_F_ZV					25000.0	// 零矢量频率 (Hz) - 零矢量注入频率
#endif
#ifndef MCCONF_FOC_DT_US
#define MCCONF_FOC_DT_US				0.12 // 死区时间补偿 (μs) - MOSFET死区时间补偿
#endif
#ifndef MCCONF_FOC_ENCODER_INVERTED
#define MCCONF_FOC_ENCODER_INVERTED		false	// 编码器方向反转 - 是否反转编码器计数方向
#endif
#ifndef MCCONF_FOC_ENCODER_OFFSET
#define MCCONF_FOC_ENCODER_OFFSET		180.0	// 编码器偏移角度 (°) - 编码器零点偏移
#endif
#ifndef MCCONF_FOC_ENCODER_RATIO
#define MCCONF_FOC_ENCODER_RATIO		7.0		// 编码器传动比 - 编码器转数与电机转数比
#endif
#ifndef MCCONF_FOC_SENSOR_MODE
#define MCCONF_FOC_SENSOR_MODE			FOC_SENSOR_MODE_SENSORLESS	// FOC传感器模式 - 无传感器模式
#endif
#ifndef MCCONF_FOC_PLL_KP
#define MCCONF_FOC_PLL_KP				2000.0	// PLL比例增益 - 锁相环比例增益
#endif
#ifndef MCCONF_FOC_PLL_KI
#define MCCONF_FOC_PLL_KI				30000.0	// PLL积分增益 - 锁相环积分增益
#endif
#ifndef MCCONF_FOC_MOTOR_L
#define MCCONF_FOC_MOTOR_L				0.000007	// 电机电感 (H) - 相电感值
#endif
#ifndef MCCONF_FOC_MOTOR_R
#define MCCONF_FOC_MOTOR_R				0.015	// 电机电阻 (Ω) - 相电阻值
#endif
#ifndef MCCONF_FOC_MOTOR_FLUX_LINKAGE
#define MCCONF_FOC_MOTOR_FLUX_LINKAGE	0.00245	// 磁链 (Wb) - 永磁体磁链
#endif
#ifndef MCCONF_FOC_MOTOR_LD_LQ_DIFF
#define MCCONF_FOC_MOTOR_LD_LQ_DIFF		0.0		// d/q轴电感差 (H) - Ld-Lq电感差值
#endif
#ifndef MCCONF_FOC_OBSERVER_GAIN
#define MCCONF_FOC_OBSERVER_GAIN		9e7		// 观测器增益 - 可设为约600/L的值
#endif
#ifndef MCCONF_FOC_OBSERVER_GAIN_SLOW
#define MCCONF_FOC_OBSERVER_GAIN_SLOW	0.05	// 低速观测器增益缩放 - 最小占空比时的增益缩放
#endif
#ifndef MCCONF_FOC_OBSERVER_OFFSET
#define MCCONF_FOC_OBSERVER_OFFSET		-1.0	// 观测器偏移 - 定时器更新周期中的偏移
#endif
#ifndef MCCONF_FOC_DUTY_DOWNRAMP_KP
#define MCCONF_FOC_DUTY_DOWNRAMP_KP		50.0	// 占空比下降斜坡Kp - 占空比减小时的PI控制器比例增益
#endif
#ifndef MCCONF_FOC_DUTY_DOWNRAMP_KI
#define MCCONF_FOC_DUTY_DOWNRAMP_KI		1000.0	// 占空比下降斜坡Ki - 占空比减小时的PI控制器积分增益
#endif
#ifndef MCCONF_FOC_START_CURR_DEC
#define MCCONF_FOC_START_CURR_DEC		1.0	// 启动电流衰减因子 - 启动时电流衰减到此比例
#endif
#ifndef MCCONF_FOC_START_CURR_DEC_RPM
#define MCCONF_FOC_START_CURR_DEC_RPM	2500.0	// 启动电流恢复转速 (RPM) - 达到此转速时恢复全电流
#endif
#ifndef MCCONF_FOC_OPENLOOP_RPM
#define MCCONF_FOC_OPENLOOP_RPM			1500.0	// 开环转速 (RPM) - 无传感器低速或寻找索引脉冲时的开环转速
#endif
#ifndef MCCONF_FOC_OPENLOOP_RPM_LOW
#define MCCONF_FOC_OPENLOOP_RPM_LOW		0.0		// 低电流开环转速比例 - 最小电机电流时的开环转速比例
#endif
#ifndef MCCONF_FOC_D_GAIN_SCALE_START
#define MCCONF_FOC_D_GAIN_SCALE_START	0.9		// d轴增益缩放起始调制度 - 开始减小d轴电流控制器增益的调制度
#endif
#ifndef MCCONF_FOC_D_GAIN_SCALE_MAX_MOD
#define MCCONF_FOC_D_GAIN_SCALE_MAX_MOD	0.2		// d轴最大调制度增益 - 最大调制度时的d轴电流控制器增益
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_HYST
#define MCCONF_FOC_SL_OPENLOOP_HYST		0.1		// 无传感器开环滞后时间 (s) - 低于最小转速多长时间激活开环
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_TIME
#define MCCONF_FOC_SL_OPENLOOP_TIME		0.05	// 开环保持时间 (s) - 斜坡后保持开环的时间
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_BOOST_Q
#define MCCONF_FOC_SL_OPENLOOP_BOOST_Q	0.0		// 开环q轴电流增强 (A) - 开环过程中的q轴电流增强
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_MAX_Q
#define MCCONF_FOC_SL_OPENLOOP_MAX_Q	-1.0	// 开环q轴最大电流 (A) - 开环过程中的q轴最大电流
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_T_LOCK
#define MCCONF_FOC_SL_OPENLOOP_T_LOCK	0.0		// 开环锁定时间 (s) - 开环序列开始时锁定电机的时间
#endif
#ifndef MCCONF_FOC_SL_OPENLOOP_T_RAMP
#define MCCONF_FOC_SL_OPENLOOP_T_RAMP	0.1		// 开环斜坡时间 (s) - 电机斜坡到开环速度的时间
#endif
#ifndef MCCONF_FOC_HALL_TAB_0
#define MCCONF_FOC_HALL_TAB_0			255		// FOC霍尔表0 - 霍尔状态0对应的角度(255=无效)
#endif
#ifndef MCCONF_FOC_HALL_TAB_1
#define MCCONF_FOC_HALL_TAB_1			255		// FOC霍尔表1 - 霍尔状态1对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_2
#define MCCONF_FOC_HALL_TAB_2			255		// FOC霍尔表2 - 霍尔状态2对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_3
#define MCCONF_FOC_HALL_TAB_3			255		// FOC霍尔表3 - 霍尔状态3对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_4
#define MCCONF_FOC_HALL_TAB_4			255		// FOC霍尔表4 - 霍尔状态4对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_5
#define MCCONF_FOC_HALL_TAB_5			255		// FOC霍尔表5 - 霍尔状态5对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_6
#define MCCONF_FOC_HALL_TAB_6			255		// FOC霍尔表6 - 霍尔状态6对应的角度
#endif
#ifndef MCCONF_FOC_HALL_TAB_7
#define MCCONF_FOC_HALL_TAB_7			255		// FOC霍尔表7 - 霍尔状态7对应的角度(255=无效)
#endif
#ifndef MCCONF_FOC_HALL_INTERP_ERPM
#define MCCONF_FOC_HALL_INTERP_ERPM		500		// 霍尔插值最小转速 (ERPM) - 低于此转速不进行霍尔传感器插值
#endif
#ifndef MCCONF_FOC_SL_ERPM
#define MCCONF_FOC_SL_ERPM				2500.0	// 无传感器切换转速 (ERPM) - 超过此转速仅使用观测器
#endif
#ifndef MCCONF_FOC_SAMPLE_V0_V7
#define MCCONF_FOC_SAMPLE_V0_V7			false	// V0和V7采样 - 在v0和v7矢量时都运行控制环(需要相分流器)
#endif
#ifndef MCCONF_FOC_SAMPLE_HIGH_CURRENT
#define MCCONF_FOC_SAMPLE_HIGH_CURRENT	false	// 大电流采样模式 - 大电流采样模式(需要三个分流器)
#endif
#ifndef MCCONF_FOC_SAT_COMP_MODE
#define MCCONF_FOC_SAT_COMP_MODE		SAT_COMP_LAMBDA		// 定子饱和补偿模式 - 磁链饱和补偿
#endif
#ifndef MCCONF_FOC_SAT_COMP
#define MCCONF_FOC_SAT_COMP				0.0		// 定子饱和补偿因子 - 饱和补偿系数
#endif
#ifndef MCCONF_FOC_TEMP_COMP
#define MCCONF_FOC_TEMP_COMP			false	// 电机温度补偿 - 是否启用温度补偿
#endif
#ifndef MCCONF_FOC_TEMP_COMP_BASE_TEMP
#define MCCONF_FOC_TEMP_COMP_BASE_TEMP	25.0	// 温度补偿基准温度 (°C) - 温度补偿的参考温度
#endif
#ifndef MCCONF_FOC_CURRENT_FILTER_CONST
#define MCCONF_FOC_CURRENT_FILTER_CONST	0.1		// 电流滤波常数 - 滤波电流的滤波器常数
#endif
#ifndef MCCONF_FOC_CC_DECOUPLING
#define MCCONF_FOC_CC_DECOUPLING		FOC_CC_DECOUPLING_DISABLED // 电流控制器解耦 - 禁用解耦
#endif
#ifndef MCCONF_FOC_OBSERVER_TYPE
#define MCCONF_FOC_OBSERVER_TYPE		FOC_OBSERVER_MXLEMMING_LAMBDA_COMP // FOC位置观测器类型 - 带磁链补偿的观测器
#endif
#ifndef MCCONF_FOC_HFI_VOLTAGE_START
#define MCCONF_FOC_HFI_VOLTAGE_START	20 // HFI启动电压 (V) - 解决歧义时的HFI电压
#endif
#ifndef MCCONF_FOC_HFI_VOLTAGE_RUN
#define MCCONF_FOC_HFI_VOLTAGE_RUN		4 // HFI运行电压 (V) - 跟踪期间的HFI电压
#endif
#ifndef MCCONF_FOC_HFI_GAIN
#define MCCONF_FOC_HFI_GAIN				0.3 // HFI校正增益 - HFI V2的校正增益
#endif
#ifndef MCCONF_FOC_HFI_HYST
#define MCCONF_FOC_HFI_HYST				0.0 // HFI感应矢量偏移滞后 - HFI V2的感应矢量偏移滞后
#endif
#ifndef MCCONF_FOC_HFI_VOLTAGE_MAX
#define MCCONF_FOC_HFI_VOLTAGE_MAX		6 // HFI最大电压 (V) - 最大电流时跟踪期间的HFI电压
#endif
#ifndef MCCONF_FOC_SL_ERPM_HFI
#define MCCONF_FOC_SL_ERPM_HFI			3000.0	// HFI切换转速 (ERPM) - 超过此转速仅使用观测器
#endif
#ifndef MCCONF_FOC_HFI_START_SAMPLES
#define MCCONF_FOC_HFI_START_SAMPLES	5 // HFI启动采样次数 - 启动时采样频率以解决歧义
#endif
#ifndef MCCONF_FOC_HFI_OBS_OVR_SEC
#define MCCONF_FOC_HFI_OBS_OVR_SEC		0.001 // HFI观测器覆盖时间 (s) - 进入HFI速度时继续使用观测器的时间
#endif
#ifndef MCCONF_FOC_HFI_SAMPLES
#define MCCONF_FOC_HFI_SAMPLES			HFI_SAMPLES_16 // HFI采样数 - 每转电机的HFI采样数
#endif
#ifndef MCCONF_FOC_OFFSETS_CAL_ON_BOOT
#define MCCONF_FOC_OFFSETS_CAL_ON_BOOT	true // 启动时偏移校准 - 每次启动时测量偏移
#endif
#ifndef MCCONF_FOC_OFFSETS_CURRENT_0
#define MCCONF_FOC_OFFSETS_CURRENT_0	2048.0 // Current 0 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_CURRENT_1
#define MCCONF_FOC_OFFSETS_CURRENT_1	2048.0 // Current 1 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_CURRENT_2
#define MCCONF_FOC_OFFSETS_CURRENT_2	2048.0 // Current 2 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_0
#define MCCONF_FOC_OFFSETS_VOLTAGE_0	0.0 // Voltage 0 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_1
#define MCCONF_FOC_OFFSETS_VOLTAGE_1	0.0 // Voltage 1 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_2
#define MCCONF_FOC_OFFSETS_VOLTAGE_2	0.0 // Voltage 2 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_0
#define MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_0	0.0 // Voltage undriven 0 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_1
#define MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_1	0.0 // Voltage undriven 1 offset
#endif
#ifndef MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_2
#define MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_2	0.0 // Voltage undriven 2 offset
#endif
#ifndef MCCONF_FOC_PHASE_FILTER_ENABLE
#define MCCONF_FOC_PHASE_FILTER_ENABLE	true // Use phase voltage filters when available
#endif
#ifndef MCCONF_FOC_PHASE_FILTER_DISABLE_FAULT
#define MCCONF_FOC_PHASE_FILTER_DISABLE_FAULT	true // Disable phase filter fault code
#endif
#ifndef MCCONF_FOC_PHASE_FILTER_MAX_ERPM
#define MCCONF_FOC_PHASE_FILTER_MAX_ERPM	4000.0 // Use phase filter up to this ERPM
#endif
#ifndef MCCONF_FOC_MTPA_MODE
#define MCCONF_FOC_MTPA_MODE				MTPA_MODE_OFF // Maximum torque per amp (MTPA) algorithm mode
#endif
#ifndef MCCONF_FOC_FW_CURRENT_MAX
#define MCCONF_FOC_FW_CURRENT_MAX		0.0 // Maximum field weakening current
#endif
#ifndef MCCONF_FOC_FW_DUTY_START
#define MCCONF_FOC_FW_DUTY_START		0.9 // Start field weakening at this fraction of max duty cycle
#endif
#ifndef MCCONF_FOC_FW_RAMP_TIME
#define MCCONF_FOC_FW_RAMP_TIME			0.2 // Ramp time for field weakening current
#endif
#ifndef MCCONF_FOC_FW_Q_CURRENT_FACTOR
#define MCCONF_FOC_FW_Q_CURRENT_FACTOR	0.02 // Factor of the FW-current to feed to the Q-axis to slow motor down when setting 0 current
#endif
#ifndef MCCONF_FOC_SPEED_SOURCE
#define MCCONF_FOC_SPEED_SOURCE			SPEED_SRC_OBSERVER // Position source for speed trackers
#endif

// GPD
#ifndef MCCONF_GPD_BUFFER_NOTIFY_LEFT
#define MCCONF_GPD_BUFFER_NOTIFY_LEFT	200		// Notify when the buffer space left is less than this
#endif
#ifndef MCCONF_GPD_BUFFER_INTERPOL
#define MCCONF_GPD_BUFFER_INTERPOL		0		// Buffer interpolation
#endif
#ifndef MCCONF_GPD_CURRENT_FILTER_CONST
#define MCCONF_GPD_CURRENT_FILTER_CONST	0.1		// GPD电流滤波常数 - 通用PWM驱动电流滤波器常数
#endif
#ifndef MCCONF_GPD_CURRENT_KP
#define MCCONF_GPD_CURRENT_KP			0.03	// GPD电流比例增益 - 通用PWM驱动电流控制Kp
#endif
#ifndef MCCONF_GPD_CURRENT_KI
#define MCCONF_GPD_CURRENT_KI			50.0	// GPD电流积分增益 - 通用PWM驱动电流控制Ki
#endif

// 杂项参数 (Miscellaneous Parameters)
#ifndef MCCONF_M_FAULT_STOP_TIME
#define MCCONF_M_FAULT_STOP_TIME		500	// 故障停止时间 (ms) - 故障发生时忽略指令的持续时间
#endif
#ifndef MCCONF_M_RAMP_STEP
#define MCCONF_M_RAMP_STEP				0.02	// 占空比斜坡步长 - 最大占空比时的占空比斜坡步长(1000次/秒)
#endif
#ifndef MCCONF_M_CURRENT_BACKOFF_GAIN
#define MCCONF_M_CURRENT_BACKOFF_GAIN	0.5		// 电流回退增益 - 电流限制算法的误差增益
#endif
#ifndef MCCONF_M_ENCODER_COUNTS
#define MCCONF_M_ENCODER_COUNTS			8192	// 编码器计数 - 编码器每转计数数
#endif
#ifndef MCCONF_M_ENCODER_SIN_AMP
#define MCCONF_M_ENCODER_SIN_AMP		1.0	// 正弦幅值 - Sin/Cos编码器正弦信号幅值
#endif
#ifndef MCCONF_M_ENCODER_SIN_OFFSET
#define MCCONF_M_ENCODER_SIN_OFFSET		1.65 // 正弦偏移 (V) - Sin/Cos编码器正弦信号偏移
#endif
#ifndef MCCONF_M_ENCODER_COS_AMP
#define MCCONF_M_ENCODER_COS_AMP		1.0	// 余弦幅值 - Sin/Cos编码器余弦信号幅值
#endif
#ifndef MCCONF_M_ENCODER_COS_OFFSET
#define MCCONF_M_ENCODER_COS_OFFSET		1.65 // 余弦偏移 (V) - Sin/Cos编码器余弦信号偏移
#endif
#ifndef MCCONF_M_ENCODER_SINCOS_FILTER
#define MCCONF_M_ENCODER_SINCOS_FILTER	0.5		// Sin/Cos编码器滤波常数 - 信号滤波器常数
#endif
#ifndef MCCONF_M_ENCODER_SINCOS_PHASE
#define MCCONF_M_ENCODER_SINCOS_PHASE		0.0		// Sin/Cos编码器相位校正 - 信号相位校正角度
#endif
#ifndef MCCONF_M_SENSOR_PORT_MODE
#define MCCONF_M_SENSOR_PORT_MODE		SENSOR_PORT_MODE_HALL // 传感器端口模式 - 霍尔/编码器端口模式
#endif
#ifndef MCCONF_M_INVERT_DIRECTION
#define MCCONF_M_INVERT_DIRECTION		false // 反转电机方向 - 是否反转电机旋转方向
#endif
#ifndef MCCONF_M_DRV8301_OC_MODE
#define MCCONF_M_DRV8301_OC_MODE		DRV8301_OC_LIMIT // DRV8301过流保护模式 - 限流模式
#endif
#ifndef MCCONF_M_DRV8301_OC_ADJ
#define MCCONF_M_DRV8301_OC_ADJ			16 // DRV8301过流保护阈值 - 过流检测阈值设置
#endif
#ifndef MCCONF_M_BLDC_F_SW_MIN
#define MCCONF_M_BLDC_F_SW_MIN			3000 // BLDC最小开关频率 (Hz) - BLDC模式下的最小开关频率
#endif
#ifndef MCCONF_M_BLDC_F_SW_MAX
#define MCCONF_M_BLDC_F_SW_MAX			35000 // BLDC最大开关频率 (Hz) - BLDC模式下的最大开关频率
#endif
#ifndef MCCONF_M_DC_F_SW
#define MCCONF_M_DC_F_SW				25000 // DC开关频率 (Hz) - DC模式下的开关频率
#endif
#ifndef MCCONF_M_NTC_MOTOR_BETA
#define MCCONF_M_NTC_MOTOR_BETA			3380.0 // 电机NTC Beta值 - 电机热敏电阻的Beta系数
#endif
#ifndef MCCONF_M_OUT_AUX_MODE
#define MCCONF_M_OUT_AUX_MODE			OUT_AUX_MODE_OFF // 辅助输出模式 - 辅助输出端口模式
#endif
#ifndef MCCONF_M_MOTOR_TEMP_SENS_TYPE
#define MCCONF_M_MOTOR_TEMP_SENS_TYPE	TEMP_SENSOR_NTC_10K_25C // 电机温度传感器类型 - NTC 10K@25°C
#endif
#ifndef MCCONF_M_PTC_MOTOR_COEFF
#define MCCONF_M_PTC_MOTOR_COEFF		0.61 // 电机PTC系数 (%/K) - PTC传感器温度系数
#endif
#ifndef MCCONF_M_NTCX_PTCX_RES
#define MCCONF_M_NTCX_PTCX_RES			10000.0 // 自定义NTC/PTC电阻 (Ω) - 自定义温度传感器电阻值
#endif
#ifndef MCCONF_M_NTCX_PTCX_BASE_TEMP
#define MCCONF_M_NTCX_PTCX_BASE_TEMP	25.0 // 自定义NTC/PTC基准温度 (°C) - 自定义温度传感器基准温度
#endif
#ifndef MCCONF_M_HALL_EXTRA_SAMPLES
#define MCCONF_M_HALL_EXTRA_SAMPLES		1 // 霍尔额外采样数 - 读取霍尔传感器时用于滤波的额外采样
#endif
#ifndef MCCONF_M_BATT_FILTER_CONST
#define MCCONF_M_BATT_FILTER_CONST		45 // 电池电平滤波常数 - 电池电量滤波器常数
#endif

// 设置信息 (Setup Information)
#ifndef MCCONF_SI_MOTOR_POLES
#define MCCONF_SI_MOTOR_POLES			14 // 电机极数 - 电机磁极对数×2
#endif
#ifndef MCCONF_SI_GEAR_RATIO
#define MCCONF_SI_GEAR_RATIO			3 // 齿轮比 - 减速器传动比
#endif
#ifndef MCCONF_SI_WHEEL_DIAMETER
#define MCCONF_SI_WHEEL_DIAMETER		0.083 // 轮径 (m) - 车轮直径
#endif
#ifndef MCCONF_SI_BATTERY_TYPE
#define MCCONF_SI_BATTERY_TYPE			BATTERY_TYPE_LIION_3_0__4_2 // 电池类型 - 锂离子电池3.0-4.2V
#endif
#ifndef MCCONF_SI_BATTERY_CELLS
#define MCCONF_SI_BATTERY_CELLS			3 // 电池串联数 - 电池组串联节数
#endif
#ifndef MCCONF_SI_BATTERY_AH
#define MCCONF_SI_BATTERY_AH			6.0 // 电池容量 (Ah) - 电池安时容量
#endif
#ifndef MCCONF_SI_MOTOR_NL_CURRENT
#define MCCONF_SI_MOTOR_NL_CURRENT		1.0 // 电机空载电流 (A) - 电机无负载时的电流
#endif

// BMS参数 (Battery Management System Parameters)
#ifndef MCCONF_BMS_TYPE
#define MCCONF_BMS_TYPE					BMS_TYPE_VESC	// BMS类型 - VESC内置BMS
#endif
#ifndef MCCONF_BMS_LIMIT_MODE
#define MCCONF_BMS_LIMIT_MODE			3	// BMS限制模式 - 限制模式选择
#endif
#ifndef MCCONF_BMS_T_LIMIT_START
#define MCCONF_BMS_T_LIMIT_START		45	// BMS温度限制开始 (°C) - 开始温度限制的温度
#endif
#ifndef MCCONF_BMS_T_LIMIT_END
#define MCCONF_BMS_T_LIMIT_END			65	// BMS温度限制结束 (°C) - 完全限制的温度
#endif
#ifndef MCCONF_BMS_SOC_LIMIT_START
#define MCCONF_BMS_SOC_LIMIT_START		0.05	// BMS SOC限制开始 - 开始SOC限制的电量百分比
#endif
#ifndef MCCONF_BMS_SOC_LIMIT_END
#define MCCONF_BMS_SOC_LIMIT_END		0	// BMS SOC限制结束 - 完全限制的电量百分比
#endif
#ifndef MCCONF_BMS_FWD_CAN_MODE
#define MCCONF_BMS_FWD_CAN_MODE			BMS_FWD_CAN_MODE_DISABLED	// BMS CAN转发模式 - 禁用CAN转发
#endif

#endif /* MCCONF_DEFAULT_H_ */
