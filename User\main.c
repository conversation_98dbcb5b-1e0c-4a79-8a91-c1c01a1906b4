/********************************************************************************
  * @file     main.c
  * @brief    Main program body
  * <AUTHOR>
  * @date     2025-01-06
  * @version  V1.0.0
  * @note     此文件包含AT32A423微控制器应用程序的主函数和初始化序列。
  *********************************************************************************/
#include "at32a423.h"                   // Device header

#include "main.h"
#include "AnoPTv8.h"
#include "SysCtl_AllHeaders.h"

/**
  * @brief  等待电源稳定，在8MHz频率下延时约60ms
  * @param  无
  * @retval 无
  */
static void wk_wait_for_power_stable(void)
{
    volatile uint32_t delay = 0;
    for(delay = 0; delay < 50000; delay++);
}

/**
  * @brief main function.
  * @param  none
  * @retval none
  */
int main(void)
{
    wk_wait_for_power_stable();
    AT32A423_system_init();
    delay_init();
    //fnSystemInitParam(); // 系统参数初始化
    user_init();
    

    while (1)
    {
        Timer_Tasks_Execute();       // 定时任务执行
        wk_usb_app_task();           // usb任务执行
        System_StatusUpdate();       // 系统保护状态机更新
        fnSysMooreCal(&SysMoore);    // 电机状态执行
        AnoPTv8HwTrigger1ms();       //
        //Update_System_Protection_Optimized();
    }
}



