#ifndef __ANOPTV8_H__
#define __ANOPTV8_H__

#include "AnoPTv8FrameFactory.h"
#include <string.h>

#define ANOPTV8_MYDEVID		(0x01)      // 本机设备地址
#define ANOPTV8DEVID_SWJ	(0xFE)      // 上位机设备地址
#define ANOPTV8DEVID_ALL	(0xFF)      // 广播地址
#define ANOPTV8_HWVER		(101)       // 硬件版本号
#define ANOPTV8_SWVER		(101)       // 软件版本号
#define ANOPTV8_BLVER		(101)       // Bootloader版本号
#define ANOPTV8_PTVER		(101)       // 协议版本号

#define ANOPTV8_FRAME_HEAD			0xAB    // 帧头标识
#define ANOPTV8_FRAME_HEADLEN 		6       // 帧头长度(字节)
#define ANOPTV8_FRAME_MAXDATALEN	256     // 数据段最大长度
#define ANOPTV8_FRAME_MAXFRAMELEN	ANOPTV8_FRAME_MAXDATALEN+ANOPTV8_FRAME_HEADLEN+2  // 最大帧长度(含校验)

#define ANOPTV8_PAR_MAXCOUNT	100         // 最大参数数量
#define ANOPTV8_CMD_MAXCOUNT	30          // 最大命令数量

#define LOG_COLOR_DEFAULT	0           // 默认日志颜色(白色)
#define LOG_COLOR_RED	  	1           // 红色日志
#define LOG_COLOR_GREEN		2           // 绿色日志
#define LOG_COLOR_BLUE		3           // 蓝色日志

#define SENDBUFLEN 			8           // 发送缓冲区数量


// 获取数据的每个字节，适用于int16、int32、float等
#define BYTE0(dwTemp) (*((char *)(&dwTemp)))
#define BYTE1(dwTemp) (*((char *)(&dwTemp) + 1))
#define BYTE2(dwTemp) (*((char *)(&dwTemp) + 2))
#define BYTE3(dwTemp) (*((char *)(&dwTemp) + 3))

#define LIMIT( x,min,max ) ( ((x) <= (min)) ? (min) : ( ((x) > (max))? (max) : (x) ) )

// V8协议帧结构定义
typedef struct {
    uint8_t head;
    uint8_t sdevid;
    uint8_t ddevid;
    uint8_t frameid;
    uint16_t datalen;
    uint8_t data[ANOPTV8_FRAME_MAXDATALEN];
    uint8_t sumcheck;
    uint8_t addcheck;
} __attribute__ ((__packed__)) _st_frame_v8;

// 联合体，按字节格式访问V8协议帧结构体
typedef union {
    _st_frame_v8 frame;
    uint8_t rawBytes[sizeof(_st_frame_v8)];
} _un_frame_v8;

typedef struct {
    uint8_t id;
    void * 		pval;
    const int32_t	min;
    const int32_t	max;
    const uint32_t	mul;
    const char		name[20];
    const char		info[70];
} __attribute__ ((__packed__)) _st_par_info;


typedef struct
{
    uint8_t cmdid0;
    uint8_t cmdid1;
    uint8_t cmdid2;
    uint8_t cmdval0;
    uint8_t cmdval1;
    uint8_t cmdval2;
    uint8_t cmdval3;
    uint8_t cmdval4;
    uint8_t cmdval5;
    uint8_t cmdval6;
    uint8_t cmdval7;
} __attribute__ ((__packed__)) _st_cmd;

typedef struct
{
    const _st_cmd		cmd;
    const char		name[20];
    const char		info[50];
    void 		(*pFun)(void);
} __attribute__ ((__packed__)) _st_cmd_info;





#endif
