#ifndef __HARDWAREINTERFACE_H
#define __HARDWAREINTERFACE_H

#include "MotorParams.h"
#include "MotorData.h"

/* 硬件接口函数 */
void AnoPTv8HwSendBytes(uint8_t *buf, uint16_t len);
void AnoPTv8HwRecvByte(uint8_t dat);
void AnoPTv8HwRecvBytes(uint8_t *buf, uint16_t len);
void AnoPTv8HwTrigger1ms(void);

/* 参数回调函数 */
void AnoPTv8HwParValRecvCallback(uint16_t parid, int32_t parval);
void AnoPTv8HwParCmdRecvCallback(uint8_t id, uint16_t val);
void AnoPTv8HwParCmdResetParameter(void);
void AnoPTv8HwParCmdResetAccCal(void);
void AnoPTv8HwParCmdResetMagCal(void);
void AnoPTv8HwParCmdFinishAccCal(void);
void AnoPTv8HwParCmdFinishMagCal(void);

/* 初始化函数 */
void AnoPTv8HwInit(void);          // 协议初始化
void AnoPTv8HwParamsInit(void);    // 参数初始化
void AnoPTv8HwCmdInit(void);       // 命令初始化
void AnoPTv8HwDataInit(void);      // 数据初始化

/* 数据发送函数 */
void AnoPTv8HwSendFrame(uint8_t frame);     // 发送指定帧
void AnoPTv8HwDataTest(void);               // 数据测试函数

/* 格式化打印函数 */
void Usb_printf(uint8_t string_color, const char *format, ...); 
void Usb_printf_direct(uint8_t string_color, const char *format, ...);

/* 字符串发送函数 */
void AnoPTv8SendStr(uint8_t daddr, uint8_t string_color, char *str);

/* 其他常用函数 */
void AnoPTv8SendBuf(uint8_t daddr, uint8_t fid, uint8_t *buf, uint16_t len);
void AnoPTv8SendAnyFrame(uint8_t daddr, uint8_t fid, const uint8_t *dat, const uint8_t len);

#endif
