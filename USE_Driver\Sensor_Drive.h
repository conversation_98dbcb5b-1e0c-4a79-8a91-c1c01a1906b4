/**
  * @file     Sensor_Drive.h
  * @brief    ADC驱动头文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  */

#ifndef __ADC_DRIVE_H
#define __ADC_DRIVE_H

#include "at32a423.h"
#include "arm_math.h"
#include "LBQ_design.h"
#include "sysTypeDef.h"

/* ADC计算系数定义 */
#define ADC_BUS_COEFF      0.268555f   // 母线电压系数0.2685547f
#define ADC_LINE_COEFF     0.58196f    // 相电压系数0.58196f
#define ADC_28V_COEFF      0.01128f     // 28V电压系数
#define ADC_UV_OFFSET      1247.4f     // UV相偏置电压
#define ADC_UW_OFFSET      1249.4f     // UW相偏置电压


/* ADC计算结果结构体 */
typedef struct {
    /* 按ADC序列排序的普通通道采样结果 - 前15个float值可直接用于F5帧 */
    float temp_cap1;         // 序列0 - 电容温度1
    float uv_voltage;        // 序列1 - UV线电压  
    float uw_voltage;        // 序列2 - UW线电压
    float temp_oll;          // 序列3 - OLL温度
    float temp_cap2;         // 序列4 - 电容温度2
    float temp_mot1;         // 序列5 - 电机温度1
    float temp_mot2;         // 序列6 - 电机温度2
    float bus_voltage;       // 序列7 - 母线电压
    float temp_PT100_6;      // 序列8 - PT100温度6
    float temp_PT1000_7;     // 序列9 - PT1000温度7
    float temp_bmf_u;        // 序列10 - U相温度
    float temp_bmf_v;        // 序列11 - V相温度
    float temp_bmf_w;        // 序列12 - W相温度
    float v28_voltage;       // 序列13 - 28V电压
    float v28v2_voltage;     // 序列14 - 28V电压2
    
    /* 其他数据（非ADC普通通道） */
    float current_u;         // U相电流（来自抢占通道）
    float current_v;         // V相电流（来自抢占通道）
    float current_w;         // W相电流（来自抢占通道）
    float bus_current;       // 母线电流（计算得出）

    /* 温度传感器无效性标志位 */
    uint16_t temp_invalid_flags;
} ADC_Result_t;
//temp_invalid_flags 掩码操作说明：
// uint16_t temp_mot1_invalid : 1;             // 电机温度1无效标志case 0
// uint16_t temp_mot2_invalid : 1;             // 电机温度2无效标志case 1
// uint16_t temp_cap1_invalid : 1;             // 电容温度1无效标志case 2
// uint16_t temp_cap2_invalid : 1;             // 电容温度2无效标志case 3
// uint16_t temp_oll_invalid : 1;              // 油温温度无效标志 case 4
// uint16_t temp_bmf_u_invalid : 1;            // U相温度无效标志  case 5
// uint16_t temp_bmf_v_invalid : 1;            // V相温度无效标志  case 6
// uint16_t temp_bmf_w_invalid : 1;            // W相温度无效标志  case 7
// uint16_t temp_bmf_PT100_6_invalid : 1;      // 预留PT100温度6无效标志case 8      
// uint16_t temp_bmf_PT1000_7_invalid : 1;     // 预留PT1000温度7无效标志case 9
// uint16_t reserved : 6;                      // 保留位


typedef struct {
    /* 电机运行参数 */
    float motor_torque;      // 电机转矩
    float motor_power;       // 电机功率
    float motor_speed;       // 电机转速
    float current_a_rms;     // A相电流有效值
    float current_b_rms;     // B相电流有效值
    float current_c_rms;     // C相电流有效值
    float unbalance_a;       // A相不平衡度
    float unbalance_b;       // B相不平衡度
    float unbalance_c;       // C相不平衡度
} Motor_Params_t;



/* ADC管理结构体 */
typedef struct {
    /* 原始数据 */
    uint16_t ADC_value[ADC_REGULAR_CHANNEL_NUM];// ADC普通通道原始值
    uint16_t adc_raw[ADC_REGULAR_CHANNEL_NUM];  // ADC普通原始值+偏置
    uint16_t adc_preempt[ADC_PREEMPT_CHANNEL_NUM];  // ADC抢占通道原始值
    
    /* 低通滤波数据 */
    float voltage_filtered[4];               // 电压IIR滤波值

    /* 校准值 */
    float uv_offset;         // UV线电压偏置校准
    float uw_offset;         // UW线电压偏置校准
    float current_u_offset;  // U相电流偏置校准
    float current_v_offset;  // V相电流偏置校准
    float current_w_offset;  // W相电流偏置校准
    
    /* 温度计算负载分散 */
    uint8_t temp_calc_index; // 当前计算的温度传感器索引(0-7)

    /* 状态标志 */
    struct {
        uint8_t adc_enable : 1;       // ADC采集使能标志
        uint8_t dma_complete : 1;     // DMA传输完成标志
        uint8_t calc_complete : 1;    // ADC计算完成标志
        uint8_t reserved : 5;         // 保留位
    } flags;
    
    /* 函数指针 */
    void (*Init)(void);                    // 初始化函数
    void (*Process)(void);                 // 数据处理函数
    float (*GetPT100Temp)(uint16_t);       // PT100温度计算函数
    float (*GetPT1000Temp)(uint16_t);      // PT1000温度计算函数
    
    /* 结果指针 */
    ADC_Result_t *result;                  // 指向结果结构体
} ADC_Manager_t;

/* ADC管理器初始化宏定义 */
#define ADC_MANAGER_INIT { \
    .ADC_value = {0}, \
    .adc_raw = {0}, \
    .adc_preempt = {0}, \
    .voltage_filtered = {0}, \
    .uv_offset = 8.0f, \
    .uw_offset = 8.0f, \
    .current_u_offset = 0, \
    .current_v_offset = 0, \
    .current_w_offset = 0, \
    .temp_calc_index = 0, \
    .flags = {0}, \
    .Init = ADC_Drive_Init, \
    .Process = ADC_Process_500us, \
    .GetPT100Temp = ADC_Get_PT100_Temp, \
    .GetPT1000Temp = ADC_Get_PT1000_Temp, \
    .result = &gADC_Result \
}

/* 外部变量声明 */
extern ADC_Manager_t gADC_Manager;
extern ADC_Result_t gADC_Result;

/* 函数声明 */
void ADC_Drive_Init(void);
void ADC_Process_500us(void);

/* ADC数据获取函数声明 */
float Get_Motor_Current(void);            /* 获取电机电流 */
float Get_Bus_Voltage(void);              /* 获取母线电压 */
float Get_Bus_Current(void);              /* 获取母线电流 */
float Get_Motor_Temperature(void);        /* 获取电机温度 */
float Get_Driver_Temperature(void);       /* 获取驱动器温度 */
float Get_Capacitor_Temperature(void);    /* 获取电容温度 */
float Calculate_Current_Power(void);      /* 计算当前功率 */
float Get_Q_Current(void);                /* 获取Q轴电流 */
float Get_Motor_Torque(void);             /* 获取电机转矩 */
float Get_Motor_Speed(void);              /* 获取电机转速 */
float Get_V28_Main(void);                 /* 获取28V主电源电压 */
float Get_V28_Backup(void);               /* 获取28V备用电源电压 */
float Get_Controller_Temperature(void);   /* 获取控制器温度 */
check_t Check_Phase_Status(void);         /* 检查相线状态 */
void ADC_DMA_Complete_Callback(void);     /* ADC DMA传输完成回调 */
float ADC_Get_PT100_Temp(uint16_t adc_value);  /* 获取PT100温度 */
float ADC_Get_PT1000_Temp(uint16_t adc_value); /* 获取PT1000温度 */


void ADC_Enable_Flag(void);
void ADC_Disable_Flag(void);
uint8_t ADC_Get_Enable_Flag(void);


#endif
