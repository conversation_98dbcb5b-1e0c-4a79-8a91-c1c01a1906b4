/********************************************
  * @file     ADC_Drive.c
  * @brief    ADC驱动实现文件
  * <AUTHOR>
  * @date     2024-01-06
  * @version  V1.0.0
  ******************************************/

#include "Sensor_Drive.h"
#include <string.h>
#include "arm_math.h"
#include "SysCtl_GlobalVar.h"
#include "at32a423_wk_config.h"
#include "sysTypeDef.h"
#include "Sys_Protect.h"          // 添加保护系统头文件

// 获取三相电流（滤波后)
float Sensor_Drive_Get_U_Current(void){
    return SynMotorVc.Isa_flt;}
float Sensor_Drive_Get_V_Current(void){
    return SynMotorVc.Isb_flt;}
float Sensor_Drive_Get_W_Current(void){
    return SynMotorVc.Isc_flt;}

/*********************全局变量定义 START**********************/
/* ADC通道序列配置表 - 序列号对应的实际通道号 */
const uint8_t ADC_CHANNEL_MAP[ADC_REGULAR_CHANNEL_NUM] = {
    ADC_CH_TEMP_CAP1        ,        // 序列0  -> 通道0
    ADC_CH_UV               ,        // 序列1  -> 通道1
    ADC_CH_UW               ,        // 序列2  -> 通道2
    ADC_CH_TEMP_OLL         ,        // 序列3  -> 通道3
    ADC_CH_TEMP_CAP2        ,        // 序列4  -> 通道4
    ADC_CH_TEMP_MOT1        ,        // 序列5  -> 通道5
    ADC_CH_TEMP_MOT2        ,        // 序列6  -> 通道6
    ADC_CH_700V             ,        // 序列7  -> 通道7
    ADC_CH_TEMP_PT100_6     ,        // 序列8  -> 通道21
    ADC_CH_TEMP_PT1000_7    ,        // 序列9  -> 通道22
    ADC_CH_TEMP_BMF_U       ,       // 序列10 -> 通道23
    ADC_CH_TEMP_BMF_V       ,       // 序列11 -> 通道24
    ADC_CH_TEMP_BMF_W       ,       // 序列12 -> 通道25
    ADC_CH_VCC28V           ,       // 序列13 -> 通道26
    ADC_CH_VCC28V2                  // 序列14 -> 通道20
};

/* 全局变量定义 */
ADC_Result_t gADC_Result = {0};
Motor_Params_t gMotor_Params = {0};
ADC_Manager_t gADC_Manager = ADC_MANAGER_INIT;

/*********************全局变量定义 END**********************/

/*********************滤波器配置 START**********************/
/* 滑动滤波器通道索引定义 */
typedef enum {
    FILTER_TEMP_MOT1   = 0,       // 滤波器0 电机温度1滤波器
    FILTER_TEMP_MOT2   = 1,       // 滤波器1 电机温度2滤波器
    FILTER_TEMP_CAP1   = 2,       // 滤波器2 电容温度1滤波器
    FILTER_TEMP_CAP2   = 3,       // 滤波器3 电容温度2滤波器
    FILTER_TEMP_BMF_U  = 4,       // 滤波器4 U相温度滤波器
    FILTER_TEMP_BMF_V  = 5,       // 滤波器5 V相温度滤波器
    FILTER_TEMP_BMF_W  = 6,       // 滤波器6 W相温度滤波器
    FILTER_BUS_VOLTAGE = 7,       // 滤波器7 母线电压滤波器
    FILTER_28V_VOLTAGE = 8,       // 滤波器8 28V电压滤波器
    FILTER_TEMP_PT100_6 = 9,      // 滤波器9 PT100温度滤波器
    FILTER_TEMP_PT1000_7 = 10,    // 滤波器10 PT1000温度滤波器
    FILTER_TEMP_OLL     = 11,     // 滤波器11 油温温度滤波器
    FILTER_VCC28V2_VOLTAGE = 12,  // 滤波器12 28V电压2滤波器
    FILTER_CHANNEL_NUM = 13       // 滤波器总数
} FilterChannel_t;

/* 滤波参数定义 */
#define IIR_ALPHA          0.8f    // IIR滤波系数(电压)

/* 滤波缓存和实例定义 */
static uint16_t filter_buffers[FILTER_CHANNEL_NUM][FILTER_LEN];  // 所有滑动滤波器的缓存
static MA_Filter_t filters[FILTER_CHANNEL_NUM];                  // 所有滑动滤波器实例
/*********************滑动滤波器配置 END**********************/

/*********************温度计算负载分散配置 START**********************/
/* 温度计算状态机枚举 */
typedef enum {
    TEMP_CALC_MOT1 = 0,      // 电机温度1
    TEMP_CALC_MOT2 = 1,      // 电机温度2  
    TEMP_CALC_CAP1 = 2,      // 电容温度1
    TEMP_CALC_CAP2 = 3,      // 电容温度2
    TEMP_CALC_OLL = 4,       // 油温
    TEMP_CALC_BMF_U = 5,     // U相温度
    TEMP_CALC_BMF_V = 6,     // V相温度
    TEMP_CALC_BMF_W = 7,     // W相温度
    TEMP_CALC_COUNT = 8      // 总数
} TempCalcState_t;

/*********************温度计算负载分散配置 END**********************/

/*********************ADC驱动核心功能 START**********************/
/**
  * @brief  ADC驱动初始化
  * @param  无
  * @retval 无
  */
void ADC_Drive_Init(void)
{
    // 清空数据结构
    memset(&gADC_Manager, 0, sizeof(ADC_Manager_t));
    memset(&gADC_Result, 0, sizeof(ADC_Result_t));
    
    // 初始化所有滤波器
    for(uint8_t i = 0; i < FILTER_CHANNEL_NUM; i++) {
        MA_Filter_Init(&filters[i], filter_buffers[i], FILTER_LEN);
    }

    gADC_Manager.flags.adc_enable = 1;

    // TODO: 计算各种偏置值
    gADC_Manager.uv_offset = 0;
    gADC_Manager.uw_offset = 0;
    gADC_Manager.current_u_offset = 0;
    gADC_Manager.current_v_offset = 0;
    gADC_Manager.current_w_offset = 0;
}

/**
  * @brief  ADC DMA传输完成中断回调
  * @param  无
  * @retval 无
  */
void ADC_DMA_Complete_Callback(void)
{
    gADC_Manager.flags.dma_complete = 1;  // 设置DMA完成标志
}

/**
 * @brief  高效的ADC校准算法 (优化版)
 * @param  vnt: ADC原始采样值
 * @retval 校准后的值，抗饱和设计
 */
static inline uint16_t ADC_Calibrate_Value(uint16_t vnt) 
{
    // 公共部分：F2 + (F2>>9)
    uint16_t result = vnt + (vnt >> 9);
    // 抗饱和处理
    return (result > 4095) ? 4095 : result;
}

/**
  * @brief  ADC数据处理(500us调用) - 负载分散优化版
  * @param  无
  * @retval 无
  * @note   温度计算采用轮流方式，每个传感器4ms更新一次；电压电流每次都更新
  */
void ADC_Process_500us(void)
{
    // 检查DMA是否完成
    if(!gADC_Manager.flags.dma_complete) {
        return;
    }
    
    uint16_t adc_value = 0;
    float voltage_raw = 0;
    
    // 直接复制ADC原始数据到adc_raw数组，不进行偏置校准
    memcpy(gADC_Manager.adc_raw, gADC_Manager.ADC_value, ADC_REGULAR_CHANNEL_NUM * 2);

    // ========== 高频数据处理：电压和电流（每次都更新）==========
    
    // 序列1: UV线电压 - ADC_CH_UV (通道1) - PA1
    voltage_raw = (float)gADC_Manager.adc_raw[ADC_SEQ_UV] * ADC_LINE_COEFF - ADC_UV_OFFSET;
    gADC_Result.uv_voltage = IIR_ALPHA * gADC_Manager.voltage_filtered[1] + (1.0f - IIR_ALPHA) * voltage_raw;
    gADC_Manager.voltage_filtered[1] = gADC_Result.uv_voltage;
    
    // 序列2: UW线电压 - ADC_CH_UW (通道2) - PA2
    voltage_raw = (float)gADC_Manager.adc_raw[ADC_SEQ_UW] * ADC_LINE_COEFF - ADC_UW_OFFSET;
    gADC_Result.uw_voltage = IIR_ALPHA * gADC_Manager.voltage_filtered[2] + (1.0f - IIR_ALPHA) * voltage_raw;
    gADC_Manager.voltage_filtered[2] = gADC_Result.uw_voltage;
    
    // 序列7: 母线电压 - ADC_CH_700V (通道7) - PA7
    adc_value = MA_Filter_Update(&filters[FILTER_BUS_VOLTAGE], gADC_Manager.adc_raw[ADC_SEQ_700V]);
    gADC_Result.bus_voltage = (float)(adc_value) * ADC_BUS_COEFF;

    // 序列13: 28V电压 - ADC_CH_VCC28V (通道26) - PB15
    adc_value = MA_Filter_Update(&filters[FILTER_28V_VOLTAGE], gADC_Manager.adc_raw[ADC_SEQ_VCC28V]);
    gADC_Result.v28_voltage = (float)(adc_value) * ADC_28V_COEFF;

    // 序列14: 28V电压2 - ADC_CH_VCC28V2 (通道20) - PB2
    adc_value = MA_Filter_Update(&filters[FILTER_VCC28V2_VOLTAGE], gADC_Manager.adc_raw[ADC_SEQ_VCC28V2]);
    gADC_Result.v28v2_voltage = (float)(adc_value) * ADC_28V_COEFF;

    // 其他数据（非ADC普通通道）
    gADC_Result.current_u = Sensor_Drive_Get_U_Current();
    gADC_Result.current_v = Sensor_Drive_Get_V_Current();
    gADC_Result.current_w = Sensor_Drive_Get_W_Current();

    // ========== 低频数据处理：温度传感器（轮流计算）==========
    
    // 根据当前状态机索引计算对应的温度传感器
    switch(gADC_Manager.temp_calc_index) {
        case TEMP_CALC_MOT1:  // 电机温度1
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_MOT1], gADC_Manager.adc_raw[ADC_SEQ_TEMP_MOT1]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 0);
            gADC_Result.temp_mot1 = ADC_Get_PT100_Temp(adc_value);
            break;
            
        case TEMP_CALC_MOT2:  // 电机温度2
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_MOT2], gADC_Manager.adc_raw[ADC_SEQ_TEMP_MOT2]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 1);
            gADC_Result.temp_mot2 = ADC_Get_PT100_Temp(adc_value);
            break;
            
        case TEMP_CALC_CAP1:  // 电容温度1
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_CAP1], gADC_Manager.adc_raw[ADC_SEQ_TEMP_CAP1]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 2);
            gADC_Result.temp_cap1 = ADC_Get_PT100_Temp(adc_value);
            break;
            
        case TEMP_CALC_CAP2:  // 电容温度2
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_CAP2], gADC_Manager.adc_raw[ADC_SEQ_TEMP_CAP2]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 3);
            gADC_Result.temp_cap2 = ADC_Get_PT100_Temp(adc_value);
            break;
            
        case TEMP_CALC_OLL:   // 油温
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_OLL], gADC_Manager.adc_raw[ADC_SEQ_TEMP_OLL]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 4);
            gADC_Result.temp_oll = ADC_Get_PT100_Temp(adc_value);
            break;
            
        case TEMP_CALC_BMF_U: // U相温度
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_BMF_U], gADC_Manager.adc_raw[ADC_SEQ_TEMP_BMF_U]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 5);
            gADC_Result.temp_bmf_u = ADC_Get_PT1000_Temp(adc_value);
            break;
            
        case TEMP_CALC_BMF_V: // V相温度
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_BMF_V], gADC_Manager.adc_raw[ADC_SEQ_TEMP_BMF_V]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 6);
            gADC_Result.temp_bmf_v = ADC_Get_PT1000_Temp(adc_value);
            break;
            
        case TEMP_CALC_BMF_W: // W相温度
            adc_value = MA_Filter_Update(&filters[FILTER_TEMP_BMF_W], gADC_Manager.adc_raw[ADC_SEQ_TEMP_BMF_W]);
            adc_value = Check_Temp_Sensor_Valid(adc_value, 7);
            gADC_Result.temp_bmf_w = ADC_Get_PT1000_Temp(adc_value);
            break;
            
        default:
            // 异常情况，重置索引
            gADC_Manager.temp_calc_index = 0;
            break;
    }
    
    // 更新温度计算状态机索引（循环0-7）
    gADC_Manager.temp_calc_index++;
    if(gADC_Manager.temp_calc_index >= TEMP_CALC_COUNT) {
        gADC_Manager.temp_calc_index = 0;
        // 序列8&9: PT100温度6和PT1000温度7暂时设为0（后续可启用）
        gADC_Result.temp_PT100_6 = 0.0f;
        gADC_Result.temp_PT1000_7 = 0.0f;
    }

    // 设置标志位
    gADC_Manager.flags.calc_complete = 1;  // 设置计算完成标志
    gADC_Manager.flags.dma_complete = 0;   // 清除DMA完成标志

    //Protection_Process_500us();
}
/*********************ADC驱动核心功能 END**********************/

/*********************ADC控制函数 START**********************/
/**
 * @brief  使能ADC采集
 * @param  None
 * @retval None
 */
void ADC_Enable_Flag(void)
{
    gADC_Manager.flags.adc_enable = 1;
}

/**
 * @brief  禁用ADC采集
 * @param  None
 * @retval None
 */
void ADC_Disable_Flag(void)
{
    gADC_Manager.flags.adc_enable = 0;
}

/**
 * @brief  获取ADC使能状态
 * @param  None
 * @retval 1: 使能, 0: 禁用
 */
uint8_t ADC_Get_Enable_Flag(void)
{
    return gADC_Manager.flags.adc_enable;
}
/*********************ADC控制函数 END**********************/

/*********************数据获取函数 START**********************/
/**
 * @brief  获取电机电流
 * @note   返回三相电流的最大值
 * @retval float: 电流值(A)
 */
float Get_Motor_Current(void)
{
    float max_current = gADC_Result.current_u;
    if(gADC_Result.current_v > max_current) max_current = gADC_Result.current_v;
    if(gADC_Result.current_w > max_current) max_current = gADC_Result.current_w;
    return max_current;
}

/**
 * @brief  获取母线电压
 * @retval float: 电压值(V)
 */
float Get_Bus_Voltage(void)
{
    return gADC_Result.bus_voltage;
}

/**
 * @brief  获取电机温度（位操作优化）
 * @retval float: 温度值(℃)
 */
float Get_Motor_Temperature(void)
{
    // 提取电机温度1和温度2的无效标志（位0和位1）
    // 0b11 = 3，对应提取低两位
    uint8_t motor_flags = gADC_Result.temp_invalid_flags & 0x03;
    
    // 判断四种情况:
    // 0b00 (0): 两个都有效
    // 0b01 (1): 只有温度1无效
    // 0b10 (2): 只有温度2无效
    // 0b11 (3): 两个都无效
    
    switch(motor_flags) {
        case 0: // 两个都有效
            return (gADC_Result.temp_mot1 > gADC_Result.temp_mot2) ? 
                   gADC_Result.temp_mot1 : gADC_Result.temp_mot2;
            
        case 1: // 只有温度1无效
            return gADC_Result.temp_mot2;
            
        case 2: // 只有温度2无效
            return gADC_Result.temp_mot1;
            
        case 3: // 两个都无效
            return -100.0f;
        default:
            return -100.0f;
    }
}

/**
 * @brief  获取驱动器温度（高性能无分支版本）
 * @note   返回三个功率模块温度的最大值
 * @retval float: 温度值(℃)
 */
float Get_Driver_Temperature(void)
{
    // 提取驱动相关标志位(位5-7)并右移5位
    const uint8_t flags = (gADC_Result.temp_invalid_flags >> 5) & 0x07;
    
    // 所有传感器无效
    if(flags == 0x07) {
        return -100.0f;
    }
    
    // 指针访问全局变量
    const float* temps = &gADC_Result.temp_bmf_u;
    
    // 将无效传感器的温度设为极低值
    const float u = (flags & 0x01) ? -100.0f : temps[0];
    const float v = (flags & 0x02) ? -100.0f : temps[1];
    const float w = (flags & 0x04) ? -100.0f : temps[2];
    
    // 最大值计算
    float max_uv = (u > v) ? u : v;
    float max_temp = (max_uv > w) ? max_uv : w;
    // 防御性检查
    return max_temp;
}

/**
 * @brief  获取电容温度（高性能优化版）
 * @note   返回两个电容温度传感器的最大值
 * @retval float: 温度值(℃)
 */
float Get_Capacitor_Temperature(void)
{
    // 提取电容温度1和温度2的无效标志（位2和位3）
    // 右移2位后取低2位: 0b11 = 3
    const uint8_t cap_flags = (gADC_Result.temp_invalid_flags >> 2) & 0x03;
    
    // 快速处理四种情况:
    // 0b00 (0): 两个都有效
    // 0b01 (1): 只有温度1无效
    // 0b10 (2): 只有温度2无效
    // 0b11 (3): 两个都无效
    
    switch(cap_flags) {
        case 0: // 两个都有效
            return (gADC_Result.temp_cap1 > gADC_Result.temp_cap2) ? 
                   gADC_Result.temp_cap1 : gADC_Result.temp_cap2;
            
        case 1: // 只有温度1无效
            return gADC_Result.temp_cap2;
            
        case 2: // 只有温度2无效
            return gADC_Result.temp_cap1;
            
        case 3: // 两个都无效
            return -100.0f;
        default:
            return -100.0f;
    }
}

/**
 * @brief  获取油温
 * @retval 温度值(℃)
 */
float Get_Oil_Temperature(void)
{
    const uint8_t oil_flag = (gADC_Result.temp_invalid_flags >> 4) & 0x01;

    return oil_flag ? -100.0f : gADC_Result.temp_oll;  // 标志位为1时无效，返回-100.0f
}

/**
 * @brief  获取电机转矩
 * @retval 转矩值(N·m)
 */
float Get_Motor_Torque(void)
{
    // 实际实现，替换临时返回值
    return 0.0f;  // 临时返回0，后续可根据实际需求实现
}

/**
 * @brief  获取电机转速
 * @retval 转速值(rpm)
 */
float Get_Motor_Speed(void)
{
    //返回矢量控制中的已滤波速度反馈值
    return SynMotorVc.speedback_flt;
}

/**
 * @brief  获取28V主电源电压
 * @retval 电压值(V)
 */
float Get_V28_Main(void)
{
    return gADC_Result.v28_voltage;
}

/**
 * @brief  获取28V备用电源电压
 * @retval 电压值(V)
 */
float Get_V28_Backup(void)
{
    // 实际实现，替换临时返回值
    return 28.0f;  // 临时返回28V，后续可根据实际需求实现
}

/**
 * @brief  获取母线电流（基于功率平衡原理）
 * @note   通过矢量控制dq轴计算母线电流，Id=0简化计算，功率因数0.96修正系数
 * @retval 电流值(A)
 */
float Get_Bus_Current(void)
{
    // 从矢量控制结构体获取q轴参数
    float Vq = SynMotorVc.Vsq_ref;           // q轴电压参考值
    float Iq = SynMotorVc.isq;               // q轴电流实际值
    float Vdc = gADC_Result.bus_voltage;     // 直流母线电压
    // 预计算常数 3/2 * 1/η，减少运行时计算
    //const float K_FACTOR = 1.5625f;    // 3/2 * 1/0.96 = 1.5625
    // 快速阈值检查
    if(Vdc < 10.0f) {
        return 0.0f;
    }
    // 计算倒数
    float Vdc_inv = 1.0f / Vdc;   
    float Idc = 1.5625f * Vq * Iq * Vdc_inv;
    // 内联
    return (Idc < 0.0f) ? -Idc : Idc;
}

/**
 * @brief  获取Q轴电流
 * @retval 电流值(A)
 */
float Get_Q_Current(void)
{
    return SynMotorVc.isq;
}


/**
 * @brief  获取控制器温度
 * @note   返回三个驱动器温度的中位值
 * @retval 温度值(℃)
 */
float Get_Controller_Temperature(void)
{
    float a = gADC_Result.temp_bmf_u;
    float b = gADC_Result.temp_bmf_v;
    float c = gADC_Result.temp_bmf_w;
    
    // 使用条件操作符
    float min_ab = (a < b) ? a : b;
    float max_ab = (a > b) ? a : b;
    float min_c_maxab = (c < max_ab) ? c : max_ab;
    
    // 中位值 = max(min(a,b), min(max(a,b),c))
    return (min_ab > min_c_maxab) ? min_ab : min_c_maxab;
}

/*********************数据获取函数 END**********************/

/*********************状态检查与计算函数 START**********************/
/**
 * @brief  计算当前功率（基于功率平衡原理）
 * @note   采用Id=0控制策略下的功率计算: P = (3/2) * Vq * Iq / η
 * @retval float: 功率值(W)
 */
float Calculate_Current_Power(void)
{
    // 从矢量控制结构体获取q轴参数
//    float Vq = SynMotorVc.Vsq_ref;     // q轴电压参考值
//    float Iq = SynMotorVc.isq;         // q轴电流实际值
    
    // 使用Id=0控制策略，功率计算仅与q轴相关
    // P = (3/2) * (Vq * Iq) / η 3/2/0.98=1.8367
    //return 1.8367* (Vq * Iq);
	return 0.0f;
}

/*********************状态检查与计算函数 END**********************/

