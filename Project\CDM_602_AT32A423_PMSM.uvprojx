<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target_1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6220000::V6.22::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>AT32A423VCT7</Device>
          <Vendor>ArteryTek</Vendor>
          <PackID>ArteryTek.AT32A423_DFP.2.0.3</PackID>
          <Cpu>IRAM(0x20000000,0xC000) IROM(0x08000000,0x40000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0AT32A423_256 -********** -FL040000 -FP0($$Device:AT32A423VCT7$Flash\AT32A423_256.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:AT32A423VCT7$Device\Include\at32a423.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:AT32A423VCT7$SVD\AT32A423xx_v2.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>Project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0xc000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0xc000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-Wno-deprecated-non-prototype</MiscControls>
              <Define>AT32A423VCT7,USE_STDPERIPH_DRIVER,AT_START_A423_V1,ARM_MATH_LOOP0UNROLL,ARM_MATH_CM4,ARM_MATH_MATRIX_CHECK,ARM_MATH_ROUNDING,</Define>
              <Undefine></Undefine>
              <IncludePath>..\Libraries\drivers\inc;..\System;..\USE_Driver;..\User;..\Libraries\cmsis\cm4\core_support;..\Libraries\cmsis\cm4\device_support;..\Libraries\usb_drivers\inc;..\Libraries\AnoPTv8;..\Motor</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>cmsis</GroupName>
          <Files>
            <File>
              <FileName>arm_cortexM4lf_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\Libraries\dsp\arm_cortexM4lf_math.lib</FilePath>
            </File>
            <File>
              <FileName>usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\usb_drivers\src\usbd_core.c</FilePath>
            </File>
            <File>
              <FileName>usbd_sdr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\usb_drivers\src\usbd_sdr.c</FilePath>
            </File>
            <File>
              <FileName>at32a423.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\cmsis\cm4\device_support\at32a423.h</FilePath>
            </File>
            <File>
              <FileName>usbd_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\usb_drivers\src\usbd_int.c</FilePath>
            </File>
            <File>
              <FileName>usb_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\usb_drivers\src\usb_core.c</FilePath>
            </File>
            <File>
              <FileName>system_at32a423.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\cmsis\cm4\device_support\system_at32a423.c</FilePath>
            </File>
            <File>
              <FileName>startup_at32a423.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Libraries\cmsis\cm4\device_support\startup\mdk\startup_at32a423.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>firmware</GroupName>
          <Files>
            <File>
              <FileName>at32a423_acc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_acc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_adc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_can.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_crc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_crm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_crm.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_dac.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_debug.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_dma.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_ertc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_ertc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_exint.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_exint.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_flash.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_gpio.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_i2c.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_misc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_pwc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_pwc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_scfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_scfg.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_spi.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_tmr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_tmr.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_usart.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_usb.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_wdt.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_wwdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_wwdt.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_xmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\drivers\src\at32a423_xmc.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_acc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_acc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_adc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_can.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_can.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_crc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_crc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_crm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_crm.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_dac.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_dac.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_debug.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_def.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_def.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_dma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_dma.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_ertc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_ertc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_exint.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_exint.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_flash.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_gpio.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_i2c.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_i2c.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_misc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_misc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_pwc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_pwc.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_scfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_scfg.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_spi.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_tmr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_tmr.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_usart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_usart.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_usb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_usb.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_wdt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_wdt.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_wwdt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_wwdt.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_xmc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\drivers\inc\at32a423_xmc.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>AnoPTv8</GroupName>
          <Files>
            <File>
              <FileName>AnoPTv8.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Cmd.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Cmd.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8FrameFactory.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8FrameFactory.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Par.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Par.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Run.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\AnoPTv8Run.h</FilePath>
            </File>
            <File>
              <FileName>HWInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\HWInterface.c</FilePath>
            </File>
            <File>
              <FileName>HWInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\HWInterface.h</FilePath>
            </File>
            <File>
              <FileName>MotorCmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorCmd.c</FilePath>
            </File>
            <File>
              <FileName>MotorCmd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorCmd.h</FilePath>
            </File>
            <File>
              <FileName>MotorData.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorData.c</FilePath>
            </File>
            <File>
              <FileName>MotorData.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorData.h</FilePath>
            </File>
            <File>
              <FileName>MotorParams.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorParams.c</FilePath>
            </File>
            <File>
              <FileName>MotorParams.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Libraries\AnoPTv8\MotorParams.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Motor</GroupName>
          <Files>
            <File>
              <FileName>SysCtl_SysMoore.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_SysMoore.h</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\delay.c</FilePath>
            </File>
            <File>
              <FileName>delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\delay.h</FilePath>
            </File>
            <File>
              <FileName>MathBasic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\MathBasic.c</FilePath>
            </File>
            <File>
              <FileName>MathBasic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\MathBasic.h</FilePath>
            </File>
            <File>
              <FileName>Motor_VectorControl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\Motor_VectorControl.c</FilePath>
            </File>
            <File>
              <FileName>Motor_VectorControl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\Motor_VectorControl.h</FilePath>
            </File>
            <File>
              <FileName>Sys_Isr_Controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\Sys_Isr_Controller.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AllHeaders.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_AllHeaders.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AnalogProcess.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\SysCtl_AnalogProcess.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AnalogProcess.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_AnalogProcess.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_GlobalVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\SysCtl_GlobalVar.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_GlobalVar.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_GlobalVar.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_IoAd2s1210.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\SysCtl_IoAd2s1210.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_IoAd2s1210.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_IoAd2s1210.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_RotorGet.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\SysCtl_RotorGet.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_RotorGet.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_RotorGet.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_SysMoore.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\SysCtl_SysMoore.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_ConstDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_ConstDef.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_CsvParamDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Motor\SysCtl_CsvParamDef.h</FilePath>
            </File>
            <File>
              <FileName>Algorithm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Motor\Algorithm.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>System</GroupName>
          <Files>
            <File>
              <FileName>Sys_BaseValue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\Sys_BaseValue.c</FilePath>
            </File>
            <File>
              <FileName>Sys_BaseValue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\Sys_BaseValue.h</FilePath>
            </File>
            <File>
              <FileName>Sys_TimerEvent.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\Sys_TimerEvent.c</FilePath>
            </File>
            <File>
              <FileName>Sys_TimerEvent.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\Sys_TimerEvent.h</FilePath>
            </File>
            <File>
              <FileName>start_self_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\start_self_test.c</FilePath>
            </File>
            <File>
              <FileName>start_self_test.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\start_self_test.h</FilePath>
            </File>
            <File>
              <FileName>sys_SpiFlash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\sys_SpiFlash.c</FilePath>
            </File>
            <File>
              <FileName>sys_SpiFlash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\sys_SpiFlash.h</FilePath>
            </File>
            <File>
              <FileName>Sys_Protect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\Sys_Protect.c</FilePath>
            </File>
            <File>
              <FileName>Sys_Protect.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\Sys_Protect.h</FilePath>
            </File>
            <File>
              <FileName>Sys_Can.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\Sys_Can.h</FilePath>
            </File>
            <File>
              <FileName>Sys_Can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\Sys_Can.c</FilePath>
            </File>
            <File>
              <FileName>Sic_SelfTest.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\System\Sic_SelfTest.c</FilePath>
            </File>
            <File>
              <FileName>Sic_SelfTest.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\Sic_SelfTest.h</FilePath>
            </File>
            <File>
              <FileName>wk_system.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\wk_system.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USE_DRIVE</GroupName>
          <Files>
            <File>
              <FileName>cdc_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\cdc_class.c</FilePath>
            </File>
            <File>
              <FileName>cdc_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\cdc_desc.c</FilePath>
            </File>
            <File>
              <FileName>usb_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\usb_app.c</FilePath>
            </File>
            <File>
              <FileName>cdc_class.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\cdc_class.h</FilePath>
            </File>
            <File>
              <FileName>cdc_desc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\cdc_desc.h</FilePath>
            </File>
            <File>
              <FileName>usb_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\usb_app.h</FilePath>
            </File>
            <File>
              <FileName>usb_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\usb_conf.h</FilePath>
            </File>
            <File>
              <FileName>LBQ_design.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\LBQ_design.c</FilePath>
            </File>
            <File>
              <FileName>LBQ_design.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\LBQ_design.h</FilePath>
            </File>
            <File>
              <FileName>SPI_w25q256.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\SPI_w25q256.c</FilePath>
            </File>
            <File>
              <FileName>SPI_w25q256.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\SPI_w25q256.h</FilePath>
            </File>
            <File>
              <FileName>Sensor_Drive.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\Sensor_Drive.c</FilePath>
            </File>
            <File>
              <FileName>Sensor_Drive.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\Sensor_Drive.h</FilePath>
            </File>
            <File>
              <FileName>ad2s1212_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\ad2s1212_spi.c</FilePath>
            </File>
            <File>
              <FileName>ad2s1212_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\ad2s1212_spi.h</FilePath>
            </File>
            <File>
              <FileName>ENC_Speed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\ENC_Speed.c</FilePath>
            </File>
            <File>
              <FileName>ENC_Speed.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\ENC_Speed.h</FilePath>
            </File>
            <File>
              <FileName>RS422_CU.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USE_Driver\RS422_CU.c</FilePath>
            </File>
            <File>
              <FileName>RS422_CU.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USE_Driver\RS422_CU.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>at32a423_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\at32a423_int.c</FilePath>
            </File>
            <File>
              <FileName>at32a423_wk_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\at32a423_wk_config.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>Overall_Init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Overall_Init.c</FilePath>
            </File>
            <File>
              <FileName>main.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\main.h</FilePath>
            </File>
            <File>
              <FileName>SysFSM.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\SysFSM.c</FilePath>
            </File>
            <File>
              <FileName>SysFSM.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\SysFSM.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\at32a423_conf.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_int.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\at32a423_int.h</FilePath>
            </File>
            <File>
              <FileName>at32a423_wk_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\at32a423_wk_config.h</FilePath>
            </File>
            <File>
              <FileName>Overall_Init.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\Overall_Init.h</FilePath>
            </File>
            <File>
              <FileName>sysTypeDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\sysTypeDef.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>Project</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
