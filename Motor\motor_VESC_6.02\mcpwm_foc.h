/*
	Copyright 2016 - 2020 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
    */

/*
 * FOC电机控制PWM头文件 (FOC Motor Control PWM Header)
 *
 * 本文件定义了VESC的磁场定向控制(Field Oriented Control, FOC)算法的接口。
 * FOC是一种高性能的电机控制技术，通过将三相交流电机的控制转换为直流电机
 * 的控制方式，实现高效率、低噪音、高精度的电机控制。
 *
 * FOC算法核心原理：
 * 1. Clarke变换：将三相静止坐标系(abc)转换为两相静止坐标系(αβ)
 *    [α] = [1    -1/2   -1/2 ] [a]
 *    [β]   [0   √3/2   -√3/2] [b]
 *                              [c]
 *
 * 2. Park变换：将两相静止坐标系(αβ)转换为两相旋转坐标系(dq)
 *    [d] = [ cos(θ)  sin(θ)] [α]
 *    [q]   [-sin(θ)  cos(θ)] [β]
 *
 * 3. 在dq坐标系中进行PI控制，实现解耦控制
 * 4. 反Park变换和反Clarke变换，生成三相PWM信号
 *
 * 主要功能模块：
 * - 电流环控制：高频PI控制器，实现精确的电流控制
 * - 速度环控制：中频PI控制器，实现速度闭环控制
 * - 位置环控制：低频PI控制器，实现位置闭环控制
 * - 无传感器控制：基于反电动势观测器的转子位置估算
 * - 高频注入(HFI)：低速无传感器控制技术
 * - 空间矢量调制(SVPWM)：高效的PWM生成算法
 * - 弱磁控制：扩展电机高速运行范围
 */

#ifndef MCPWM_FOC_H_
#define MCPWM_FOC_H_

#include "conf_general.h"
#include "datatypes.h"
#include <stdbool.h>

/*
 * ============================================================================
 * 系统初始化和配置函数 (System Initialization & Configuration Functions)
 * ============================================================================
 */

/**
 * FOC系统初始化
 * 初始化FOC控制系统，包括硬件定时器、ADC、中断等
 * @param conf_m1 电机1配置参数
 * @param conf_m2 电机2配置参数(双电机系统)
 */
void mcpwm_foc_init(mc_configuration *conf_m1, mc_configuration *conf_m2);

/**
 * FOC系统反初始化
 * 停止所有FOC相关的硬件和软件资源
 */
void mcpwm_foc_deinit(void);

/**
 * 检查FOC初始化是否完成
 * @return true=初始化完成, false=初始化进行中
 */
bool mcpwm_foc_init_done(void);

/**
 * 设置FOC配置参数
 * 动态更新FOC控制参数
 * @param configuration 新的配置参数
 */
void mcpwm_foc_set_configuration(mc_configuration *configuration);

/**
 * 获取电机状态
 * @return 当前电机状态(运行/停止/故障等)
 */
mc_state mcpwm_foc_get_state(void);

/**
 * 获取当前控制模式
 * @return 控制模式(电流/速度/位置/占空比)
 */
mc_control_mode mcpwm_foc_control_mode(void);

/**
 * 检查直流校准是否完成
 * 直流校准用于消除ADC采样偏移
 * @return true=校准完成, false=校准进行中
 */
bool mcpwm_foc_is_dccal_done(void);

/**
 * 获取当前中断处理的电机编号
 * @return 电机编号(1或2)
 */
int mcpwm_foc_isr_motor(void);

/**
 * 停止PWM输出
 * @param is_second_motor 是否为第二个电机
 */
void mcpwm_foc_stop_pwm(bool is_second_motor);

/*
 * ============================================================================
 * 电机控制命令函数 (Motor Control Command Functions)
 * ============================================================================
 */

/**
 * 设置占空比控制
 * 占空比控制方程：V_avg = duty × V_bus
 * @param dutyCycle 占空比(-1.0到+1.0)
 */
void mcpwm_foc_set_duty(float dutyCycle);

/**
 * 设置占空比控制(无斜坡)
 * 立即设置占空比，不使用斜坡过渡
 * @param dutyCycle 占空比(-1.0到+1.0)
 */
void mcpwm_foc_set_duty_noramp(float dutyCycle);

/**
 * 设置PID速度控制
 * 速度环PID控制方程：u(t) = Kp×e(t) + Ki×∫e(t)dt + Kd×de(t)/dt
 * @param rpm 目标转速(RPM)
 */
void mcpwm_foc_set_pid_speed(float rpm);

/**
 * 设置PID位置控制
 * 位置环PID控制，支持多圈定位
 * @param pos 目标位置(弧度)
 */
void mcpwm_foc_set_pid_pos(float pos);

/**
 * 设置电流控制
 * 直接控制q轴电流，转矩关系：T = Kt × Iq
 * @param current 目标电流(A)
 */
void mcpwm_foc_set_current(float current);

/**
 * 释放电机
 * 停止所有控制输出，让电机自由转动
 */
void mcpwm_foc_release_motor(void);

/**
 * 设置制动电流
 * 再生制动，将动能转换为电能
 * @param current 制动电流(A)
 */
void mcpwm_foc_set_brake_current(float current);

/**
 * 设置手刹电流
 * 静态制动，保持电机位置
 * @param current 手刹电流(A)
 */
void mcpwm_foc_set_handbrake(float current);

/*
 * ============================================================================
 * 开环控制函数 (Open-Loop Control Functions)
 * ============================================================================
 */

/**
 * 设置开环电流控制
 * 不依赖位置传感器的电流控制
 * @param current 电流幅值(A)
 * @param rpm 估算转速(RPM)
 */
void mcpwm_foc_set_openloop_current(float current, float rpm);

/**
 * 设置开环相位控制
 * 直接指定转子电角度
 * @param current 电流幅值(A)
 * @param phase 转子电角度(弧度)
 */
void mcpwm_foc_set_openloop_phase(float current, float phase);

/**
 * 设置开环占空比控制
 * @param dutyCycle 占空比(-1.0到+1.0)
 * @param rpm 估算转速(RPM)
 */
void mcpwm_foc_set_openloop_duty(float dutyCycle, float rpm);

/**
 * 设置开环占空比相位控制
 * @param dutyCycle 占空比(-1.0到+1.0)
 * @param phase 转子电角度(弧度)
 */
void mcpwm_foc_set_openloop_duty_phase(float dutyCycle, float phase);

/**
 * 设置转速计数值
 * @param steps 转速计数值
 * @return 设置后的实际计数值
 */
int mcpwm_foc_set_tachometer_value(int steps);
/*
 * ============================================================================
 * 状态读取函数 (Status Reading Functions)
 * ============================================================================
 */

/**
 * 获取设定的占空比
 * @return 设定占空比值(-1.0到+1.0)
 */
float mcpwm_foc_get_duty_cycle_set(void);

/**
 * 获取当前实际占空比
 * @return 实际占空比值(-1.0到+1.0)
 */
float mcpwm_foc_get_duty_cycle_now(void);

/**
 * 获取PID位置设定值
 * @return 位置设定值(弧度)
 */
float mcpwm_foc_get_pid_pos_set(void);

/**
 * 获取PID位置当前值
 * @return 位置当前值(弧度)
 */
float mcpwm_foc_get_pid_pos_now(void);

/**
 * 获取当前开关频率
 * @return 开关频率(Hz)
 */
float mcpwm_foc_get_switching_frequency_now(void);

/**
 * 获取当前采样频率
 * @return 采样频率(Hz)
 */
float mcpwm_foc_get_sampling_frequency_now(void);

/*
 * ============================================================================
 * 转速测量函数 (Speed Measurement Functions)
 * ============================================================================
 */

/**
 * 获取电机转速
 * 转速计算：RPM = (ω × 60) / (2π)
 * @return 转速(RPM)
 */
float mcpwm_foc_get_rpm(void);

/**
 * 获取电机转速(快速)
 * 使用较少滤波的快速转速估算
 * @return 转速(RPM)
 */
float mcpwm_foc_get_rpm_fast(void);

/**
 * 获取电机转速(更快速)
 * 使用最少滤波的最快转速估算
 * @return 转速(RPM)
 */
float mcpwm_foc_get_rpm_faster(void);

/*
 * ============================================================================
 * 电流测量函数 (Current Measurement Functions)
 * ============================================================================
 */

/**
 * 获取总电机电流(瞬时值)
 * 三相电流矢量和：I_total = √(I_α² + I_β²)
 * @return 总电机电流(A)
 */
float mcpwm_foc_get_tot_current(void);

/**
 * 获取总电机电流(滤波值)
 * @return 滤波后的总电机电流(A)
 */
float mcpwm_foc_get_tot_current_filtered(void);

/**
 * 获取电机电流绝对值
 * @return 电机电流绝对值(A)
 */
float mcpwm_foc_get_abs_motor_current(void);

/**
 * 获取电机电流不平衡度
 * 用于检测三相电流的不平衡情况
 * @return 电流不平衡度
 */
float mcpwm_foc_get_abs_motor_current_unbalance(void);

/**
 * 获取电机电压绝对值
 * @return 电机电压绝对值(V)
 */
float mcpwm_foc_get_abs_motor_voltage(void);

/**
 * 获取电机电流绝对值(滤波)
 * @return 滤波后的电机电流绝对值(A)
 */
float mcpwm_foc_get_abs_motor_current_filtered(void);

/**
 * 获取有向总电流
 * 考虑方向的电机电流(正值=电机模式，负值=发电机模式)
 * @return 有向总电流(A)
 */
float mcpwm_foc_get_tot_current_directional(void);

/**
 * 获取有向总电流(滤波)
 * @return 滤波后的有向总电流(A)
 */
float mcpwm_foc_get_tot_current_directional_filtered(void);

/*
 * ============================================================================
 * dq轴电流和电压函数 (dq-axis Current & Voltage Functions)
 * ============================================================================
 */

/**
 * 获取d轴电流
 * d轴电流主要用于磁通控制和弱磁控制
 * @return d轴电流(A)
 */
float mcpwm_foc_get_id(void);

/**
 * 获取q轴电流
 * q轴电流直接对应电机转矩：T = Kt × Iq
 * @return q轴电流(A)
 */
float mcpwm_foc_get_iq(void);

/**
 * 获取d轴电流(滤波)
 * @return 滤波后的d轴电流(A)
 */
float mcpwm_foc_get_id_filter(void);

/**
 * 获取q轴电流(滤波)
 * @return 滤波后的q轴电流(A)
 */
float mcpwm_foc_get_iq_filter(void);

/**
 * 获取输入总电流
 * @return 输入总电流(A)
 */
float mcpwm_foc_get_tot_current_in(void);

/**
 * 获取输入总电流(滤波)
 * @return 滤波后的输入总电流(A)
 */
float mcpwm_foc_get_tot_current_in_filtered(void);

/**
 * 获取转速计数值
 * @param reset 是否重置计数器
 * @return 转速计数值
 */
int mcpwm_foc_get_tachometer_value(bool reset);

/**
 * 获取转速计绝对值
 * @param reset 是否重置计数器
 * @return 转速计绝对值
 */
int mcpwm_foc_get_tachometer_abs_value(bool reset);

/*
 * ============================================================================
 * 相位和电压函数 (Phase & Voltage Functions)
 * ============================================================================
 */

/**
 * 获取转子电角度
 * @return 转子电角度(弧度)
 */
float mcpwm_foc_get_phase(void);

/**
 * 获取观测器估算的转子电角度
 * @return 观测器角度(弧度)
 */
float mcpwm_foc_get_phase_observer(void);

/**
 * 获取编码器测量的转子电角度
 * @return 编码器角度(弧度)
 */
float mcpwm_foc_get_phase_encoder(void);

/**
 * 获取d轴电压
 * @return d轴电压(V)
 */
float mcpwm_foc_get_vd(void);

/**
 * 获取q轴电压
 * @return q轴电压(V)
 */
float mcpwm_foc_get_vq(void);

/*
 * ============================================================================
 * 调制信号函数 (Modulation Signal Functions)
 * ============================================================================
 */

/**
 * 获取原始α轴调制信号
 * @return α轴调制信号
 */
float mcpwm_foc_get_mod_alpha_raw(void);

/**
 * 获取原始β轴调制信号
 * @return β轴调制信号
 */
float mcpwm_foc_get_mod_beta_raw(void);

/**
 * 获取测量的α轴调制信号
 * @return 测量的α轴调制信号
 */
float mcpwm_foc_get_mod_alpha_measured(void);

/**
 * 获取测量的β轴调制信号
 * @return 测量的β轴调制信号
 */
float mcpwm_foc_get_mod_beta_measured(void);

/*
 * ============================================================================
 * 电机参数估算函数 (Motor Parameter Estimation Functions)
 * ============================================================================
 */

/**
 * 获取估算的磁链
 * @return 估算磁链(Wb)
 */
float mcpwm_foc_get_est_lambda(void);

/**
 * 获取估算的电阻
 * @return 估算电阻(Ω)
 */
float mcpwm_foc_get_est_res(void);

/**
 * 获取估算的电感
 * @return 估算电感(H)
 */
float mcpwm_foc_get_est_ind(void);
/*
 * ============================================================================
 * 电机检测和测量函数 (Motor Detection & Measurement Functions)
 * ============================================================================
 */

/**
 * 编码器检测
 * 自动检测编码器偏移、传动比和方向
 * @param current 检测电流(A)
 * @param print 是否打印结果
 * @param offset 输出：编码器偏移角度
 * @param ratio 输出：编码器传动比
 * @param inverted 输出：是否反向
 * @return 检测结果状态码
 */
int mcpwm_foc_encoder_detect(float current, bool print, float *offset, float *ratio, bool *inverted);

/**
 * 电阻测量
 * 使用直流电流测量电机相电阻：R = V / I
 * @param current 测量电流(A)
 * @param samples 采样次数
 * @param stop_after 测量后是否停止
 * @param resistance 输出：测量的电阻值(Ω)
 * @return 测量结果状态码
 */
int mcpwm_foc_measure_resistance(float current, int samples, bool stop_after, float *resistance);

/**
 * 电感测量(占空比方式)
 * 使用PWM占空比测量电机电感：L = V×dt / dI
 * @param duty 测量占空比
 * @param samples 采样次数
 * @param curr 输出：测量电流
 * @param ld_lq_diff 输出：Ld-Lq差值
 * @param inductance 输出：电感值(H)
 * @return 测量结果状态码
 */
int mcpwm_foc_measure_inductance(float duty, int samples, float *curr, float *ld_lq_diff, float *inductance);

/**
 * 电感测量(电流方式)
 * 使用目标电流测量电机电感
 * @param curr_goal 目标电流(A)
 * @param samples 采样次数
 * @param curr 输出：实际电流
 * @param ld_lq_diff 输出：Ld-Lq差值
 * @param inductance 输出：电感值(H)
 * @return 测量结果状态码
 */
int mcpwm_foc_measure_inductance_current(float curr_goal, int samples, float *curr, float *ld_lq_diff, float *inductance);

/**
 * 电机蜂鸣器
 * 使电机发出指定频率的声音
 * @param freq 频率(Hz)
 * @param time 持续时间(s)
 * @param voltage 电压幅值(V)
 * @return 是否成功
 */
bool mcpwm_foc_beep(float freq, float time, float voltage);

/**
 * 电阻电感综合测量
 * 同时测量电机的电阻和电感参数
 * @param res 输出：电阻值(Ω)
 * @param ind 输出：电感值(H)
 * @param ld_lq_diff 输出：Ld-Lq差值(H)
 * @return 测量结果状态码
 */
int mcpwm_foc_measure_res_ind(float *res, float *ind, float *ld_lq_diff);

/**
 * 霍尔传感器检测
 * 自动检测霍尔传感器的换相表
 * @param current 检测电流(A)
 * @param hall_table 输出：霍尔换相表
 * @param result 输出：检测结果
 * @return 检测状态码
 */
int mcpwm_foc_hall_detect(float current, uint8_t *hall_table, bool *result);

/**
 * 直流校准
 * 校准ADC采样的直流偏移
 * @param cal_undriven 是否校准未驱动状态
 * @return 校准结果状态码
 */
int mcpwm_foc_dc_cal(bool cal_undriven);

/*
 * ============================================================================
 * 调试和诊断函数 (Debug & Diagnostic Functions)
 * ============================================================================
 */

/**
 * 打印FOC状态
 * 输出当前FOC系统的详细状态信息
 */
void mcpwm_foc_print_state(void);

/**
 * 获取上次ADC中断持续时间
 * 用于性能监测和调试
 * @return 中断持续时间(μs)
 */
float mcpwm_foc_get_last_adc_isr_duration(void);

/*
 * ============================================================================
 * 偏移校准函数 (Offset Calibration Functions)
 * ============================================================================
 */

/**
 * 获取电流偏移值
 * @param curr0_offset 输出：电流0偏移
 * @param curr1_offset 输出：电流1偏移
 * @param curr2_offset 输出：电流2偏移
 * @param is_second_motor 是否为第二个电机
 */
void mcpwm_foc_get_current_offsets(
		volatile float *curr0_offset,
		volatile float *curr1_offset,
		volatile float *curr2_offset,
		bool is_second_motor);

/**
 * 设置电流偏移值
 * @param curr0_offset 电流0偏移
 * @param curr1_offset 电流1偏移
 * @param curr2_offset 电流2偏移
 */
void mcpwm_foc_set_current_offsets(
		volatile float curr0_offset,
		volatile float curr1_offset,
		volatile float curr2_offset);

/**
 * 获取电压偏移值
 * @param v0_offset 输出：电压0偏移
 * @param v1_offset 输出：电压1偏移
 * @param v2_offset 输出：电压2偏移
 * @param is_second_motor 是否为第二个电机
 */
void mcpwm_foc_get_voltage_offsets(
		float *v0_offset,
		float *v1_offset,
		float *v2_offset,
		bool is_second_motor);

/**
 * 获取未驱动状态电压偏移值
 * @param v0_offset 输出：电压0偏移
 * @param v1_offset 输出：电压1偏移
 * @param v2_offset 输出：电压2偏移
 * @param is_second_motor 是否为第二个电机
 */
void mcpwm_foc_get_voltage_offsets_undriven(
		float *v0_offset,
		float *v1_offset,
		float *v2_offset,
		bool is_second_motor);

/**
 * 获取ADC电流原始值
 * @param ph0 输出：相0电流
 * @param ph1 输出：相1电流
 * @param ph2 输出：相2电流
 * @param is_second_motor 是否为第二个电机
 */
void mcpwm_foc_get_currents_adc(
		float *ph0,
		float *ph1,
		float *ph2,
		bool is_second_motor);

/*
 * ============================================================================
 * 系统信息函数 (System Information Functions)
 * ============================================================================
 */

/**
 * 获取采样周期
 * @return 采样周期(s)
 */
float mcpwm_foc_get_ts(void);

/**
 * 检查是否使用编码器
 * @return true=使用编码器, false=无传感器
 */
bool mcpwm_foc_is_using_encoder(void);

/**
 * 获取观测器状态
 * @param x1 输出：观测器状态1
 * @param x2 输出：观测器状态2
 */
void mcpwm_foc_get_observer_state(float *x1, float *x2);

/**
 * 设置电流关闭延时
 * @param delay_sec 延时时间(s)
 */
void mcpwm_foc_set_current_off_delay(float delay_sec);

/*
 * ============================================================================
 * 双电机选择函数 (Dual Motor Selection Functions)
 * ============================================================================
 */

/**
 * 获取指定电机的总电流
 * @param is_second_motor 是否为第二个电机
 * @return 总电流(A)
 */
float mcpwm_foc_get_tot_current_motor(bool is_second_motor);

/**
 * 获取指定电机的总电流(滤波)
 * @param is_second_motor 是否为第二个电机
 * @return 滤波后的总电流(A)
 */
float mcpwm_foc_get_tot_current_filtered_motor(bool is_second_motor);

/**
 * 获取指定电机的输入电流
 * @param is_second_motor 是否为第二个电机
 * @return 输入电流(A)
 */
float mcpwm_foc_get_tot_current_in_motor(bool is_second_motor);

/**
 * 获取指定电机的输入电流(滤波)
 * @param is_second_motor 是否为第二个电机
 * @return 滤波后的输入电流(A)
 */
float mcpwm_foc_get_tot_current_in_filtered_motor(bool is_second_motor);

/**
 * 获取指定电机的电流绝对值
 * @param is_second_motor 是否为第二个电机
 * @return 电流绝对值(A)
 */
float mcpwm_foc_get_abs_motor_current_motor(bool is_second_motor);

/**
 * 获取指定电机的电流绝对值(滤波)
 * @param is_second_motor 是否为第二个电机
 * @return 滤波后的电流绝对值(A)
 */
float mcpwm_foc_get_abs_motor_current_filtered_motor(bool is_second_motor);

/**
 * 获取指定电机的状态
 * @param is_second_motor 是否为第二个电机
 * @return 电机状态
 */
mc_state mcpwm_foc_get_state_motor(bool is_second_motor);

/*
 * ============================================================================
 * 中断处理函数 (Interrupt Handlers)
 * ============================================================================
 */

/**
 * 定时器采样中断处理函数
 * 处理定时器触发的采样中断，用于同步ADC采样
 */
void mcpwm_foc_tim_sample_int_handler(void);

/**
 * ADC中断处理函数
 * FOC控制的核心中断，执行电流采样、坐标变换、PI控制、SVPWM等
 *
 * 中断处理流程：
 * 1. 读取三相电流ADC值
 * 2. Clarke变换：abc → αβ
 * 3. Park变换：αβ → dq
 * 4. PI控制器：计算Vd、Vq
 * 5. 反Park变换：dq → αβ
 * 6. SVPWM：生成三相PWM占空比
 * 7. 更新PWM寄存器
 *
 * @param p 中断参数指针
 * @param flags 中断标志
 */
void mcpwm_foc_adc_int_handler(void *p, uint32_t flags);

/*
 * ============================================================================
 * 宏定义 (Macro Definitions)
 * ============================================================================
 */

/**
 * ADC电流采样偏移
 *
 * 定义ADC采样时刻相对于定时器顶部的偏移量。
 * 这个偏移确保在PWM周期的合适时刻进行电流采样，
 * 避免开关噪声对采样精度的影响。
 *
 * 采样时刻选择原则：
 * - 避开MOSFET开关瞬间
 * - 确保电流稳定
 * - 最大化采样窗口
 */
#define MCPWM_FOC_CURRENT_SAMP_OFFSET				(2) // 定时器顶部ADC采样偏移量

#endif /* MCPWM_FOC_H_ */
