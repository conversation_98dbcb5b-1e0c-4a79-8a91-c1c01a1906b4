--cpu=Cortex-M4.fp.sp
"..\Libraries\dsp\arm_cortexM4lf_math.lib"
".\objects\usbd_core.o"
".\objects\usbd_sdr.o"
".\objects\usbd_int.o"
".\objects\usb_core.o"
".\objects\system_at32a423.o"
".\objects\startup_at32a423.o"
".\objects\at32a423_acc.o"
".\objects\at32a423_adc.o"
".\objects\at32a423_can.o"
".\objects\at32a423_crc.o"
".\objects\at32a423_crm.o"
".\objects\at32a423_dac.o"
".\objects\at32a423_debug.o"
".\objects\at32a423_dma.o"
".\objects\at32a423_ertc.o"
".\objects\at32a423_exint.o"
".\objects\at32a423_flash.o"
".\objects\at32a423_gpio.o"
".\objects\at32a423_i2c.o"
".\objects\at32a423_misc.o"
".\objects\at32a423_pwc.o"
".\objects\at32a423_scfg.o"
".\objects\at32a423_spi.o"
".\objects\at32a423_tmr.o"
".\objects\at32a423_usart.o"
".\objects\at32a423_usb.o"
".\objects\at32a423_wdt.o"
".\objects\at32a423_wwdt.o"
".\objects\at32a423_xmc.o"
".\objects\anoptv8cmd.o"
".\objects\anoptv8framefactory.o"
".\objects\anoptv8par.o"
".\objects\anoptv8run.o"
".\objects\hwinterface.o"
".\objects\motorcmd.o"
".\objects\motordata.o"
".\objects\motorparams.o"
".\objects\delay.o"
".\objects\mathbasic.o"
".\objects\motor_vectorcontrol.o"
".\objects\sys_isr_controller.o"
".\objects\sysctl_analogprocess.o"
".\objects\sysctl_globalvar.o"
".\objects\sysctl_ioad2s1210.o"
".\objects\sysctl_rotorget.o"
".\objects\sysctl_sysmoore.o"
".\objects\algorithm.o"
".\objects\sys_basevalue.o"
".\objects\sys_timerevent.o"
".\objects\start_self_test.o"
".\objects\sys_spiflash.o"
".\objects\sys_protect.o"
".\objects\sys_can.o"
".\objects\sic_selftest.o"
".\objects\cdc_class.o"
".\objects\cdc_desc.o"
".\objects\usb_app.o"
".\objects\lbq_design.o"
".\objects\spi_w25q256.o"
".\objects\sensor_drive.o"
".\objects\ad2s1212_spi.o"
".\objects\enc_speed.o"
".\objects\rs422_cu.o"
".\objects\at32a423_int.o"
".\objects\at32a423_wk_config.o"
".\objects\main.o"
".\objects\overall_init.o"
".\objects\sysfsm.o"
--strict --scatter ".\Objects\Project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\Project.map" -o .\Objects\Project.axf