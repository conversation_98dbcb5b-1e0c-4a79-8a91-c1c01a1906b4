/**********************************************************
 * @file     motor_current_loop.c
 * @brief    高性能电流环控制实现文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     基于VESC架构的裸机电流环实现
 *           支持编码器和非线性磁链观测器
 *           不包含高频注入相关实现
***********************************************************/

#include "motor_current_loop.h"
#include "Sensor_Drive.h"
#include "ENC_Speed.h"
#include "adc_pmsm.h"
#include <math.h>
#include <string.h>

/*============================ 私有变量 ============================*/

// 全局电流环实例
static current_loop_t g_current_loop;

/*============================ 私有函数声明 ============================*/

static void current_sampling(current_loop_t *loop);
static void coordinate_transforms(current_loop_t *loop);
static void pi_current_control(current_loop_t *loop);
static void decoupling_control(current_loop_t *loop);
static void voltage_limit_and_svpwm(current_loop_t *loop);
static void pwm_output(current_loop_t *loop);
static void temperature_compensation(current_loop_t *loop);
static void angle_processing(current_loop_t *loop);

/*============================ 公共函数实现 ============================*/

/**
 * @brief 电流环初始化
 * @param loop 电流环结构体指针
 * @return 初始化结果
 */
ret_t current_loop_init(current_loop_t *loop)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 清零结构体
    memset(loop, 0, sizeof(current_loop_t));
    
    // 设置默认配置参数
    loop->config.current_kp = DEFAULT_CURRENT_KP;
    loop->config.current_ki = DEFAULT_CURRENT_KI;
    loop->config.d_gain_scale_start = 0.9f;
    loop->config.d_gain_scale_max_mod = DEFAULT_D_GAIN_SCALE;

    // 电机参数(需要根据实际电机调整)
    loop->config.motor.rs = 0.05f;              // 50mΩ
    loop->config.motor.ld = 0.0001f;            // 100μH
    loop->config.motor.lq = 0.0001f;            // 100μH
    loop->config.motor.flux_linkage = 0.01f;    // 10mWb
    loop->config.motor.pole_pairs = 4;          // 4极对
    loop->config.motor.rated_current = 50.0f;   // 50A
    loop->config.motor.rated_voltage = 350.0f;  // 350V
    loop->config.motor.rated_power = 10000.0f;  // 10kW
    loop->config.motor.rated_speed = 3000.0f;   // 3000rpm
    loop->config.motor.temp_coeff_rs = 0.004f;  // 铜线温度系数
    loop->config.motor.temp_ref = 25.0f;        // 参考温度25°C

    // 角度补偿参数
    loop->config.angle_comp.encoder_offset = 0.0f;
    loop->config.angle_comp.encoder_ratio = 1.0f;
    loop->config.angle_comp.encoder_inverted = FALSE;
    loop->config.angle_comp.phase_advance = 0.0f;
    loop->config.angle_comp.observer_offset = 0.0f;
    loop->config.angle_comp.switching_freq_comp = TRUE;
    loop->config.angle_comp.comp_factor = 0.5f;
    
    // 限制参数
    loop->config.max_current = MAX_CURRENT_A;
    loop->config.max_voltage = MAX_VOLTAGE_V;
    loop->config.max_duty = MAX_DUTY_CYCLE;

    // 解耦控制
    loop->config.decoupling_mode = DECOUPLING_CROSS_BEMF;
    loop->config.temp_comp_enabled = TRUE;

    // 传感器配置
    loop->config.sensor_mode = SENSOR_MODE_ENCODER;
    
    // 初始化PI控制器
    pi_controller_init(&loop->pi_id, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,  // 积分限幅
                      loop->config.max_voltage);        // 输出限幅
                      
    pi_controller_init(&loop->pi_iq, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,  // 积分限幅
                      loop->config.max_voltage);        // 输出限幅
    
    // 设置初始状态
    loop->motor_state = MOTOR_STATE_STOP;
    loop->control_mode = CONTROL_MODE_CURRENT;
    loop->enabled = FALSE;
    loop->output_enabled = FALSE;
    loop->fault_flag = FALSE;
    
    return RET_OK;
}

/**
 * @brief 设置电流环配置参数
 * @param loop 电流环结构体指针
 * @param config 配置参数指针
 * @return 设置结果
 */
ret_t current_loop_config_set(current_loop_t *loop, const current_loop_config_t *config)
{
    if (loop == NULL || config == NULL) {
        return RET_ERROR;
    }
    
    // 复制配置参数
    memcpy(&loop->config, config, sizeof(current_loop_config_t));
    
    // 更新PI控制器参数
    pi_controller_init(&loop->pi_id, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,
                      loop->config.max_voltage);
                      
    pi_controller_init(&loop->pi_iq, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,
                      loop->config.max_voltage);
    
    return RET_OK;
}

/**
 * @brief 重置电流环状态
 * @param loop 电流环结构体指针
 * @return 重置结果
 */
ret_t current_loop_reset(current_loop_t *loop)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 重置状态变量
    memset(&loop->state, 0, sizeof(current_loop_state_t));
    
    // 重置PI控制器
    pi_controller_reset(&loop->pi_id);
    pi_controller_reset(&loop->pi_iq);

    // 重置循环计数器
    loop->loop_counter = 0;
    
    return RET_OK;
}

/**
 * @brief 电流环主执行函数
 * @param loop 电流环结构体指针
 * @return 执行结果
 */
ret_t current_loop_execute(current_loop_t *loop)
{
    if (loop == NULL || !loop->enabled) {
        return RET_ERROR;
    }

    // 1. 电流采样
    current_sampling(loop);

    // 2. 角度处理和补偿
    angle_processing(loop);

    // 3. 坐标变换
    coordinate_transforms(loop);

    // 4. 温度补偿
    if (loop->config.temp_comp_enabled) {
        temperature_compensation(loop);
    }

    // 5. PI电流控制
    pi_current_control(loop);

    // 6. 解耦控制
    if (loop->config.decoupling_mode != DECOUPLING_DISABLED) {
        decoupling_control(loop);
    }

    // 7. 电压限制和SVPWM
    voltage_limit_and_svpwm(loop);

    // 8. PWM输出
    if (loop->output_enabled && loop->motor_state == MOTOR_STATE_RUNNING) {
        pwm_output(loop);
    }

    // 更新循环计数器
    loop->loop_counter++;

    return RET_OK;
}

/**
 * @brief 使能/禁用电流环
 * @param loop 电流环结构体指针
 * @param enable 使能标志
 * @return 操作结果
 */
ret_t current_loop_enable(current_loop_t *loop, bool_t enable)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    if (enable && !loop->fault_flag) {
        loop->enabled = TRUE;
        loop->motor_state = MOTOR_STATE_RUNNING;
        loop->output_enabled = TRUE;
        ENABLE_PWM_OUTPUT();
    } else {
        loop->enabled = FALSE;
        loop->motor_state = MOTOR_STATE_STOP;
        loop->output_enabled = FALSE;
        DISABLE_PWM_OUTPUT();
        
        // 重置控制器状态
        current_loop_reset(loop);
    }
    
    return RET_OK;
}

/**
 * @brief 设置dq轴电流参考值
 * @param loop 电流环结构体指针
 * @param id_ref d轴电流参考值(A)
 * @param iq_ref q轴电流参考值(A)
 * @return 设置结果
 */
ret_t current_loop_set_current_dq(current_loop_t *loop, float id_ref, float iq_ref)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 限制电流幅值
    float i_mag = sqrtf(id_ref * id_ref + iq_ref * iq_ref);
    if (i_mag > loop->config.max_current) {
        float scale = loop->config.max_current / i_mag;
        id_ref *= scale;
        iq_ref *= scale;
    }
    
    loop->state.id_target = id_ref;
    loop->state.iq_target = iq_ref;
    
    return RET_OK;
}

/**
 * @brief 设置电流目标值(仅q轴，d轴为0)
 * @param loop 电流环结构体指针
 * @param i_target 电流目标值(A)
 * @return 设置结果
 */
ret_t current_loop_set_current_target(current_loop_t *loop, float i_target)
{
    return current_loop_set_current_dq(loop, 0.0f, i_target);
}

/**
 * @brief 获取全局电流环实例指针
 * @return 电流环实例指针
 */
current_loop_t* current_loop_get_instance(void)
{
    return &g_current_loop;
}

/*============================ 状态获取函数 ============================*/

float current_loop_get_id(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.id : 0.0f;
}

float current_loop_get_iq(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.iq : 0.0f;
}

float current_loop_get_vd(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.vd : 0.0f;
}

float current_loop_get_vq(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.vq : 0.0f;
}

float current_loop_get_duty_cycle(const current_loop_t *loop)
{
    if (loop == NULL) return 0.0f;

    // 计算等效占空比
    float v_mag = sqrtf(loop->state.v_alpha * loop->state.v_alpha +
                       loop->state.v_beta * loop->state.v_beta);
    return (loop->state.v_bus > 0.1f) ? (v_mag / loop->state.v_bus) : 0.0f;
}

/*============================ 私有函数实现 ============================*/

/**
 * @brief 电流采样函数
 * @param loop 电流环结构体指针
 */
static void current_sampling(current_loop_t *loop)
{
    // 使用adc_pmsm模块获取滤波后的三相电流
    loop->state.ia_filt = GET_CURRENT_A();
    loop->state.ib_filt = GET_CURRENT_B();
    loop->state.ic_filt = GET_CURRENT_C();

    // 同时获取原始电流值
    loop->state.ia = ADC_GET_IA_RAW();
    loop->state.ib = ADC_GET_IB_RAW();
    loop->state.ic = ADC_GET_IC_RAW();

    // 读取母线电压
    loop->state.v_bus = GET_BUS_VOLTAGE();

    // 读取电机温度
    if (loop->config.temp_comp_enabled) {
        loop->state.temp_motor = GET_MOTOR_TEMP();
    }
}

/**
 * @brief 角度处理函数
 * @param loop 电流环结构体指针
 */
static void angle_processing(current_loop_t *loop)
{
    // 获取原始电角度
    loop->state.theta_elec_raw = GET_ELEC_ANGLE();

    // 应用角度补偿
    loop->state.theta_elec_comp = angle_compensation_apply(loop, loop->state.theta_elec_raw);

    // 使用补偿后的角度作为最终角度
    loop->state.theta_elec = loop->state.theta_elec_comp;

    // 计算正弦和余弦值
    arm_sin_cos_f32(loop->state.theta_elec, &loop->state.sin_theta, &loop->state.cos_theta);

    // 计算电角速度(简化估计)
    static float theta_prev = 0.0f;
    float theta_diff = loop->state.theta_elec - theta_prev;
    normalize_angle(&theta_diff);
    loop->state.omega_elec = theta_diff / CURRENT_LOOP_DT;
    theta_prev = loop->state.theta_elec;

    // 电角速度滤波
    loop->state.omega_elec_filt = low_pass_filter(loop->state.omega_elec,
                                                 loop->state.omega_elec_filt,
                                                 0.1f);
}

/**
 * @brief 坐标变换函数
 * @param loop 电流环结构体指针
 */
static void coordinate_transforms(current_loop_t *loop)
{
    // Clarke变换: abc → αβ (使用滤波后的电流)
    clarke_transform(loop->state.ia_filt, loop->state.ib_filt, loop->state.ic_filt,
                    &loop->state.i_alpha, &loop->state.i_beta);

    // αβ电流滤波
    loop->state.i_alpha_filt = low_pass_filter(loop->state.i_alpha, loop->state.i_alpha_filt, 0.1f);
    loop->state.i_beta_filt = low_pass_filter(loop->state.i_beta, loop->state.i_beta_filt, 0.1f);

    // 计算电流幅值
    loop->state.i_abs = sqrtf(loop->state.i_alpha * loop->state.i_alpha +
                             loop->state.i_beta * loop->state.i_beta);
    loop->state.i_abs_filt = low_pass_filter(loop->state.i_abs, loop->state.i_abs_filt, 0.1f);

    // Park变换: αβ → dq
    park_transform(loop->state.i_alpha, loop->state.i_beta,
                  loop->state.sin_theta, loop->state.cos_theta,
                  &loop->state.id, &loop->state.iq);

    // dq电流滤波
    loop->state.id_filt = low_pass_filter(loop->state.id, loop->state.id_filt, 0.1f);
    loop->state.iq_filt = low_pass_filter(loop->state.iq, loop->state.iq_filt, 0.1f);
}

/**
 * @brief PI电流控制函数
 * @param loop 电流环结构体指针
 */
static void pi_current_control(current_loop_t *loop)
{
    // 计算电流误差
    float id_error = loop->state.id_target - loop->state.id;
    float iq_error = loop->state.iq_target - loop->state.iq;

    // d轴增益调度
    float d_gain_scale = 1.0f;
    if (loop->config.d_gain_scale_start < 0.99f) {
        float duty_now = current_loop_get_duty_cycle(loop);
        if (duty_now > loop->config.d_gain_scale_start) {
            d_gain_scale = loop->config.d_gain_scale_max_mod;
        }
    }

    // 临时调整d轴PI控制器增益
    float kp_d_orig = loop->pi_id.kp;
    float ki_d_orig = loop->pi_id.ki;
    loop->pi_id.kp = kp_d_orig * d_gain_scale;
    loop->pi_id.ki = (loop->config.temp_comp_enabled) ?
                     loop->state.ki_temp_comp * d_gain_scale :
                     ki_d_orig * d_gain_scale;

    // PI控制器计算
    loop->state.vd = pi_controller_update(&loop->pi_id, id_error, CURRENT_LOOP_DT);
    loop->state.vq = pi_controller_update(&loop->pi_iq, iq_error, CURRENT_LOOP_DT);

    // 恢复原始增益
    loop->pi_id.kp = kp_d_orig;
    loop->pi_id.ki = ki_d_orig;

    // 保存积分项
    loop->state.vd_int = loop->pi_id.integral;
    loop->state.vq_int = loop->pi_iq.integral;
}

/**
 * @brief 解耦控制函数
 * @param loop 电流环结构体指针
 */
static void decoupling_control(current_loop_t *loop)
{
    // 计算电角速度(简化估计)
    static float theta_prev = 0.0f;
    float theta_diff = loop->state.theta_elec - theta_prev;
    normalize_angle(&theta_diff);
    loop->state.omega_elec = theta_diff / CURRENT_LOOP_DT;
    theta_prev = loop->state.theta_elec;

    // 初始化解耦项
    loop->state.dec_vd = 0.0f;
    loop->state.dec_vq = 0.0f;
    loop->state.dec_bemf = 0.0f;

    switch (loop->config.decoupling_mode) {
        case DECOUPLING_CROSS:
            // 交叉解耦: 消除dq轴之间的耦合
            loop->state.dec_vd = -loop->state.omega_elec * loop->config.motor_lq * loop->state.iq_filt;
            loop->state.dec_vq = loop->state.omega_elec * loop->config.motor_ld * loop->state.id_filt;
            break;

        case DECOUPLING_BEMF:
            // 反电动势解耦
            loop->state.dec_bemf = loop->state.omega_elec * loop->config.motor_flux_linkage;
            break;

        case DECOUPLING_CROSS_BEMF:
            // 交叉解耦 + 反电动势解耦
            loop->state.dec_vd = -loop->state.omega_elec * loop->config.motor_lq * loop->state.iq_filt;
            loop->state.dec_vq = loop->state.omega_elec * loop->config.motor_ld * loop->state.id_filt;
            loop->state.dec_bemf = loop->state.omega_elec * loop->config.motor_flux_linkage;
            break;

        default:
            break;
    }

    // 应用解耦补偿
    loop->state.vd += loop->state.dec_vd;
    loop->state.vq += loop->state.dec_vq + loop->state.dec_bemf;
}

/**
 * @brief 电压限制和SVPWM函数
 * @param loop 电流环结构体指针
 */
static void voltage_limit_and_svpwm(current_loop_t *loop)
{
    // 电压限制
    float v_max = loop->state.v_bus * loop->config.max_duty / SQRT3;
    limit_value(&loop->state.vd, -v_max, v_max);
    limit_value(&loop->state.vq, -v_max, v_max);

    // 反Park变换: dq → αβ
    inverse_park_transform(loop->state.vd, loop->state.vq,
                          loop->state.sin_theta, loop->state.cos_theta,
                          &loop->state.v_alpha, &loop->state.v_beta);

    // SVPWM计算
    svpwm_calculate(loop->state.v_alpha, loop->state.v_beta, loop->state.v_bus,
                   &loop->state.duty_a, &loop->state.duty_b, &loop->state.duty_c);

    // 占空比限制
    limit_value(&loop->state.duty_a, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
    limit_value(&loop->state.duty_b, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
    limit_value(&loop->state.duty_c, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
}

/**
 * @brief PWM输出函数
 * @param loop 电流环结构体指针
 */
static void pwm_output(current_loop_t *loop)
{
    // 输出PWM占空比到硬件
    SET_PWM_DUTY_A(loop->state.duty_a);
    SET_PWM_DUTY_B(loop->state.duty_b);
    SET_PWM_DUTY_C(loop->state.duty_c);
}

/**
 * @brief 温度补偿函数
 * @param loop 电流环结构体指针
 */
static void temperature_compensation(current_loop_t *loop)
{
    // 铜线电阻温度系数: 0.004/°C
    const float temp_coeff = 0.004f;
    const float temp_ref = 25.0f;  // 参考温度

    // 计算温度补偿因子
    float temp_factor = 1.0f + temp_coeff * (loop->state.temp_motor - temp_ref);

    // 应用温度补偿到积分增益
    loop->state.ki_temp_comp = loop->config.current_ki * temp_factor;

    // 限制补偿范围
    limit_value(&loop->state.ki_temp_comp,
               loop->config.current_ki * 0.5f,
               loop->config.current_ki * 2.0f);
}

/**
 * @brief 性能统计函数
 * @param loop 电流环结构体指针
 */
static void performance_statistics(current_loop_t *loop)
{
    // 计算执行时间(微秒)
    uint32_t exec_cycles = DWT->CYCCNT - exec_time_start;
    float exec_time_us = (float)exec_cycles / (SystemCoreClock / 1000000.0f);

    // 更新最大执行时间
    if (exec_time_us > loop->max_exec_time_us) {
        loop->max_exec_time_us = exec_time_us;
    }

    // 计算平均执行时间
    exec_time_sum += exec_cycles;
    exec_time_count++;

    if (exec_time_count >= 1000) {  // 每1000次更新一次平均值
        loop->avg_exec_time_us = (float)exec_time_sum / exec_time_count / (SystemCoreClock / 1000000.0f);
        exec_time_sum = 0;
        exec_time_count = 0;
    }
}

/*============================ 电流环中断处理函数 ============================*/

/**
 * @brief 电流环中断处理函数
 * @note 在PWM中断或定时器中断中调用，频率为20kHz
 */
void motor_current_loop_interrupt_handler(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 执行电流环控制
    current_loop_execute(loop);
}

/**
 * @brief 电流环快速初始化函数
 * @note 使用默认参数快速初始化电流环
 * @return 初始化结果
 */
ret_t motor_current_loop_quick_init(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 初始化电流环
    ret_t result = current_loop_init(loop);
    if (result != RET_OK) {
        return result;
    }

    // 初始化硬件接口
    // 用户需要根据实际硬件实现这些函数
    // motor_pwm_init();
    // motor_adc_init();
    // motor_encoder_init();
    // motor_protection_init();
    // motor_interrupt_init();

    return RET_OK;
}

/**
 * @brief 电流环启动函数
 * @param target_current 目标电流(A)
 * @return 启动结果
 */
ret_t motor_current_loop_start(float target_current)
{
    current_loop_t *loop = current_loop_get_instance();

    // 设置目标电流
    ret_t result = current_loop_set_current_target(loop, target_current);
    if (result != RET_OK) {
        return result;
    }

    // 使能电流环
    return current_loop_enable(loop, TRUE);
}

/**
 * @brief 电流环停止函数
 * @return 停止结果
 */
ret_t motor_current_loop_stop(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 禁用电流环
    return current_loop_enable(loop, FALSE);
}
