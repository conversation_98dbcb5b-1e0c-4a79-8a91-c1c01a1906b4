/**********************************************************
 * @file     motor_current_loop.c
 * @brief    高性能电流环控制实现文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     基于VESC架构的裸机电流环实现
 *           支持编码器和非线性磁链观测器
 *           不包含高频注入相关实现
***********************************************************/

#include "motor_current_loop.h"
#include "Sensor_Drive.h"
#include "ENC_Speed.h"
#include "adc_pmsm.h"
#include "fast_trig_lookup.h"
#include <math.h>
#include <string.h>

/*============================ 私有变量 ============================*/

// 全局电流环实例
static current_loop_t g_current_loop;

/*============================ 私有函数声明 ============================*/

static void current_sampling(current_loop_t *loop);
static void coordinate_transforms(current_loop_t *loop);
static void pi_current_control(current_loop_t *loop);
static void decoupling_control(current_loop_t *loop);
static void voltage_limit_and_svpwm(current_loop_t *loop);
static void pwm_output(current_loop_t *loop);
static void temperature_compensation(current_loop_t *loop);
static void angle_processing(current_loop_t *loop);

/*============================ 公共函数实现 ============================*/

/**
 * @brief 电流环初始化
 * @param loop 电流环结构体指针
 * @return 初始化结果
 */
ret_t current_loop_init(current_loop_t *loop)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 清零结构体
    memset(loop, 0, sizeof(current_loop_t));
    
    // 设置默认配置参数
    loop->config.current_kp = DEFAULT_CURRENT_KP;
    loop->config.current_ki = DEFAULT_CURRENT_KI;
    loop->config.d_gain_scale_start = 0.9f;
    loop->config.d_gain_scale_max_mod = DEFAULT_D_GAIN_SCALE;

    // 电机参数(需要根据实际电机调整)
    loop->config.motor.rs = 0.05f;              // 50mΩ
    loop->config.motor.ld = 0.0001f;            // 100μH
    loop->config.motor.lq = 0.0001f;            // 100μH
    loop->config.motor.flux_linkage = 0.01f;    // 10mWb
    loop->config.motor.pole_pairs = 4;          // 4极对
    loop->config.motor.rated_current = 50.0f;   // 50A
    loop->config.motor.rated_voltage = 350.0f;  // 350V
    loop->config.motor.rated_power = 10000.0f;  // 10kW
    loop->config.motor.rated_speed = 3000.0f;   // 3000rpm
    loop->config.motor.temp_coeff_rs = 0.004f;  // 铜线温度系数
    loop->config.motor.temp_ref = 25.0f;        // 参考温度25°C

    // 角度补偿参数
    loop->config.angle_comp.encoder_offset = 0.0f;
    loop->config.angle_comp.encoder_ratio = 1.0f;
    loop->config.angle_comp.encoder_inverted = FALSE;
    loop->config.angle_comp.phase_advance = 0.0f;
    loop->config.angle_comp.observer_offset = 0.0f;
    loop->config.angle_comp.switching_freq_comp = TRUE;
    loop->config.angle_comp.comp_factor = 0.5f;

    // 观测器配置参数
    loop->config.observer.type = OBSERVER_ORTEGA_ORIGINAL;
    loop->config.observer.gain = 1000.0f;           // 对应VESC的foc_observer_gain
    loop->config.observer.pll_kp = 2000.0f;         // 对应VESC的foc_pll_kp
    loop->config.observer.pll_ki = 40000.0f;        // 对应VESC的foc_pll_ki
    loop->config.observer.sl_erpm = 2500.0f;        // 对应VESC的foc_sl_erpm
    loop->config.observer.enable_switch = TRUE;
    
    // 限制参数
    loop->config.max_current = MAX_CURRENT_A;
    loop->config.max_voltage = MAX_VOLTAGE_V;
    loop->config.max_duty = MAX_DUTY_CYCLE;

    // 解耦控制
    loop->config.decoupling_mode = DECOUPLING_CROSS_BEMF;
    loop->config.temp_comp_enabled = TRUE;

    // 传感器配置
    loop->config.sensor_mode = SENSOR_MODE_ENCODER;
    
    // 初始化PI控制器
    pi_controller_init(&loop->pi_id, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,  // 积分限幅
                      loop->config.max_voltage);        // 输出限幅
                      
    pi_controller_init(&loop->pi_iq, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,  // 积分限幅
                      loop->config.max_voltage);        // 输出限幅
    
    // 设置初始状态
    loop->motor_state = MOTOR_STATE_STOP;
    loop->control_mode = CONTROL_MODE_CURRENT;
    loop->enabled = FALSE;
    loop->output_enabled = FALSE;
    loop->fault_flag = FALSE;

    // 初始化观测器
    observer_init(loop);

    return RET_OK;
}

/**
 * @brief 设置电流环配置参数
 * @param loop 电流环结构体指针
 * @param config 配置参数指针
 * @return 设置结果
 */
ret_t current_loop_config_set(current_loop_t *loop, const current_loop_config_t *config)
{
    if (loop == NULL || config == NULL) {
        return RET_ERROR;
    }
    
    // 复制配置参数
    memcpy(&loop->config, config, sizeof(current_loop_config_t));
    
    // 更新PI控制器参数
    pi_controller_init(&loop->pi_id, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,
                      loop->config.max_voltage);
                      
    pi_controller_init(&loop->pi_iq, 
                      loop->config.current_kp, 
                      loop->config.current_ki,
                      loop->config.max_voltage * 0.8f,
                      loop->config.max_voltage);
    
    return RET_OK;
}

/**
 * @brief 重置电流环状态
 * @param loop 电流环结构体指针
 * @return 重置结果
 */
ret_t current_loop_reset(current_loop_t *loop)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 重置状态变量
    memset(&loop->state, 0, sizeof(current_loop_state_t));
    
    // 重置PI控制器
    pi_controller_reset(&loop->pi_id);
    pi_controller_reset(&loop->pi_iq);

    // 重置循环计数器
    loop->loop_counter = 0;
    
    return RET_OK;
}

/**
 * @brief 电流环主执行函数
 * @param loop 电流环结构体指针
 * @return 执行结果
 */
ret_t current_loop_execute(current_loop_t *loop)
{
    if (loop == NULL || !loop->enabled) {
        return RET_ERROR;
    }

    // 1. 电流采样
    current_sampling(loop);

    // 2. 角度处理和补偿
    angle_processing(loop);

    // 3. 坐标变换
    coordinate_transforms(loop);

    // 4. 观测器更新
    observer_update(loop, CURRENT_LOOP_DT);

    // 5. 温度补偿
    if (loop->config.temp_comp_enabled) {
        temperature_compensation(loop);
    }

    // 6. PI电流控制
    pi_current_control(loop);

    // 7. 解耦控制
    if (loop->config.decoupling_mode != DECOUPLING_DISABLED) {
        decoupling_control(loop);
    }

    // 8. 电压限制和SVPWM
    voltage_limit_and_svpwm(loop);

    // 9. PWM输出
    if (loop->output_enabled && loop->motor_state == MOTOR_STATE_RUNNING) {
        pwm_output(loop);
    }

    // 更新循环计数器
    loop->loop_counter++;

    return RET_OK;
}

/**
 * @brief 使能/禁用电流环
 * @param loop 电流环结构体指针
 * @param enable 使能标志
 * @return 操作结果
 */
ret_t current_loop_enable(current_loop_t *loop, bool_t enable)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    if (enable && !loop->fault_flag) {
        loop->enabled = TRUE;
        loop->motor_state = MOTOR_STATE_RUNNING;
        loop->output_enabled = TRUE;
        ENABLE_PWM_OUTPUT();
    } else {
        loop->enabled = FALSE;
        loop->motor_state = MOTOR_STATE_STOP;
        loop->output_enabled = FALSE;
        DISABLE_PWM_OUTPUT();
        
        // 重置控制器状态
        current_loop_reset(loop);
    }
    
    return RET_OK;
}

/**
 * @brief 设置dq轴电流参考值
 * @param loop 电流环结构体指针
 * @param id_ref d轴电流参考值(A)
 * @param iq_ref q轴电流参考值(A)
 * @return 设置结果
 */
ret_t current_loop_set_current_dq(current_loop_t *loop, float id_ref, float iq_ref)
{
    if (loop == NULL) {
        return RET_ERROR;
    }
    
    // 限制电流幅值
    float i_mag = sqrtf(id_ref * id_ref + iq_ref * iq_ref);
    if (i_mag > loop->config.max_current) {
        float scale = loop->config.max_current / i_mag;
        id_ref *= scale;
        iq_ref *= scale;
    }
    
    loop->state.id_target = id_ref;
    loop->state.iq_target = iq_ref;
    
    return RET_OK;
}

/**
 * @brief 设置电流目标值(仅q轴，d轴为0)
 * @param loop 电流环结构体指针
 * @param i_target 电流目标值(A)
 * @return 设置结果
 */
ret_t current_loop_set_current_target(current_loop_t *loop, float i_target)
{
    return current_loop_set_current_dq(loop, 0.0f, i_target);
}

/**
 * @brief 获取全局电流环实例指针
 * @return 电流环实例指针
 */
current_loop_t* current_loop_get_instance(void)
{
    return &g_current_loop;
}

/*============================ 状态获取函数 ============================*/

float current_loop_get_id(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.id : 0.0f;
}

float current_loop_get_iq(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.iq : 0.0f;
}

float current_loop_get_vd(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.vd : 0.0f;
}

float current_loop_get_vq(const current_loop_t *loop)
{
    return (loop != NULL) ? loop->state.vq : 0.0f;
}

float current_loop_get_duty_cycle(const current_loop_t *loop)
{
    if (loop == NULL) return 0.0f;

    // 计算等效占空比
    float v_mag = sqrtf(loop->state.v_alpha * loop->state.v_alpha +
                       loop->state.v_beta * loop->state.v_beta);
    return (loop->state.v_bus > 0.1f) ? (v_mag / loop->state.v_bus) : 0.0f;
}

/*============================ 私有函数实现 ============================*/

/**
 * @brief 电流采样函数
 * @param loop 电流环结构体指针
 */
static void current_sampling(current_loop_t *loop)
{
    // 使用adc_pmsm模块获取滤波后的三相电流
    loop->state.ia_filt = GET_CURRENT_A();
    loop->state.ib_filt = GET_CURRENT_B();
    loop->state.ic_filt = GET_CURRENT_C();

    // 同时获取原始电流值
    loop->state.ia = ADC_GET_IA_RAW();
    loop->state.ib = ADC_GET_IB_RAW();
    loop->state.ic = ADC_GET_IC_RAW();

    // 读取母线电压
    loop->state.v_bus = GET_BUS_VOLTAGE();

    // 读取电机温度
    if (loop->config.temp_comp_enabled) {
        loop->state.temp_motor = GET_MOTOR_TEMP();
    }
}

/**
 * @brief 角度处理函数
 * @param loop 电流环结构体指针
 */
static void angle_processing(current_loop_t *loop)
{
    // 获取原始电角度
    loop->state.theta_elec_raw = GET_ELEC_ANGLE();

    // 应用角度补偿
    loop->state.theta_elec_comp = angle_compensation_apply(loop, loop->state.theta_elec_raw);

    // 使用补偿后的角度作为最终角度
    loop->state.theta_elec = loop->state.theta_elec_comp;

    // 使用快速三角函数查找表计算正弦和余弦值
    fast_sincos(loop->state.theta_elec, &loop->state.sin_theta, &loop->state.cos_theta);

    // 计算电角速度(简化估计)
    static float theta_prev = 0.0f;
    float theta_diff = loop->state.theta_elec - theta_prev;
    normalize_angle(&theta_diff);
    loop->state.omega_elec = theta_diff / CURRENT_LOOP_DT;
    theta_prev = loop->state.theta_elec;

    // 电角速度滤波
    loop->state.omega_elec_filt = low_pass_filter(loop->state.omega_elec,
                                                 loop->state.omega_elec_filt,
                                                 0.1f);
}

/**
 * @brief 坐标变换函数
 * @param loop 电流环结构体指针
 */
static void coordinate_transforms(current_loop_t *loop)
{
    // Clarke变换: abc → αβ (使用滤波后的电流)
    clarke_transform(loop->state.ia_filt, loop->state.ib_filt, loop->state.ic_filt,
                    &loop->state.i_alpha, &loop->state.i_beta);

    // αβ电流滤波
    loop->state.i_alpha_filt = low_pass_filter(loop->state.i_alpha, loop->state.i_alpha_filt, 0.1f);
    loop->state.i_beta_filt = low_pass_filter(loop->state.i_beta, loop->state.i_beta_filt, 0.1f);

    // 计算电流幅值
    loop->state.i_abs = sqrtf(loop->state.i_alpha * loop->state.i_alpha +
                             loop->state.i_beta * loop->state.i_beta);
    loop->state.i_abs_filt = low_pass_filter(loop->state.i_abs, loop->state.i_abs_filt, 0.1f);

    // Park变换: αβ → dq
    park_transform(loop->state.i_alpha, loop->state.i_beta,
                  loop->state.sin_theta, loop->state.cos_theta,
                  &loop->state.id, &loop->state.iq);

    // dq电流滤波
    loop->state.id_filt = low_pass_filter(loop->state.id, loop->state.id_filt, 0.1f);
    loop->state.iq_filt = low_pass_filter(loop->state.iq, loop->state.iq_filt, 0.1f);
}

/**
 * @brief PI电流控制函数 (完全基于VESC实现)
 *
 * 实现与VESC完全一致的电流环PI控制算法，包括：
 * 1. d轴增益调度
 * 2. 温度补偿
 * 3. 积分器直接更新
 * 4. Back-calculation积分抗饱和
 *
 * @param loop 电流环结构体指针
 */
static void pi_current_control(current_loop_t *loop)
{
    // 计算电流误差
    float id_error = loop->state.id_target - loop->state.id;
    float iq_error = loop->state.iq_target - loop->state.iq;

    // d轴增益调度 (与VESC一致)
    float d_gain_scale = 1.0f;
    if (loop->config.d_gain_scale_start < 0.99f) {
        float duty_now = current_loop_get_duty_cycle(loop);
        if (duty_now > loop->config.d_gain_scale_start) {
            d_gain_scale = loop->config.d_gain_scale_max_mod;
        }
    }

    // 温度补偿积分增益
    float ki = loop->config.current_ki;
    if (loop->config.temp_comp_enabled) {
        ki = loop->state.ki_temp_comp;
    }

    /*
     * 积分器更新 (与VESC完全一致)
     *
     * 直接更新积分器状态，而不是通过PI控制器函数
     * 这样可以实现与VESC相同的积分抗饱和算法
     */
    // d轴积分器更新 (使用增益缩放)
    loop->state.vd_int += id_error * (ki * d_gain_scale * CURRENT_LOOP_DT);

    // q轴积分器更新
    loop->state.vq_int += iq_error * (ki * CURRENT_LOOP_DT);

    /*
     * PI电流控制器输出计算
     *
     * 与VESC相同的控制方程：
     * vd = vd_int + id_error * kp * d_gain_scale
     * vq = vq_int + iq_error * kp
     */
    loop->state.vd = loop->state.vd_int + id_error * loop->config.current_kp * d_gain_scale;
    loop->state.vq = loop->state.vq_int + iq_error * loop->config.current_kp;

    // 同步PI控制器结构体的积分项 (用于状态监控)
    loop->pi_id.integral = loop->state.vd_int;
    loop->pi_iq.integral = loop->state.vq_int;
}

/**
 * @brief 解耦控制函数
 * @param loop 电流环结构体指针
 */
static void decoupling_control(current_loop_t *loop)
{
    // 使用滤波后的电角速度
    float omega_elec = loop->state.omega_elec_filt;

    // 初始化解耦项
    loop->state.dec_vd = 0.0f;
    loop->state.dec_vq = 0.0f;
    loop->state.dec_bemf = 0.0f;

    switch (loop->config.decoupling_mode) {
        case DECOUPLING_CROSS:
            // 交叉解耦: 消除dq轴之间的耦合
            // vd_dec = -ωe * Lq * iq
            // vq_dec = ωe * Ld * id
            loop->state.dec_vd = -omega_elec * loop->config.motor.lq * loop->state.iq_filt;
            loop->state.dec_vq = omega_elec * loop->config.motor.ld * loop->state.id_filt;
            break;

        case DECOUPLING_BEMF:
            // 反电动势解耦
            // vq_bemf = ωe * ψm
            loop->state.dec_bemf = omega_elec * loop->config.motor.flux_linkage;
            break;

        case DECOUPLING_CROSS_BEMF:
            // 交叉解耦 + 反电动势解耦
            loop->state.dec_vd = -omega_elec * loop->config.motor.lq * loop->state.iq_filt;
            loop->state.dec_vq = omega_elec * loop->config.motor.ld * loop->state.id_filt;
            loop->state.dec_bemf = omega_elec * loop->config.motor.flux_linkage;
            break;

        default:
            break;
    }

    // 应用解耦补偿
    loop->state.vd += loop->state.dec_vd;
    loop->state.vq += loop->state.dec_vq + loop->state.dec_bemf;
}

/**
 * @brief 电压限制和SVPWM函数 (完全基于VESC实现)
 *
 * 实现与VESC完全一致的电压饱和限制和积分抗饱和算法：
 * 1. 圆形电压限制 (优先级策略：d轴优先)
 * 2. Back-calculation积分抗饱和
 * 3. 矢量饱和处理
 *
 * @param loop 电流环结构体指针
 */
static void voltage_limit_and_svpwm(current_loop_t *loop)
{
    /*
     * 电压饱和限制和积分反饱和 (与VESC完全一致)
     *
     * 当PI控制器输出超过物理限制时，需要进行饱和限制，
     * 同时防止积分器饱和(windup)。
     *
     * 优先级策略：
     * d轴优先级高于q轴，因为d轴控制弱磁和效率。
     *
     * 饱和限制算法：
     * 1. 首先限制vd在[-max_v_mag, +max_v_mag]范围内
     * 2. 计算剩余电压空间：max_vq = √(max_v_mag² - vd²)
     * 3. 限制vq在[-max_vq, +max_vq]范围内
     *
     * 积分反饱和：
     * 当输出饱和时，调整积分器以防止积分饱和：
     * I_new = I_old + (V_saturated - V_unsaturated)
     */
    float max_v_mag = ONE_DIV_SQRT3 * loop->config.max_duty * loop->state.v_bus;  // 最大电压矢量幅值

    // d轴电压饱和限制和积分抗饱和
    float vd_presat = loop->state.vd;                           // 饱和前的d轴电压
    limit_value(&loop->state.vd, -max_v_mag, max_v_mag);       // d轴电压饱和限制
    loop->state.vd_int += (loop->state.vd - vd_presat);        // d轴积分器反饱和

    // 计算q轴最大可用电压 (圆形限制)
    float max_vq = sqrtf(SQ(max_v_mag) - SQ(loop->state.vd));  // 计算q轴最大可用电压

    // q轴电压饱和限制和积分抗饱和
    float vq_presat = loop->state.vq;                          // 饱和前的q轴电压
    limit_value(&loop->state.vq, -max_vq, max_vq);             // q轴电压饱和限制
    loop->state.vq_int += (loop->state.vq - vq_presat);        // q轴积分器反饱和

    // 最终矢量饱和处理 (确保 |vd + j*vq| ≤ max_v_mag)
    float v_mag = sqrtf(SQ(loop->state.vd) + SQ(loop->state.vq));
    if (v_mag > max_v_mag) {
        float scale = max_v_mag / v_mag;
        loop->state.vd *= scale;
        loop->state.vq *= scale;
    }

    // 反Park变换: dq → αβ
    inverse_park_transform(loop->state.vd, loop->state.vq,
                          loop->state.sin_theta, loop->state.cos_theta,
                          &loop->state.v_alpha, &loop->state.v_beta);

    // SVPWM计算
    svpwm_calculate(loop->state.v_alpha, loop->state.v_beta, loop->state.v_bus,
                   &loop->state.duty_a, &loop->state.duty_b, &loop->state.duty_c);

    // 占空比限制
    limit_value(&loop->state.duty_a, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
    limit_value(&loop->state.duty_b, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
    limit_value(&loop->state.duty_c, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE);
}

/**
 * @brief PWM输出函数
 * @param loop 电流环结构体指针
 */
static void pwm_output(current_loop_t *loop)
{
    // 输出PWM占空比到硬件
    SET_PWM_DUTY_A(loop->state.duty_a);
    SET_PWM_DUTY_B(loop->state.duty_b);
    SET_PWM_DUTY_C(loop->state.duty_c);
}

/**
 * @brief 温度补偿函数
 * @param loop 电流环结构体指针
 */
static void temperature_compensation(current_loop_t *loop)
{
    // 使用电机参数中的温度系数和参考温度
    float temp_coeff = loop->config.motor.temp_coeff_rs;
    float temp_ref = loop->config.motor.temp_ref;

    // 计算温度补偿因子
    float temp_factor = 1.0f + temp_coeff * (loop->state.temp_motor - temp_ref);

    // 应用温度补偿到积分增益
    loop->state.ki_temp_comp = loop->config.current_ki * temp_factor;

    // 应用温度补偿到电阻值
    loop->state.rs_temp_comp = loop->config.motor.rs * temp_factor;

    // 限制补偿范围
    limit_value(&loop->state.ki_temp_comp,
               loop->config.current_ki * 0.5f,
               loop->config.current_ki * 2.0f);

    limit_value(&loop->state.rs_temp_comp,
               loop->config.motor.rs * 0.5f,
               loop->config.motor.rs * 2.0f);
}

/*============================ 角度补偿函数实现 ============================*/

/**
 * @brief 应用角度补偿
 * @param loop 电流环结构体指针
 * @param raw_angle 原始角度(rad)
 * @return 补偿后的角度(rad)
 */
float angle_compensation_apply(current_loop_t *loop, float raw_angle)
{
    float compensated_angle = raw_angle;

    // 编码器偏移补偿
    compensated_angle += loop->config.angle_comp.encoder_offset;

    // 编码器方向补偿
    if (loop->config.angle_comp.encoder_inverted) {
        compensated_angle = -compensated_angle;
    }

    // 编码器传动比补偿
    compensated_angle *= loop->config.angle_comp.encoder_ratio;

    // 相位超前补偿
    compensated_angle += loop->config.angle_comp.phase_advance;

    // 开关频率相位滞后补偿
    if (loop->config.angle_comp.switching_freq_comp) {
        float comp_factor = loop->config.angle_comp.comp_factor;
        float observer_offset = loop->config.angle_comp.observer_offset;
        compensated_angle += loop->state.omega_elec_filt * CURRENT_LOOP_DT * (comp_factor + observer_offset);
    }

    // 角度归一化
    normalize_angle(&compensated_angle);

    return compensated_angle;
}

/**
 * @brief 设置编码器零点偏移
 * @param loop 电流环结构体指针
 * @param offset 偏移角度(rad)
 * @return 设置结果
 */
ret_t angle_compensation_set_encoder_offset(current_loop_t *loop, float offset)
{
    if (loop == NULL) {
        return RET_ERROR;
    }

    loop->config.angle_comp.encoder_offset = offset;
    return RET_OK;
}

/**
 * @brief 设置相位超前补偿
 * @param loop 电流环结构体指针
 * @param advance 超前角度(rad)
 * @return 设置结果
 */
ret_t angle_compensation_set_phase_advance(current_loop_t *loop, float advance)
{
    if (loop == NULL) {
        return RET_ERROR;
    }

    loop->config.angle_comp.phase_advance = advance;
    return RET_OK;
}

/*============================ 电机参数函数实现 ============================*/

/**
 * @brief 设置电机参数
 * @param loop 电流环结构体指针
 * @param params 电机参数指针
 * @return 设置结果
 */
ret_t motor_params_set(current_loop_t *loop, const motor_params_t *params)
{
    if (loop == NULL || params == NULL) {
        return RET_ERROR;
    }

    // 复制电机参数
    memcpy(&loop->config.motor, params, sizeof(motor_params_t));

    return RET_OK;
}

/**
 * @brief 获取电机参数
 * @param loop 电流环结构体指针
 * @param params 电机参数输出指针
 * @return 获取结果
 */
ret_t motor_params_get(const current_loop_t *loop, motor_params_t *params)
{
    if (loop == NULL || params == NULL) {
        return RET_ERROR;
    }

    // 复制电机参数
    memcpy(params, &loop->config.motor, sizeof(motor_params_t));

    return RET_OK;
}

/**
 * @brief 获取温度补偿后的电阻值
 * @param loop 电流环结构体指针
 * @return 温度补偿后的电阻值(Ω)
 */
float motor_params_get_temp_compensated_rs(const current_loop_t *loop)
{
    if (loop == NULL) {
        return 0.0f;
    }

    if (loop->config.temp_comp_enabled) {
        return loop->state.rs_temp_comp;
    } else {
        return loop->config.motor.rs;
    }
}

/*============================ 观测器接口函数实现 (基于VESC) ============================*/

/**
 * @brief 观测器初始化函数
 * @param loop 电流环结构体指针
 * @return 初始化结果
 */
ret_t observer_init(current_loop_t *loop)
{
    if (loop == NULL) {
        return RET_ERROR;
    }

    // 初始化观测器状态
    memset(&loop->state.observer, 0, sizeof(observer_state_t));

    // 设置观测器磁链估计初始值
    loop->state.observer.lambda_est = loop->config.motor.flux_linkage;

    // 初始化PLL状态
    loop->state.pll_phase = 0.0f;
    loop->state.pll_speed = 0.0f;

    // 初始化相位状态
    loop->state.phase_now_observer = 0.0f;
    loop->state.phase_now_encoder = 0.0f;
    loop->state.using_encoder = TRUE;  // 默认使用编码器

    return RET_OK;
}

/**
 * @brief FOC观测器更新函数 (完全基于VESC实现)
 *
 * 实现多种无传感器观测器算法，用于估计永磁同步电机的转子位置。
 *
 * 参考文献：
 * http://cas.ensmp.fr/~praly/Telechargement/Journaux/2010-IEEE_TPEL-Lee-Hong-Nam-Ortega-Praly-Astolfi.pdf
 */
void foc_observer_update(float v_alpha, float v_beta, float i_alpha, float i_beta,
                        float dt, observer_state_t *state, float *phase, current_loop_t *loop)
{
    // 获取电机基本参数
    float R = loop->config.motor.rs;                    // 定子电阻 (Ω)
    float L = (loop->config.motor.ld + loop->config.motor.lq) * 0.5f;  // 平均电感 (H)
    float lambda = loop->config.motor.flux_linkage;     // 永磁体磁链 (Wb)

    // 温度补偿
    if (loop->config.temp_comp_enabled) {
        R = loop->state.rs_temp_comp;
    }

    // 预计算常用项
    float R_ia = R * i_alpha;
    float R_ib = R * i_beta;
    float L_ia = L * i_alpha;
    float L_ib = L * i_beta;

    // 观测器增益
    float gamma_half = loop->config.observer.gain * 0.5f;

    // 根据配置的观测器类型执行相应算法
    switch (loop->config.observer.type) {
        case OBSERVER_ORTEGA_ORIGINAL: {
            /*
             * Ortega原始观测器算法
             *
             * 基于磁链幅值约束的非线性观测器：
             * dx/dt = v - R*i + γ/2 * (x - L*i) * err
             * 其中 err = λ² - |x - L*i|²
             */
            float err = SQ(lambda) - (SQ(state->x1 - L_ia) + SQ(state->x2 - L_ib));

            // 强制误差项保持非正值有助于收敛性
            if (err > 0.0f) {
                err = 0.0f;
            }

            // Ortega观测器状态方程
            float x1_dot = v_alpha - R_ia + gamma_half * (state->x1 - L_ia) * err;
            float x2_dot = v_beta - R_ib + gamma_half * (state->x2 - L_ib) * err;

            // 数值积分更新状态
            state->x1 += x1_dot * dt;
            state->x2 += x2_dot * dt;
            break;
        }

        case OBSERVER_MXLEMMING: {
            /*
             * MXLEMMING观测器算法
             *
             * 改进的线性观测器，直接观测磁链矢量
             */
            float x1_dot = v_alpha - R_ia + gamma_half * (lambda - sqrtf(SQ(state->x1) + SQ(state->x2))) * state->x1;
            float x2_dot = v_beta - R_ib + gamma_half * (lambda - sqrtf(SQ(state->x1) + SQ(state->x2))) * state->x2;

            // 数值积分更新状态
            state->x1 += x1_dot * dt;
            state->x2 += x2_dot * dt;

            // 限制观测器状态在磁链幅值范围内
            float mag = sqrtf(SQ(state->x1) + SQ(state->x2));
            if (mag > lambda) {
                state->x1 = state->x1 * lambda / mag;
                state->x2 = state->x2 * lambda / mag;
            }

            // 设置为0以便与Ortega观测器使用相同的atan2计算
            L_ia = 0.0f;
            L_ib = 0.0f;
            break;
        }

        default:
            break;
    }

    // 保存当前电流值供下次迭代使用
    state->i_alpha_last = i_alpha;
    state->i_beta_last = i_beta;

    // 防止NaN值传播，确保数值稳定性
    if (isnan(state->x1)) state->x1 = 0.0f;
    if (isnan(state->x2)) state->x2 = 0.0f;

    // 防止磁链幅值过小导致角度不稳定
    float mag = sqrtf(SQ(state->x1) + SQ(state->x2));
    if (mag < (lambda * 0.5f)) {
        state->x1 *= 1.1f;  // 放大10%
        state->x2 *= 1.1f;
    }

    // 计算转子电角度
    if (phase) {
        *phase = atan2f(state->x2 - L_ib, state->x1 - L_ia);
    }
}

/**
 * @brief 锁相环(PLL)算法 (完全基于VESC实现)
 *
 * 功能：跟踪观测器输出的相位信号，提供平滑的相位和速度估计
 *
 * PLL方程：
 * θ̇_pll = ω_pll + Kp × Δθ     (相位更新方程)
 * ω̇_pll = Ki × Δθ             (频率更新方程)
 */
void foc_pll_run(float phase, float dt, float *phase_var, float *speed_var, current_loop_t *loop)
{
    // 防止NaN值传播
    if (isnan(*phase_var)) *phase_var = 0.0f;

    // 计算相位误差：Δθ = θ_input - θ_pll
    float delta_theta = phase - *phase_var;
    normalize_angle(&delta_theta);  // 将角度差归一化到[-π, π]

    // 防止速度变量为NaN
    if (isnan(*speed_var)) *speed_var = 0.0f;

    // PLL相位更新：θ_pll += (ω_pll + Kp * Δθ) * dt
    *phase_var += (*speed_var + loop->config.observer.pll_kp * delta_theta) * dt;
    normalize_angle(phase_var);  // 将相位归一化到[-π, π]

    // PLL速度更新：ω_pll += Ki * Δθ * dt
    *speed_var += loop->config.observer.pll_ki * delta_theta * dt;
}

/**
 * @brief 编码器角度校正函数 (完全基于VESC实现)
 *
 * 在观测器角度和编码器角度之间进行智能切换，根据转速自动选择
 * 最可靠的角度源。
 */
float foc_correct_encoder(float obs_angle, float enc_angle, float speed, float sl_erpm, current_loop_t *loop)
{
    // 计算当前转速的绝对值 (RPM)
    float rpm_abs = fabsf(speed * 60.0f / (2.0f * PI) * loop->config.motor.pole_pairs);

    /*
     * 滞回控制逻辑
     *
     * 使用5%的滞回带避免在切换点附近的频繁切换
     */
    float hyst = sl_erpm * 0.05f;  // 滞回带：切换阈值的5%
    if (loop->state.using_encoder) {
        // 当前使用编码器，检查是否需要切换到观测器
        if (rpm_abs > (sl_erpm + hyst)) {
            loop->state.using_encoder = FALSE;  // 切换到观测器模式
        }
    } else {
        // 当前使用观测器，检查是否需要切换到编码器
        if (rpm_abs < (sl_erpm - hyst)) {
            loop->state.using_encoder = TRUE;   // 切换到编码器模式
        }
    }

    // 根据当前模式返回相应的角度值
    return loop->state.using_encoder ? enc_angle : obs_angle;
}

/**
 * @brief 观测器更新主函数
 * @param loop 电流环结构体指针
 * @param dt 采样时间(s)
 * @return 更新结果
 */
ret_t observer_update(current_loop_t *loop, float dt)
{
    if (loop == NULL) {
        return RET_ERROR;
    }

    // 1. 更新观测器
    foc_observer_update(loop->state.v_alpha, loop->state.v_beta,
                       loop->state.i_alpha, loop->state.i_beta,
                       dt, &loop->state.observer, &loop->state.phase_now_observer, loop);

    // 2. 获取编码器角度
    loop->state.phase_now_encoder = loop->state.theta_elec_comp;

    // 3. 编码器/观测器切换
    float corrected_phase;
    if (loop->config.observer.enable_switch) {
        corrected_phase = foc_correct_encoder(loop->state.phase_now_observer,
                                            loop->state.phase_now_encoder,
                                            loop->state.omega_elec_filt,
                                            loop->config.observer.sl_erpm,
                                            loop);
    } else {
        // 根据传感器模式选择
        switch (loop->config.sensor_mode) {
            case SENSOR_MODE_ENCODER:
                corrected_phase = loop->state.phase_now_encoder;
                loop->state.using_encoder = TRUE;
                break;
            case SENSOR_MODE_OBSERVER:
                corrected_phase = loop->state.phase_now_observer;
                loop->state.using_encoder = FALSE;
                break;
            case SENSOR_MODE_HYBRID:
            default:
                corrected_phase = loop->state.phase_now_encoder;  // 默认使用编码器
                loop->state.using_encoder = TRUE;
                break;
        }
    }

    // 4. 运行PLL进行相位和速度平滑
    foc_pll_run(corrected_phase, dt, &loop->state.pll_phase, &loop->state.pll_speed, loop);

    // 5. 更新最终使用的角度和速度
    if (loop->config.sensor_mode == SENSOR_MODE_OBSERVER ||
        (!loop->state.using_encoder && loop->config.observer.enable_switch)) {
        // 使用观测器模式时，使用PLL输出
        loop->state.theta_elec = loop->state.pll_phase;
        loop->state.omega_elec_filt = loop->state.pll_speed;
    }

    return RET_OK;
}

/**
 * @brief 获取观测器相位
 * @param loop 电流环结构体指针
 * @return 观测器相位(rad)
 */
float observer_get_phase(const current_loop_t *loop)
{
    if (loop == NULL) {
        return 0.0f;
    }

    return loop->state.phase_now_observer;
}

/**
 * @brief 获取观测器速度
 * @param loop 电流环结构体指针
 * @return 观测器速度(rad/s)
 */
float observer_get_speed(const current_loop_t *loop)
{
    if (loop == NULL) {
        return 0.0f;
    }

    return loop->state.pll_speed;
}

/*============================ 电流环中断处理函数 ============================*/

/**
 * @brief 电流环中断处理函数
 * @note 在PWM中断或定时器中断中调用，频率为20kHz
 */
void motor_current_loop_interrupt_handler(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 执行电流环控制
    current_loop_execute(loop);
}

/**
 * @brief 电流环快速初始化函数
 * @note 使用默认参数快速初始化电流环
 * @return 初始化结果
 */
ret_t motor_current_loop_quick_init(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 初始化电流环
    ret_t result = current_loop_init(loop);
    if (result != RET_OK) {
        return result;
    }

    // 初始化硬件接口
    // 用户需要根据实际硬件实现这些函数
    // motor_pwm_init();
    // motor_adc_init();
    // motor_encoder_init();
    // motor_protection_init();
    // motor_interrupt_init();

    return RET_OK;
}

/**
 * @brief 电流环启动函数
 * @param target_current 目标电流(A)
 * @return 启动结果
 */
ret_t motor_current_loop_start(float target_current)
{
    current_loop_t *loop = current_loop_get_instance();

    // 设置目标电流
    ret_t result = current_loop_set_current_target(loop, target_current);
    if (result != RET_OK) {
        return result;
    }

    // 使能电流环
    return current_loop_enable(loop, TRUE);
}

/**
 * @brief 电流环停止函数
 * @return 停止结果
 */
ret_t motor_current_loop_stop(void)
{
    current_loop_t *loop = current_loop_get_instance();

    // 禁用电流环
    return current_loop_enable(loop, FALSE);
}
