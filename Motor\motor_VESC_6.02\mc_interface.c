/*
	Copyright 2016 - 2022 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

/*
 * 电机控制接口实现文件 (Motor Control Interface Implementation)
 *
 * 本文件实现了VESC电机控制系统的统一接口层，提供了对底层电机控制算法的
 * 高级抽象和封装。该接口层是应用层和底层控制算法之间的桥梁。
 *
 * 主要实现功能：
 * 1. 电机控制模式管理 - 占空比、电流、速度、位置控制的统一接口
 * 2. 状态监测和数据采集 - 实时电机参数监测和统计
 * 3. 故障检测和保护 - 多层次的安全保护机制
 * 4. 配置管理 - 电机参数的动态配置和验证
 * 5. 双电机支持 - 多电机系统的协调控制
 * 6. 线程安全 - 多线程环境下的安全访问
 *
 * 架构特点：
 * - 硬件抽象：统一接口适配不同的电机控制算法(BLDC/FOC)
 * - 实时性：高效的中断处理和低延迟控制
 * - 可靠性：完善的故障检测和恢复机制
 * - 可扩展性：模块化设计便于功能扩展
 *
 * 性能指标：
 * - 控制频率：20-30kHz (FOC模式)
 * - 响应时间：<50μs (电流控制)
 * - 精度：12位ADC，0.1%控制精度
 */

#include "mc_interface.h"
#include "mcpwm.h"
#include "mcpwm_foc.h"
#include "ledpwm.h"
#include "stm32f4xx_conf.h"
#include "hw.h"
#include "terminal.h"
#include "utils_math.h"
#include "utils_sys.h"
#include "ch.h"
#include "hal.h"
#include "commands.h"
#include "encoder.h"
#include "buffer.h"
#include "gpdrive.h"
#include "comm_can.h"
#include "shutdown.h"
#include "app.h"
#include "mempools.h"
#include "crc.h"
#include "bms.h"
#include "events.h"

#include <math.h>
#include <stdlib.h>
#include <string.h>

/*
 * ============================================================================
 * 宏定义 (Macro Definitions)
 * ============================================================================
 */

/**
 * 方向乘数宏
 *
 * 根据电机方向配置返回方向乘数：
 * - 正常方向：返回 +1.0
 * - 反向配置：返回 -1.0
 *
 * 用于统一处理电机正反转，简化代码中的方向判断
 */
#define DIR_MULT		(motor_now()->m_conf.m_invert_direction ? -1.0 : 1.0)

/*
 * ============================================================================
 * 全局变量 (Global Variables)
 * ============================================================================
 */

/**
 * ADC原始采样值数组
 *
 * 存储所有ADC通道的原始12位采样值，包括：
 * - 基本通道：电流、电压、温度等
 * - 扩展通道：额外的模拟信号
 *
 * 数组大小 = HW_ADC_CHANNELS + HW_ADC_CHANNELS_EXTRA
 */
volatile uint16_t ADC_Value[HW_ADC_CHANNELS + HW_ADC_CHANNELS_EXTRA];

/**
 * ADC电流归一化值数组
 *
 * 经过校准和归一化处理的电流采样值，包括：
 * [0-2]: 三相电流 (A)
 * [3-5]: 备用电流通道 (A)
 *
 * 归一化公式：I_norm = (ADC_raw - offset) × scale_factor
 */
volatile float ADC_curr_norm_value[6];

/**
 * 电机接口状态结构体
 *
 * 包含单个电机的所有状态信息、配置参数和统计数据。
 * 该结构体是电机控制接口的核心数据结构。
 */
typedef struct {
	// 基本配置和状态
	mc_configuration m_conf;				// 电机配置参数
	mc_fault_code m_fault_now;				// 当前故障代码
	setup_stats m_stats;					// 设置统计信息

	// 控制状态管理
	int m_ignore_iterations;				// 忽略输入的迭代次数
	int m_drv_fault_iterations;				// 驱动故障迭代计数
	unsigned int m_cycles_running;			// 运行周期计数
	bool m_lock_enabled;					// 锁定使能标志
	bool m_lock_override_once;				// 一次性锁定覆盖标志

	/*
	 * 电流统计累加器
	 *
	 * 用于计算平均电流值的累加器和计数器：
	 * 平均值 = sum / iterations
	 */
	float m_motor_current_sum;				// 电机电流累加和 (A·次)
	float m_input_current_sum;				// 输入电流累加和 (A·次)
	float m_motor_current_iterations;		// 电机电流采样次数
	float m_input_current_iterations;		// 输入电流采样次数

	/*
	 * dq轴电流统计累加器
	 *
	 * FOC控制中的dq轴电流统计：
	 * - d轴电流：磁通电流，用于弱磁控制
	 * - q轴电流：转矩电流，直接对应电机转矩
	 */
	float m_motor_id_sum;					// d轴电流累加和 (A·次)
	float m_motor_iq_sum;					// q轴电流累加和 (A·次)
	float m_motor_id_iterations;			// d轴电流采样次数
	float m_motor_iq_iterations;			// q轴电流采样次数

	/*
	 * dq轴电压统计累加器
	 *
	 * FOC控制中的dq轴电压统计，用于功率计算和系统分析
	 */
	float m_motor_vd_sum;					// d轴电压累加和 (V·次)
	float m_motor_vq_sum;					// q轴电压累加和 (V·次)
	float m_motor_vd_iterations;			// d轴电压采样次数
	float m_motor_vq_iterations;			// q轴电压采样次数

	/*
	 * 能量统计
	 *
	 * 累计能量消耗和回收统计：
	 * - amp_seconds: 安秒数 = ∫I(t)dt
	 * - watt_seconds: 瓦秒数 = ∫P(t)dt
	 */
	float m_amp_seconds;					// 累计安秒数(放电) (A·s)
	float m_amp_seconds_charged;			// 累计安秒数(充电) (A·s)
	float m_watt_seconds;					// 累计瓦秒数(放电) (W·s)
	float m_watt_seconds_charged;			// 累计瓦秒数(充电) (W·s)

	// 位置控制
	float m_position_set;					// 位置设定值 (弧度)

	/*
	 * 温度监测
	 *
	 * 系统温度监测用于过热保护和温度补偿
	 */
	float m_temp_fet;						// MOSFET温度 (°C)
	float m_temp_motor;						// 电机温度 (°C)
	float m_temp_override;					// 温度覆盖值 (°C)

	// 系统监测
	float m_gate_driver_voltage;			// 栅极驱动电压 (V)
	float m_motor_current_unbalance;		// 电机电流不平衡度
	float m_motor_current_unbalance_error_rate;	// 电流不平衡错误率
	float m_f_samp_now;						// 当前采样频率 (Hz)

	/*
	 * 电压滤波
	 *
	 * 多级电压滤波用于不同的控制和保护目的：
	 * - 快速滤波：用于实时控制
	 * - 慢速滤波：用于保护和显示
	 */
	float m_input_voltage_filtered;			// 输入电压(快速滤波) (V)
	float m_input_voltage_filtered_slower;	// 输入电压(慢速滤波) (V)

	/*
	 * 备份数据计数器
	 *
	 * 用于数据持久化和恢复的计数器
	 */
	uint64_t m_odometer_last;				// 上次里程计读数 (m)
	uint64_t m_runtime_last;				// 上次运行时间 (ms)
} motor_if_state_t;

/*
 * ============================================================================
 * 私有变量 (Private Variables)
 * ============================================================================
 */

/**
 * 电机接口状态变量
 *
 * 每个电机都有独立的状态管理结构体
 */
static volatile motor_if_state_t m_motor_1;		// 第一个电机状态
#ifdef HW_HAS_DUAL_MOTORS
static volatile motor_if_state_t m_motor_2;		// 第二个电机状态(双电机配置)
#endif

/*
 * ============================================================================
 * 采样变量 (Sampling Variables)
 * ============================================================================
 */

/**
 * ADC采样缓冲区最大长度
 *
 * 用于调试和数据分析的采样缓冲区大小
 */
#define ADC_SAMPLE_MAX_LEN		2000

/*
 * 采样数据缓冲区
 *
 * 使用RAM4段存储采样数据，提高访问速度。
 * 所有缓冲区都使用volatile修饰，确保在中断和主程序间的数据一致性。
 */
__attribute__((section(".ram4"))) static volatile int16_t m_curr0_samples[ADC_SAMPLE_MAX_LEN];	// 电流0采样
__attribute__((section(".ram4"))) static volatile int16_t m_curr1_samples[ADC_SAMPLE_MAX_LEN];	// 电流1采样
__attribute__((section(".ram4"))) static volatile int16_t m_ph1_samples[ADC_SAMPLE_MAX_LEN];		// 相电压1采样
__attribute__((section(".ram4"))) static volatile int16_t m_ph2_samples[ADC_SAMPLE_MAX_LEN];		// 相电压2采样
__attribute__((section(".ram4"))) static volatile int16_t m_ph3_samples[ADC_SAMPLE_MAX_LEN];		// 相电压3采样
__attribute__((section(".ram4"))) static volatile int16_t m_vzero_samples[ADC_SAMPLE_MAX_LEN];	// 零电压采样
__attribute__((section(".ram4"))) static volatile uint8_t m_status_samples[ADC_SAMPLE_MAX_LEN];	// 状态采样
__attribute__((section(".ram4"))) static volatile int16_t m_curr_fir_samples[ADC_SAMPLE_MAX_LEN];	// FIR滤波电流采样
__attribute__((section(".ram4"))) static volatile int16_t m_f_sw_samples[ADC_SAMPLE_MAX_LEN];		// 开关频率采样
__attribute__((section(".ram4"))) static volatile int8_t m_phase_samples[ADC_SAMPLE_MAX_LEN];		// 相位采样

/*
 * 采样控制变量
 *
 * 用于控制数据采样过程的状态变量
 */
static volatile int m_sample_len;					// 采样长度
static volatile int m_sample_int;					// 采样间隔(抽取比例)
static volatile bool m_sample_raw;					// 是否采样原始数据
static volatile debug_sampling_mode m_sample_mode;	// 当前采样模式
static volatile debug_sampling_mode m_sample_mode_last;	// 上次采样模式
static volatile int m_sample_now;					// 当前采样计数
static volatile int m_sample_trigger;				// 采样触发计数
static volatile float m_last_adc_duration_sample;	// 上次ADC持续时间采样
static volatile bool m_sample_is_second_motor;		// 是否为第二个电机采样
static volatile gnss_data m_gnss = {0};			// GNSS数据

/**
 * 故障数据本地结构体
 *
 * 用于在故障处理线程间传递故障信息
 */
typedef struct {
	bool is_second_motor;		// 是否为第二个电机
	mc_fault_code fault_code;	// 故障代码
	const char *info_str;		// 故障信息字符串
	int info_argn;				// 信息参数个数
	float info_args[2];			// 信息参数数组
} fault_data_local;

/**
 * 故障数据实例
 *
 * 存储当前故障的详细信息
 */
static volatile fault_data_local m_fault_data = {0, FAULT_CODE_NONE, 0, 0, {0, 0}};

/*
 * ============================================================================
 * 私有函数声明 (Private Function Declarations)
 * ============================================================================
 */

/**
 * 更新覆盖限制
 * 根据配置更新电机的各种限制参数
 */
static void update_override_limits(volatile motor_if_state_t *motor, volatile mc_configuration *conf);

/**
 * 运行定时器任务
 * 执行周期性的定时器任务，如统计更新、保护检查等
 */
static void run_timer_tasks(volatile motor_if_state_t *motor);

/**
 * 更新统计信息
 * 更新电机运行统计数据
 */
static void update_stats(volatile motor_if_state_t *motor);

/**
 * 获取当前电机状态指针
 * 根据当前选择的电机返回对应的状态结构体指针
 */
static volatile motor_if_state_t *motor_now(void);

/*
 * ============================================================================
 * 函数指针 (Function Pointers)
 * ============================================================================
 */

/**
 * PWM完成回调函数指针
 * 在PWM周期完成时调用的用户回调函数
 */
static void(*pwn_done_func)(void) = 0;

/**
 * 采样数据发送函数指针
 * 用于发送采样数据的回调函数
 */
static void(* volatile send_func_sample)(unsigned char *data, unsigned int len) = 0;

/*
 * ============================================================================
 * 线程定义 (Thread Definitions)
 * ============================================================================
 */

/**
 * 定时器线程
 * 执行周期性任务，如统计更新、温度监测等
 */
static THD_WORKING_AREA(timer_thread_wa, 512);		// 定时器线程工作区
static THD_FUNCTION(timer_thread, arg);			// 定时器线程函数

/**
 * 采样数据发送线程
 * 负责发送采样数据到上位机
 */
static THD_WORKING_AREA(sample_send_thread_wa, 512);	// 采样发送线程工作区
static THD_FUNCTION(sample_send_thread, arg);			// 采样发送线程函数
static thread_t *sample_send_tp;						// 采样发送线程指针

/**
 * 故障停止线程
 * 处理故障停止逻辑，确保电机安全停止
 */
static THD_WORKING_AREA(fault_stop_thread_wa, 512);	// 故障停止线程工作区
static THD_FUNCTION(fault_stop_thread, arg);			// 故障停止线程函数
static thread_t *fault_stop_tp;						// 故障停止线程指针

/**
 * 统计线程
 * 处理运行统计数据的计算和更新
 */
static THD_WORKING_AREA(stat_thread_wa, 512);			// 统计线程工作区
static THD_FUNCTION(stat_thread, arg);					// 统计线程函数

/**
 * 电机控制接口初始化函数
 *
 * 初始化电机控制系统的所有组件，包括：
 * 1. 电机状态结构体初始化
 * 2. 配置参数加载
 * 3. 硬件驱动初始化
 * 4. 系统线程启动
 * 5. 编码器初始化
 *
 * 该函数在系统启动时调用一次，完成整个电机控制系统的初始化。
 */
void mc_interface_init(void) {
	/*
	 * 电机状态结构体初始化
	 *
	 * 清零所有电机状态变量，确保系统从已知状态开始运行
	 */
	memset((void*)&m_motor_1, 0, sizeof(motor_if_state_t));
#ifdef HW_HAS_DUAL_MOTORS
	memset((void*)&m_motor_2, 0, sizeof(motor_if_state_t));
#endif

	/*
	 * 配置参数加载
	 *
	 * 从非易失性存储器加载电机配置参数
	 * - 第一个参数false表示加载第一个电机配置
	 * - 第二个参数true表示加载第二个电机配置
	 */
	conf_general_read_mc_configuration((mc_configuration*)&m_motor_1.m_conf, false);
#ifdef HW_HAS_DUAL_MOTORS
	conf_general_read_mc_configuration((mc_configuration*)&m_motor_2.m_conf, true);
#endif

	/*
	 * 双电机配置强制设置
	 *
	 * 在双电机系统中，强制使用FOC控制模式以获得最佳性能
	 */
#ifdef HW_HAS_DUAL_MOTORS
	m_motor_1.m_conf.motor_type = MOTOR_TYPE_FOC;
	m_motor_2.m_conf.motor_type = MOTOR_TYPE_FOC;
#endif

	/*
	 * 采样系统初始化
	 *
	 * 初始化数据采样和调试系统的参数
	 */
	m_last_adc_duration_sample = 0.0;		// ADC持续时间采样
	m_sample_len = 1000;					// 默认采样长度
	m_sample_int = 1;						// 默认采样间隔
	m_sample_now = 0;						// 当前采样计数
	m_sample_raw = false;					// 默认非原始数据
	m_sample_trigger = 0;					// 采样触发计数
	m_sample_mode = DEBUG_SAMPLING_OFF;		// 默认关闭调试采样
	m_sample_mode_last = DEBUG_SAMPLING_OFF;// 上次采样模式
	m_sample_is_second_motor = false;		// 默认第一个电机

	// 重置运行统计
	mc_interface_stat_reset();

	/*
	 * 系统线程启动
	 *
	 * 启动电机控制系统的各个功能线程：
	 * - timer_thread: 定时器线程，执行周期性任务
	 * - sample_send_thread: 采样数据发送线程
	 * - fault_stop_thread: 故障停止处理线程(高优先级)
	 * - stat_thread: 统计数据处理线程
	 */
	chThdCreateStatic(timer_thread_wa, sizeof(timer_thread_wa), NORMALPRIO, timer_thread, NULL);
	chThdCreateStatic(sample_send_thread_wa, sizeof(sample_send_thread_wa), NORMALPRIO - 1, sample_send_thread, NULL);
	chThdCreateStatic(fault_stop_thread_wa, sizeof(fault_stop_thread_wa), HIGHPRIO - 3, fault_stop_thread, NULL);
	chThdCreateStatic(stat_thread_wa, sizeof(stat_thread_wa), NORMALPRIO, stat_thread, NULL);

	/*
	 * 栅极驱动器初始化
	 *
	 * 配置MOSFET栅极驱动芯片的过流保护和其他参数。
	 * 支持多种驱动芯片：DRV8301、DRV8320S、DRV8323S
	 */
	int motor_old = mc_interface_get_motor_thread();	// 保存当前电机选择

	// 第一个电机的驱动器配置
	mc_interface_select_motor_thread(1);
#ifdef HW_HAS_DRV8301
	// DRV8301栅极驱动器配置
	drv8301_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);	// 过流保护模式
	drv8301_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);	// 过流阈值调整
#elif defined(HW_HAS_DRV8320S)
	// DRV8320S栅极驱动器配置
	drv8320s_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);
	drv8320s_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8323S)
	// DRV8323S栅极驱动器配置
	drv8323s_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);
	drv8323s_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);
	DRV8323S_CUSTOM_SETTINGS();	// 应用自定义设置
#endif

	/*
	 * 第二个电机的驱动器配置(双电机系统)
	 *
	 * 在双电机或双并联配置中，需要分别配置两个驱动器
	 */
#if defined HW_HAS_DUAL_MOTORS || defined HW_HAS_DUAL_PARALLEL
	mc_interface_select_motor_thread(2);
#ifdef HW_HAS_DRV8301
	drv8301_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);
	drv8301_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8320S)
	drv8320s_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);
	drv8320s_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8323S)
	drv8323s_set_oc_mode(motor_now()->m_conf.m_drv8301_oc_mode);
	drv8323s_set_oc_adj(motor_now()->m_conf.m_drv8301_oc_adj);
	DRV8323S_CUSTOM_SETTINGS();
#endif
#endif

	// 恢复原来的电机选择
	mc_interface_select_motor_thread(motor_old);

	/*
	 * 编码器初始化
	 *
	 * 初始化位置传感器系统，包括：
	 * - 增量编码器
	 * - 绝对编码器
	 * - 霍尔传感器
	 * - 解析器等
	 */
	encoder_init(&motor_now()->m_conf);

	/*
	 * 电机控制算法初始化
	 *
	 * 根据配置的电机类型初始化相应的控制算法：
	 * - BLDC/DC: 传统的六步换相控制
	 * - FOC: 磁场定向控制，提供更高的效率和性能
	 * - GPD: 通用PWM驱动，用于特殊应用
	 */
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		/*
		 * BLDC/DC电机控制初始化
		 *
		 * 初始化传统的六步换相控制算法，适用于：
		 * - 有刷直流电机
		 * - 无刷直流电机(方波控制)
		 * - 成本敏感的应用
		 */
		mcpwm_init(&motor_now()->m_conf);
		break;

	case MOTOR_TYPE_FOC:
		/*
		 * FOC控制初始化
		 *
		 * 初始化磁场定向控制算法，提供：
		 * - 高效率和低噪音
		 * - 精确的转矩控制
		 * - 无传感器运行能力
		 * - 高动态响应性能
		 */
#ifdef HW_HAS_DUAL_MOTORS
		// 双电机配置：分别传入两个电机的配置参数
		mcpwm_foc_init((mc_configuration*)&m_motor_1.m_conf, (mc_configuration*)&m_motor_2.m_conf);
#else
		// 单电机配置：两个参数使用相同的配置
		mcpwm_foc_init((mc_configuration*)&m_motor_1.m_conf, (mc_configuration*)&m_motor_1.m_conf);
#endif
		break;

	case MOTOR_TYPE_GPD:
		/*
		 * GPD(通用PWM驱动)初始化
		 *
		 * 用于特殊的PWM驱动应用，如：
		 * - 步进电机驱动
		 * - 特殊的执行器控制
		 * - 自定义PWM输出
		 */
		gpdrive_init(&motor_now()->m_conf);
		break;

	default:
		// 未知电机类型，不进行初始化
		break;
	}

	/*
	 * BMS(电池管理系统)初始化
	 *
	 * 初始化电池管理系统，提供：
	 * - 电池电压监测
	 * - 电池温度监测
	 * - 充放电保护
	 * - 电池均衡管理
	 */
	bms_init((bms_config*)&m_motor_1.m_conf.bms);
}

/**
 * 获取当前活动电机编号
 *
 * 在双电机系统中，确定当前操作的电机编号。
 * 优先级顺序：
 * 1. 中断服务程序中的电机选择(最高优先级)
 * 2. 线程中的电机选择
 * 3. 默认电机1
 *
 * 这种优先级设计确保了中断处理的实时性和正确性。
 *
 * @return 当前电机编号 (1=电机1, 2=电机2)
 */
int mc_interface_motor_now(void) {
#if defined HW_HAS_DUAL_MOTORS || defined HW_HAS_DUAL_PARALLEL
	// 获取中断服务程序中选择的电机
	int isr_motor = mcpwm_foc_isr_motor();
	// 获取当前线程选择的电机
	int thd_motor = chThdGetSelfX()->motor_selected;

	if (isr_motor > 0) {
		// 中断中有电机选择，优先使用(确保实时性)
		return isr_motor;
	} else if (thd_motor > 0) {
		// 线程中有电机选择，次优先使用
		return thd_motor;
	} else {
		// 默认使用电机1
		return 1;
	}
#else
	// 单电机系统，始终返回电机1
	return 1;
#endif
}

/**
 * 为当前线程选择电机
 *
 * 在双电机硬件中，为当前线程设置要操作的电机。
 * 当线程选择了电机后，该线程中的所有mc_interface函数
 * 都将操作指定的电机。
 *
 * 线程电机选择机制的优势：
 * 1. 线程安全：每个线程可以独立操作不同的电机
 * 2. 代码简洁：无需在每个函数调用中指定电机
 * 3. 上下文保持：线程的电机选择在整个生命周期内保持
 *
 * @param motor 电机选择参数
 *   0: 不选择特定电机，使用上次选择的电机
 *   1: 选择电机1 (默认)
 *   2: 选择电机2
 */
void mc_interface_select_motor_thread(int motor) {
#if defined HW_HAS_DUAL_MOTORS || defined HW_HAS_DUAL_PARALLEL
	// 验证电机编号的有效性
	if (motor == 0 || motor == 1 || motor == 2) {
		// 将电机选择存储在当前线程的控制块中
		chThdGetSelfX()->motor_selected = motor;
	}
#else
	// 单电机系统，忽略参数
	(void)motor;
#endif
}

/**
 * 获取当前线程选择的电机编号
 *
 * 返回当前线程设置的电机选择状态。
 *
 * @return 电机选择状态
 *   0: 未选择特定电机，将使用上次选择的电机
 *   1: 已选择电机1 (默认)
 *   2: 已选择电机2
 */
int mc_interface_get_motor_thread(void) {
	return chThdGetSelfX()->motor_selected;
}

/**
 * 获取当前电机的配置参数
 *
 * 返回当前选择电机的配置参数结构体指针。
 * 该指针指向易失性数据，确保读取到最新的配置。
 *
 * @return 指向当前电机配置的常量易失性指针
 */
const volatile mc_configuration* mc_interface_get_configuration(void) {
	return &motor_now()->m_conf;
}

/**
 * 设置电机配置参数
 *
 * 动态更新电机配置参数并应用到控制系统。
 * 该函数会智能地处理配置变更，只重新初始化必要的组件。
 *
 * 配置更新流程：
 * 1. 检查传感器端口模式变化
 * 2. 更新栅极驱动器设置
 * 3. 检查电机类型变化
 * 4. 重新初始化相关组件
 * 5. 应用新配置参数
 *
 * @param configuration 新的电机配置参数指针
 */
void mc_interface_set_configuration(mc_configuration *configuration) {
	volatile motor_if_state_t *motor = motor_now();

	/*
	 * 双电机系统强制FOC模式
	 *
	 * 在双电机或双并联系统中，强制使用FOC控制模式
	 * 以确保最佳的性能和同步性
	 */
#if defined HW_HAS_DUAL_MOTORS || defined HW_HAS_DUAL_PARALLEL
	configuration->motor_type = MOTOR_TYPE_FOC;
#endif

	/*
	 * 编码器配置更新
	 *
	 * 检查传感器端口模式是否发生变化：
	 * - 如果模式改变：完全重新初始化编码器
	 * - 如果模式未变：仅更新配置参数
	 */
	if (motor->m_conf.m_sensor_port_mode != configuration->m_sensor_port_mode) {
		encoder_deinit();				// 反初始化编码器
		encoder_init(configuration);	// 用新配置重新初始化
	} else {
		encoder_update_config(configuration);	// 仅更新配置
	}

	/*
	 * 栅极驱动器配置更新
	 *
	 * 根据硬件类型更新相应的栅极驱动芯片设置：
	 * - 过流保护模式
	 * - 过流阈值调整
	 */
#ifdef HW_HAS_DRV8301
	drv8301_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8301_set_oc_adj(configuration->m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8320S)
	drv8320s_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8320s_set_oc_adj(configuration->m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8323S)
	drv8323s_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8323s_set_oc_adj(configuration->m_drv8301_oc_adj);
#endif

	/*
	 * 双并联系统的第二个驱动器配置
	 *
	 * 在双并联配置中，需要同时配置两个驱动器
	 * 以确保并联电机的同步运行
	 */
#ifdef HW_HAS_DUAL_PARALLEL
	mc_interface_select_motor_thread(2);	// 切换到第二个电机
#ifdef HW_HAS_DRV8301
	drv8301_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8301_set_oc_adj(configuration->m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8320S)
	drv8320s_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8320s_set_oc_adj(configuration->m_drv8301_oc_adj);
#elif defined(HW_HAS_DRV8323S)
	drv8323s_set_oc_mode(configuration->m_drv8301_oc_mode);
	drv8323s_set_oc_adj(configuration->m_drv8301_oc_adj);
#endif
	mc_interface_select_motor_thread(1);	// 切换回第一个电机
#endif

	/*
	 * 电机类型变更处理
	 *
	 * 当电机类型发生变化时，需要完全重新初始化控制系统：
	 * 1. 反初始化所有控制模块
	 * 2. 应用新配置
	 * 3. 根据新类型初始化相应模块
	 */
	if (motor->m_conf.motor_type != configuration->motor_type) {
		// 反初始化所有可能的控制模块
		mcpwm_deinit();			// BLDC/DC控制反初始化
		mcpwm_foc_deinit();		// FOC控制反初始化
		gpdrive_deinit();		// GPD控制反初始化

		// 应用新配置
		motor->m_conf = *configuration;

		// 根据新的电机类型初始化相应的控制模块
		switch (motor->m_conf.motor_type) {
		case MOTOR_TYPE_BLDC:
		case MOTOR_TYPE_DC:
			// 初始化BLDC/DC控制
			mcpwm_init(&motor->m_conf);
			break;

		case MOTOR_TYPE_FOC:
			// 初始化FOC控制
#ifdef HW_HAS_DUAL_MOTORS
			mcpwm_foc_init((mc_configuration*)&m_motor_1.m_conf, (mc_configuration*)&m_motor_2.m_conf);
#else
			mcpwm_foc_init((mc_configuration*)&m_motor_1.m_conf, (mc_configuration*)&m_motor_1.m_conf);
#endif
			break;

		case MOTOR_TYPE_GPD:
			// 初始化GPD控制
			gpdrive_init(&motor->m_conf);
			break;

		default:
			// 未知类型，不进行初始化
			break;
		}
	} else {
		/*
		 * 电机类型未变更
		 *
		 * 直接应用新配置，无需重新初始化控制模块
		 */
		motor->m_conf = *configuration;
	}

	/*
	 * 更新覆盖限制
	 *
	 * 根据新配置更新各种保护限制参数，包括：
	 * - 电流限制
	 * - 电压限制
	 * - 温度限制
	 * - 速度限制等
	 */
	update_override_limits(motor, &motor->m_conf);

	/*
	 * 应用配置到相应的控制模块
	 *
	 * 根据电机类型将配置参数应用到对应的控制算法
	 */
	switch (motor->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// 应用BLDC/DC控制配置
		mcpwm_set_configuration(&motor->m_conf);
		break;

	case MOTOR_TYPE_FOC:
		/*
		 * FOC配置同步(双电机系统)
		 *
		 * 在双电机系统中，某些FOC参数需要在两个电机间同步，
		 * 如零矢量频率(foc_f_zv)，以确保系统的协调运行
		 */
#ifdef HW_HAS_DUAL_MOTORS
		if (motor == &m_motor_1) {
			// 电机1的零矢量频率同步到电机2
			m_motor_2.m_conf.foc_f_zv = motor->m_conf.foc_f_zv;
		} else {
			// 电机2的零矢量频率同步到电机1
			m_motor_1.m_conf.foc_f_zv = motor->m_conf.foc_f_zv;
		}
#endif
		// 应用FOC控制配置
		mcpwm_foc_set_configuration((mc_configuration*)&motor->m_conf);
		break;

	case MOTOR_TYPE_GPD:
		// 应用GPD控制配置
		gpdrive_set_configuration(&motor->m_conf);
		break;

	default:
		// 未知类型，不进行配置
		break;
	}

	/*
	 * 重新初始化BMS
	 *
	 * 使用新的BMS配置重新初始化电池管理系统
	 */
	bms_init(&configuration->bms);
}

/**
 * 检查直流校准是否完成
 *
 * 直流校准(DC Calibration)用于消除ADC采样的直流偏移，
 * 这对于精确的电流测量至关重要。
 *
 * 校准过程：
 * 1. 在电机静止状态下采样电流
 * 2. 计算各相电流的直流偏移
 * 3. 将偏移值存储用于后续补偿
 * 4. 校准完成后才能开始正常控制
 *
 * @return true=校准完成，可以开始电机控制
 *         false=校准进行中，不能启动电机
 */
bool mc_interface_dccal_done(void) {
	bool ret = false;

	// 根据电机类型查询相应控制模块的校准状态
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// 查询BLDC/DC控制模块的校准状态
		ret = mcpwm_is_dccal_done();
		break;

	case MOTOR_TYPE_FOC:
		// 查询FOC控制模块的校准状态
		ret = mcpwm_foc_is_dccal_done();
		break;

	case MOTOR_TYPE_GPD:
		// 查询GPD控制模块的校准状态
		ret = gpdrive_is_dccal_done();
		break;

	default:
		// 未知类型，默认为未完成
		break;
	}

	return ret;
}

/**
 * 设置PWM周期完成回调函数
 *
 * 注册一个在每个PWM周期完成后调用的回调函数。
 * 这个机制允许用户代码在精确的时间点执行操作。
 *
 * 重要提示：
 * - 回调函数在中断上下文中执行
 * - 必须保持函数简短和高效
 * - 避免在回调中执行阻塞操作
 * - 调用频率等于PWM频率(通常20-30kHz)
 *
 * 典型应用：
 * - 高精度数据采集
 * - 实时控制算法
 * - 同步外部设备
 *
 * @param p_func 回调函数指针，传入0将取消回调
 */
void mc_interface_set_pwm_callback(void (*p_func)(void)) {
	pwn_done_func = p_func;
}

/**
 * 锁定电机控制
 *
 * 禁用所有电机控制命令，用于安全保护。
 * 当系统检测到危险状态时，可以锁定控制以防止意外操作。
 *
 * 锁定状态下：
 * - 所有控制命令将被忽略
 * - 电机将保持当前状态或停止
 * - 需要显式解锁才能恢复控制
 */
void mc_interface_lock(void) {
	motor_now()->m_lock_enabled = true;
}

/**
 * 解锁电机控制
 *
 * 重新启用所有电机控制命令。
 * 解锁后系统将恢复正常的控制响应。
 */
void mc_interface_unlock(void) {
	motor_now()->m_lock_enabled = false;
}

/**
 * 一次性锁定覆盖
 *
 * 在锁定状态下允许执行一次控制命令。
 * 这个机制用于在安全锁定状态下执行必要的操作，
 * 如紧急停止或安全位置移动。
 *
 * 特点：
 * - 只允许执行一次命令
 * - 执行后自动恢复锁定状态
 * - 提供受控的安全操作能力
 */
void mc_interface_lock_override_once(void) {
	motor_now()->m_lock_override_once = true;
}

/**
 * 获取当前故障代码
 *
 * 返回当前电机的故障状态代码。
 * 故障代码用于诊断系统问题和触发保护机制。
 *
 * @return 当前故障代码枚举值
 */
mc_fault_code mc_interface_get_fault(void) {
	return motor_now()->m_fault_now;
}

/**
 * 将故障代码转换为字符串描述
 *
 * 将故障代码枚举值转换为可读的字符串描述，
 * 用于调试、日志记录和用户界面显示。
 *
 * 故障类型分类：
 * 1. 电压故障：过压、欠压
 * 2. 电流故障：过流、电流不平衡
 * 3. 温度故障：MOSFET过热、电机过热
 * 4. 硬件故障：驱动器故障、传感器故障
 * 5. 系统故障：看门狗复位、Flash损坏
 *
 * @param fault 故障代码枚举值
 * @return 故障描述字符串
 */
const char* mc_interface_fault_to_string(mc_fault_code fault) {
	switch (fault) {
	// 正常状态
	case FAULT_CODE_NONE: return "FAULT_CODE_NONE"; break;

	// 电压相关故障
	case FAULT_CODE_OVER_VOLTAGE: return "FAULT_CODE_OVER_VOLTAGE"; break;				// 过电压
	case FAULT_CODE_UNDER_VOLTAGE: return "FAULT_CODE_UNDER_VOLTAGE"; break;			// 欠电压
	case FAULT_CODE_GATE_DRIVER_OVER_VOLTAGE: return "FAULT_CODE_GATE_DRIVER_OVER_VOLTAGE"; break;	// 栅极驱动过压
	case FAULT_CODE_GATE_DRIVER_UNDER_VOLTAGE: return "FAULT_CODE_GATE_DRIVER_UNDER_VOLTAGE"; break;	// 栅极驱动欠压
	case FAULT_CODE_MCU_UNDER_VOLTAGE: return "FAULT_CODE_MCU_UNDER_VOLTAGE"; break;	// MCU欠电压

	// 电流相关故障
	case FAULT_CODE_ABS_OVER_CURRENT: return "FAULT_CODE_ABS_OVER_CURRENT"; break;		// 绝对过电流
	case FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_1: return "FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_1"; break;	// 电流传感器1偏移过大
	case FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_2: return "FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_2"; break;	// 电流传感器2偏移过大
	case FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_3: return "FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_3"; break;	// 电流传感器3偏移过大
	case FAULT_CODE_UNBALANCED_CURRENTS: return "FAULT_CODE_UNBALANCED_CURRENTS"; break;	// 电流不平衡

	// 温度相关故障
	case FAULT_CODE_OVER_TEMP_FET: return "FAULT_CODE_OVER_TEMP_FET"; break;			// MOSFET过热
	case FAULT_CODE_OVER_TEMP_MOTOR: return "FAULT_CODE_OVER_TEMP_MOTOR"; break;		// 电机过热

	// 硬件驱动故障
	case FAULT_CODE_DRV: return "FAULT_CODE_DRV"; break;								// 驱动器故障

	// 编码器相关故障
	case FAULT_CODE_ENCODER_SPI: return "FAULT_CODE_ENCODER_SPI"; break;				// 编码器SPI通信故障
	case FAULT_CODE_ENCODER_SINCOS_BELOW_MIN_AMPLITUDE: return "FAULT_CODE_ENCODER_SINCOS_BELOW_MIN_AMPLITUDE"; break;	// Sin/Cos编码器幅值过小
	case FAULT_CODE_ENCODER_SINCOS_ABOVE_MAX_AMPLITUDE: return "FAULT_CODE_ENCODER_SINCOS_ABOVE_MAX_AMPLITUDE"; break;	// Sin/Cos编码器幅值过大

	// 系统相关故障
	case FAULT_CODE_BOOTING_FROM_WATCHDOG_RESET: return "FAULT_CODE_BOOTING_FROM_WATCHDOG_RESET"; break;	// 看门狗复位启动
	case FAULT_CODE_FLASH_CORRUPTION: return "FAULT_CODE_FLASH_CORRUPTION"; break;		// Flash损坏
	case FAULT_CODE_FLASH_CORRUPTION_APP_CFG: return "FAULT_CODE_FLASH_CORRUPTION_APP_CFG"; break;	// 应用配置Flash损坏
	case FAULT_CODE_FLASH_CORRUPTION_MC_CFG: return "FAULT_CODE_FLASH_CORRUPTION_MC_CFG"; break;	// 电机配置Flash损坏
    case FAULT_CODE_BRK: return "FAULT_CODE_BRK";
    case FAULT_CODE_RESOLVER_LOT: return "FAULT_CODE_RESOLVER_LOT";
    case FAULT_CODE_RESOLVER_DOS: return "FAULT_CODE_RESOLVER_DOS";
    case FAULT_CODE_RESOLVER_LOS: return "FAULT_CODE_RESOLVER_LOS";
    case FAULT_CODE_ENCODER_NO_MAGNET: return "FAULT_CODE_ENCODER_NO_MAGNET";
    case FAULT_CODE_ENCODER_MAGNET_TOO_STRONG: return "FAULT_CODE_ENCODER_MAGNET_TOO_STRONG";
    case FAULT_CODE_PHASE_FILTER: return "FAULT_CODE_PHASE_FILTER";
    case FAULT_CODE_ENCODER_FAULT: return "FAULT_CODE_ENCODER_FAULT";
	case FAULT_CODE_LV_OUTPUT_FAULT: return "FAULT_CODE_LV_OUTPUT_FAULT";
	}

	return "Unknown fault";
}

mc_state mc_interface_get_state(void) {
	mc_state ret = MC_STATE_OFF;
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_state();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_state();
		break;

	default:
		break;
	}

	return ret;
}

mc_control_mode mc_interface_get_control_mode(void) {
	mc_control_mode ret = CONTROL_MODE_NONE;
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_control_mode();
		break;

	default:
		break;
	}
	return ret;
}

/**
 * 设置占空比控制
 *
 * 设置电机的PWM占空比进行开环控制。
 * 占空比直接控制输出电压的平均值。
 *
 * 控制原理：
 * V_avg = duty_cycle × V_bus
 * 其中：
 * - V_avg: 平均输出电压
 * - duty_cycle: 占空比 (-1.0 到 +1.0)
 * - V_bus: 总线电压
 *
 * 安全机制：
 * 1. 输入验证和锁定检查
 * 2. 方向乘数处理
 * 3. 关机保护重置
 * 4. 事件记录
 *
 * @param dutyCycle 占空比 (-1.0到+1.0，正值=正转，负值=反转)
 */
void mc_interface_set_duty(float dutyCycle) {
	/*
	 * 关机保护重置
	 *
	 * 当占空比大于阈值时，重置关机保护计时器，
	 * 表明系统正在正常运行
	 */
	if (fabsf(dutyCycle) > 0.001) {
		SHUTDOWN_RESET();
	}

	/*
	 * 输入验证
	 *
	 * 检查系统是否允许接受控制输入：
	 * - 锁定状态检查
	 * - 故障状态检查
	 * - 忽略输入状态检查
	 */
	if (mc_interface_try_input()) {
		return;
	}

	/*
	 * 根据电机类型设置占空比
	 *
	 * 应用方向乘数(DIR_MULT)处理电机方向配置
	 */
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// BLDC/DC电机占空比控制
		mcpwm_set_duty(DIR_MULT * dutyCycle);
		break;

	case MOTOR_TYPE_FOC:
		// FOC电机占空比控制
		mcpwm_foc_set_duty(DIR_MULT * dutyCycle);
		break;

	default:
		// 未支持的电机类型
		break;
	}

	// 记录控制事件用于调试和分析
	events_add("set_duty", dutyCycle);
}

/**
 * 设置占空比控制(无斜坡)
 *
 * 立即设置电机的PWM占空比，不使用斜坡过渡。
 * 与普通占空比控制的区别在于没有渐变过程。
 *
 * 应用场景：
 * - 需要快速响应的控制
 * - 测试和调试
 * - 特殊的控制算法
 *
 * 注意事项：
 * - 突然的占空比变化可能产生电流冲击
 * - 可能引起机械振动
 * - 建议仅在必要时使用
 *
 * @param dutyCycle 占空比 (-1.0到+1.0)
 */
void mc_interface_set_duty_noramp(float dutyCycle) {
	// 关机保护重置
	if (fabsf(dutyCycle) > 0.001) {
		SHUTDOWN_RESET();
	}

	// 输入验证
	if (mc_interface_try_input()) {
		return;
	}

	// 根据电机类型设置占空比(无斜坡)
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		mcpwm_set_duty_noramp(DIR_MULT * dutyCycle);
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_duty_noramp(DIR_MULT * dutyCycle);
		break;

	default:
		break;
	}

	// 记录控制事件
	events_add("set_duty_noramp", dutyCycle);
}

/**
 * 设置PID速度控制
 *
 * 使用PID控制器实现电机速度闭环控制。
 *
 * PID控制原理：
 * u(t) = Kp×e(t) + Ki×∫e(t)dt + Kd×de(t)/dt
 * 其中：
 * - e(t) = rpm_set - rpm_actual (速度误差)
 * - Kp: 比例增益
 * - Ki: 积分增益
 * - Kd: 微分增益
 *
 * 控制特点：
 * - 自动调节输出以达到目标速度
 * - 具有良好的稳态精度
 * - 能够抑制负载扰动
 * - 响应速度可通过PID参数调节
 *
 * @param rpm 目标转速 (RPM，正值=正转，负值=反转)
 */
void mc_interface_set_pid_speed(float rpm) {
	// 关机保护重置
	if (fabsf(rpm) > 0.001) {
		SHUTDOWN_RESET();
	}

	// 输入验证
	if (mc_interface_try_input()) {
		return;
	}

	// 根据电机类型设置PID速度控制
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// BLDC/DC电机PID速度控制
		mcpwm_set_pid_speed(DIR_MULT * rpm);
		break;

	case MOTOR_TYPE_FOC:
		// FOC电机PID速度控制
		mcpwm_foc_set_pid_speed(DIR_MULT * rpm);
		break;

	default:
		break;
	}

	// 记录控制事件
	events_add("set_pid_speed", rpm);
}

/**
 * 设置PID位置控制
 *
 * 使用PID控制器实现电机位置闭环控制。
 * 位置控制是最高精度的控制模式，适用于精确定位应用。
 *
 * 位置处理流程：
 * 1. 应用位置偏移补偿
 * 2. 应用方向乘数
 * 3. 应用编码器反向配置
 * 4. 角度归一化处理
 *
 * 控制特点：
 * - 高精度位置控制
 * - 支持多圈定位
 * - 自动角度归一化
 * - 编码器方向补偿
 *
 * @param pos 目标位置 (弧度)
 */
void mc_interface_set_pid_pos(float pos) {
	// 位置控制总是重置关机保护
	SHUTDOWN_RESET();

	// 输入验证
	if (mc_interface_try_input()) {
		return;
	}

	volatile mc_configuration *conf = &motor_now()->m_conf;

	// 保存原始设定位置
	motor_now()->m_position_set = pos;

	/*
	 * 位置变换处理
	 *
	 * 按顺序应用各种位置变换：
	 * 1. 位置偏移补偿
	 * 2. 方向乘数
	 * 3. 编码器反向
	 */
	pos += motor_now()->m_conf.p_pid_offset;	// 应用位置偏移
	pos *= DIR_MULT;							// 应用方向乘数

	// 编码器反向处理
	if (encoder_is_configured()) {
		if (conf->foc_encoder_inverted) {
			pos *= -1.0;
		}
	}

	/*
	 * 角度归一化
	 *
	 * 将角度归一化到 [-π, π] 范围内，
	 * 确保角度计算的一致性和精度
	 */
	utils_norm_angle(&pos);

	// 根据电机类型设置PID位置控制
	switch (conf->motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		mcpwm_set_pid_pos(pos);
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_pid_pos(pos);
		break;

	default:
		break;
	}

	events_add("set_pid_pos", pos);
}

/**
 * 设置电流控制
 *
 * 直接控制电机电流，提供精确的转矩控制。
 * 电流控制是最基本和最重要的控制模式。
 *
 * 转矩关系：
 * T = Kt × I
 * 其中：
 * - T: 电机转矩 (N·m)
 * - Kt: 转矩常数 (N·m/A)
 * - I: 电机电流 (A)
 *
 * 控制特点：
 * - 直接转矩控制
 * - 快速响应(通常<1ms)
 * - 高精度
 * - 良好的线性特性
 *
 * 应用场景：
 * - 转矩控制应用
 * - 力控制系统
 * - 高动态响应要求
 *
 * @param current 目标电流 (A，正值=正转转矩，负值=反转转矩)
 */
void mc_interface_set_current(float current) {
	// 关机保护重置
	if (fabsf(current) > 0.001) {
		SHUTDOWN_RESET();
	}

	// 输入验证
	if (mc_interface_try_input()) {
		return;
	}

	// 根据电机类型设置电流控制
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// BLDC/DC电机电流控制
		mcpwm_set_current(DIR_MULT * current);
		break;

	case MOTOR_TYPE_FOC:
		// FOC电机电流控制(q轴电流)
		mcpwm_foc_set_current(DIR_MULT * current);
		break;

	default:
		break;
	}

	// 记录控制事件
	events_add("set_current", current);
}

/**
 * 设置制动电流
 *
 * 设置再生制动电流，将电机的动能转换为电能回馈到电池。
 * 制动电流控制提供精确的制动力控制。
 *
 * 制动原理：
 * - 电机作为发电机运行
 * - 动能转换为电能
 * - 电能回馈到电源或消耗在制动电阻上
 * - 制动力与制动电流成正比
 *
 * 制动特点：
 * - 能量回收
 * - 精确的制动力控制
 * - 无机械磨损
 * - 响应快速
 *
 * 应用场景：
 * - 电动车辆制动
 * - 起重机械下降控制
 * - 精确停止定位
 *
 * @param current 制动电流 (A，正值，制动力大小)
 */
void mc_interface_set_brake_current(float current) {
	// 关机保护重置
	if (fabsf(current) > 0.001) {
		SHUTDOWN_RESET();
	}

	// 输入验证
	if (mc_interface_try_input()) {
		return;
	}

	// 根据电机类型设置制动电流
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// BLDC/DC电机制动电流控制
		mcpwm_set_brake_current(DIR_MULT * current);
		break;

	case MOTOR_TYPE_FOC:
		// FOC电机制动电流控制
		mcpwm_foc_set_brake_current(DIR_MULT * current);
		break;

	case MOTOR_TYPE_GPD:
		/*
		 * GPD模式制动
		 *
		 * 通过停止PWM输出实现制动，
		 * 依靠超时机制停止输出
		 */
		gpdrive_set_mode(GPD_OUTPUT_MODE_NONE);
		break;

	default:
		break;
	}

	// 记录制动事件
	events_add("set_current_brake", current);
}

/**
 * Set current relative to the minimum and maximum current limits.
 *
 * @param current
 * The relative current value, range [-1.0 1.0]
 */
void mc_interface_set_current_rel(float val) {
	if (fabsf(val) > 0.001) {
		SHUTDOWN_RESET();
	}

	mc_interface_set_current(val * motor_now()->m_conf.lo_current_motor_max_now);
}

/**
 * Set brake current relative to the minimum current limit.
 *
 * @param current
 * The relative current value, range [0.0 1.0]
 */
void mc_interface_set_brake_current_rel(float val) {
	if (fabsf(val) > 0.001) {
		SHUTDOWN_RESET();
	}

	mc_interface_set_brake_current(val * fabsf(motor_now()->m_conf.lo_current_motor_min_now));
}

/**
 * Set open loop current vector to brake motor.
 *
 * @param current
 * The current value.
 */
void mc_interface_set_handbrake(float current) {
	if (fabsf(current) > 0.001) {
		SHUTDOWN_RESET();
	}

	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// TODO: Not implemented yet, use brake mode for now.
		mcpwm_set_brake_current(current);
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_handbrake(current);
		break;

	default:
		break;
	}

	events_add("set_handbrake", current);
}

/**
 * Set handbrake brake current relative to the minimum current limit.
 *
 * @param current
 * The relative current value, range [0.0 1.0]
 */
void mc_interface_set_handbrake_rel(float val) {
	if (fabsf(val) > 0.001) {
		SHUTDOWN_RESET();
	}

	mc_interface_set_handbrake(val * fabsf(motor_now()->m_conf.lo_current_motor_min_now));
}

void mc_interface_set_openloop_current(float current, float rpm) {
	if (fabsf(current) > 0.001) {
		SHUTDOWN_RESET();
	}

	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_openloop_current(current, DIR_MULT * rpm);
		break;

	default:
		break;
	}

	events_add("set_openloop_current", current);
}
void mc_interface_set_openloop_phase(float current, float phase){
	if (fabsf(current) > 0.001) {
		SHUTDOWN_RESET();
	}

	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_openloop_phase(current, DIR_MULT * phase);
		break;

	default:
		break;
	}

	events_add("set_openloop_phase", phase);
}
void mc_interface_set_openloop_duty(float dutyCycle, float rpm){
	if (fabsf(dutyCycle) > 0.001) {
		SHUTDOWN_RESET();
	}

	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_openloop_duty(dutyCycle, DIR_MULT * rpm);
		break;

	default:
		break;
	}

	events_add("set_openloop_duty", dutyCycle);
}
void mc_interface_set_openloop_duty_phase(float dutyCycle, float phase){
	if (fabsf(dutyCycle) > 0.001) {
		SHUTDOWN_RESET();
	}

	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_openloop_duty_phase(dutyCycle, phase); // Should this use DIR_MULT?
		break;

	default:
		break;
	}

	events_add("set_openloop_duty_phase", phase);
}

void mc_interface_brake_now(void) {
	SHUTDOWN_RESET();

	mc_interface_set_duty(0.0);
}

/**
 * Disconnect the motor and let it turn freely.
 */
void mc_interface_release_motor(void) {
	if (mc_interface_try_input()) {
		return;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		mcpwm_release_motor();
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_release_motor();
		break;

	default:
		break;
	}

	events_add("release_motor", 0.0);
}

void mc_interface_release_motor_override(void) {
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		mcpwm_release_motor();
		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_release_motor();
		break;

	default:
		break;
	}

	events_add("release_motor_override", 0.0);
}

bool mc_interface_wait_for_motor_release(float timeout) {
	systime_t time_start = chVTGetSystemTimeX();
	bool res = false;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		while (UTILS_AGE_S(time_start) < timeout) {
			if (mcpwm_get_state() == MC_STATE_OFF) {
				res = true;
				break;
			}

			chThdSleepMilliseconds(1);
		}
		break;

	case MOTOR_TYPE_FOC:
		while (UTILS_AGE_S(time_start) < timeout) {
			if (mcpwm_foc_get_state() == MC_STATE_OFF) {
				res = true;
				break;
			}

			chThdSleepMilliseconds(1);
		}
		break;

	default:
		break;
	}

	return res;
}

/**
 * Stop the motor and use braking.
 */
float mc_interface_get_duty_cycle_set(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_duty_cycle_set();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_duty_cycle_set();
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

float mc_interface_get_duty_cycle_now(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_duty_cycle_now();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_duty_cycle_now();
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

float mc_interface_get_sampling_frequency_now(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_switching_frequency_now();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_sampling_frequency_now();
		break;

	case MOTOR_TYPE_GPD:
		ret = gpdrive_get_switching_frequency_now();
		break;

	default:
		break;
	}

	return ret;
}

/**
 * 获取电机转速
 *
 * 读取当前电机的实际转速。转速可能来自不同的源：
 * - 编码器反馈
 * - 霍尔传感器
 * - 无传感器观测器
 *
 * 转速计算原理：
 * RPM = (ω × 60) / (2π)
 * 其中：
 * - ω: 角速度 (rad/s)
 * - RPM: 每分钟转数
 *
 * 方向处理：
 * - 应用方向乘数(DIR_MULT)
 * - 正值表示正转
 * - 负值表示反转
 *
 * @return 电机转速 (RPM，正值=正转，负值=反转)
 */
float mc_interface_get_rpm(void) {
	float ret = 0.0;

	// 根据电机类型获取转速
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		// 从BLDC/DC控制模块获取转速
		ret = mcpwm_get_rpm();
		break;

	case MOTOR_TYPE_FOC:
		// 从FOC控制模块获取转速
		ret = mcpwm_foc_get_rpm();
		break;

	default:
		// 未知类型，返回0
		break;
	}

	// 应用方向乘数并返回
	return DIR_MULT * ret;
}

/**
 * 获取累计安时数(放电)
 *
 * 获取从输入电源消耗的累计安时数。
 * 安时数是电池容量和能耗的重要指标。
 *
 * 计算原理：
 * Ah = ∫I(t)dt / 3600
 * 其中：
 * - I(t): 瞬时电流 (A)
 * - dt: 时间微分 (s)
 * - 3600: 秒到小时的转换因子
 *
 * 应用场景：
 * - 电池剩余容量估算
 * - 能耗统计
 * - 续航里程计算
 *
 * @param reset 是否重置计数器
 * @return 累计安时数 (Ah)
 */
float mc_interface_get_amp_hours(bool reset) {
	// 将安秒转换为安时
	float val = motor_now()->m_amp_seconds / 3600;

	// 可选的计数器重置
	if (reset) {
		motor_now()->m_amp_seconds = 0.0;
	}

	return val;
}

/**
 * 获取累计安时数(充电)
 *
 * 获取回馈到输入电源的累计安时数。
 * 主要来自再生制动等能量回收过程。
 *
 * 能量回收原理：
 * - 电机作为发电机运行
 * - 机械能转换为电能
 * - 电能回馈到电池或电源
 * - 提高整体能效
 *
 * 应用场景：
 * - 能量回收效率评估
 * - 制动能量统计
 * - 系统效率分析
 *
 * @param reset 是否重置计数器
 * @return 累计充电安时数 (Ah)
 */
float mc_interface_get_amp_hours_charged(bool reset) {
	// 将充电安秒转换为安时
	float val = motor_now()->m_amp_seconds_charged / 3600;

	// 可选的计数器重置
	if (reset) {
		motor_now()->m_amp_seconds_charged = 0.0;
	}

	return val;
}

/**
 * Get the amount of watt hours drawn from the input source.
 *
 * @param reset
 * If true, the counter will be reset after this call.
 *
 * @return
 * The amount of watt hours drawn.
 */
float mc_interface_get_watt_hours(bool reset) {
	float val = motor_now()->m_watt_seconds / 3600;

	if (reset) {
		motor_now()->m_watt_seconds = 0.0;
	}

	return val;
}

/**
 * Get the amount of watt hours fed back into the input source.
 *
 * @param reset
 * If true, the counter will be reset after this call.
 *
 * @return
 * The amount of watt hours fed back.
 */
float mc_interface_get_watt_hours_charged(bool reset) {
	float val = motor_now()->m_watt_seconds_charged / 3600;

	if (reset) {
		motor_now()->m_watt_seconds_charged = 0.0;
	}

	return val;
}

float mc_interface_get_tot_current(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current();
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_get_tot_current_filtered(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current_filtered();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current_filtered();
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_get_tot_current_directional(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current_directional();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current_directional();
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

float mc_interface_get_tot_current_directional_filtered(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current_directional_filtered();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current_directional_filtered();
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

float mc_interface_get_tot_current_in(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current_in();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current_in();
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_get_tot_current_in_filtered(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tot_current_in_filtered();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tot_current_in_filtered();
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_get_input_voltage_filtered(void) {
	return motor_now()->m_input_voltage_filtered;
}

float mc_interface_get_abs_motor_current_unbalance(void) {
	float ret = 0.0;

#ifdef HW_HAS_3_SHUNTS
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_abs_motor_current_unbalance();
		break;

	default:
		break;
	}
#endif
	return ret;
}

int mc_interface_set_tachometer_value(int steps) {
	int ret = 0;
	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_set_tachometer_value(DIR_MULT * steps);
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_set_tachometer_value(DIR_MULT * steps);
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

int mc_interface_get_tachometer_value(bool reset) {
	int ret = 0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tachometer_value(reset);
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tachometer_value(reset);
		break;

	default:
		break;
	}

	return DIR_MULT * ret;
}

int mc_interface_get_tachometer_abs_value(bool reset) {
	int ret = 0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_tachometer_abs_value(reset);
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_tachometer_abs_value(reset);
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_get_last_inj_adc_isr_duration(void) {
	float ret = 0.0;

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = mcpwm_get_last_inj_adc_isr_duration();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_last_adc_isr_duration();
		break;

	case MOTOR_TYPE_GPD:
		ret = gpdrive_get_last_adc_isr_duration();
		break;

	default:
		break;
	}

	return ret;
}

float mc_interface_read_reset_avg_motor_current(void) {
	if (motor_now()->m_conf.motor_type == MOTOR_TYPE_GPD) {
		return gpdrive_get_current_filtered();
	}

	float res = motor_now()->m_motor_current_sum / motor_now()->m_motor_current_iterations;
	motor_now()->m_motor_current_sum = 0.0;
	motor_now()->m_motor_current_iterations = 0.0;
	return res;
}

float mc_interface_read_reset_avg_input_current(void) {
	if (motor_now()->m_conf.motor_type == MOTOR_TYPE_GPD) {
		return gpdrive_get_current_filtered() * gpdrive_get_modulation();
	}

	float res = motor_now()->m_input_current_sum / motor_now()->m_input_current_iterations;
	motor_now()->m_input_current_sum = 0.0;
	motor_now()->m_input_current_iterations = 0.0;
	return res;
}

/**
 * Read and reset the average direct axis motor current. (FOC only)
 *
 * @return
 * The average D axis current.
 */
float mc_interface_read_reset_avg_id(void) {
	float res = motor_now()->m_motor_id_sum / motor_now()->m_motor_id_iterations;
	motor_now()->m_motor_id_sum = 0.0;
	motor_now()->m_motor_id_iterations = 0.0;
	return res;
}

/**
 * Read and reset the average quadrature axis motor current. (FOC only)
 *
 * @return
 * The average Q axis current.
 */
float mc_interface_read_reset_avg_iq(void) {
	float res = motor_now()->m_motor_iq_sum / motor_now()->m_motor_iq_iterations;
	motor_now()->m_motor_iq_sum = 0.0;
	motor_now()->m_motor_iq_iterations = 0.0;
	return DIR_MULT * res;
}

/**
 * Read and reset the average direct axis motor voltage. (FOC only)
 *
 * @return
 * The average D axis voltage.
 */
float mc_interface_read_reset_avg_vd(void) {
	float res = motor_now()->m_motor_vd_sum / motor_now()->m_motor_vd_iterations;
	motor_now()->m_motor_vd_sum = 0.0;
	motor_now()->m_motor_vd_iterations = 0.0;
	return res;
}

/**
 * Read and reset the average quadrature axis motor voltage. (FOC only)
 *
 * @return
 * The average Q axis voltage.
 */
float mc_interface_read_reset_avg_vq(void) {
	float res = motor_now()->m_motor_vq_sum / motor_now()->m_motor_vq_iterations;
	motor_now()->m_motor_vq_sum = 0.0;
	motor_now()->m_motor_vq_iterations = 0.0;
	return DIR_MULT * res;
}

float mc_interface_get_pid_pos_set(void) {
	return motor_now()->m_position_set;
}

float mc_interface_get_pid_pos_now(void) {
	float ret = 0.0;

	volatile mc_configuration *conf = &motor_now()->m_conf;

	switch (conf->motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		ret = encoder_read_deg();
		break;

	case MOTOR_TYPE_FOC:
		ret = mcpwm_foc_get_pid_pos_now();
		break;

	default:
		break;
	}

	if (encoder_is_configured()) {
		if (conf->foc_encoder_inverted) {
			ret *= -1.0;
		}
	}

	ret *= DIR_MULT;
	ret -= motor_now()->m_conf.p_pid_offset;
	utils_norm_angle(&ret);

	return ret;
}

/**
 * Update the offset such that the current angle becomes angle_now
 */
void mc_interface_update_pid_pos_offset(float angle_now, bool store) {
	mc_configuration *mcconf = mempools_alloc_mcconf();
	*mcconf = *mc_interface_get_configuration();

	mcconf->p_pid_offset += mc_interface_get_pid_pos_now() - angle_now;
	utils_norm_angle(&mcconf->p_pid_offset);

	if (store) {
		conf_general_store_mc_configuration(mcconf, mc_interface_get_motor_thread() == 2);
	}

	mc_interface_set_configuration(mcconf);

	mempools_free_mcconf(mcconf);
}

float mc_interface_get_last_sample_adc_isr_duration(void) {
	return m_last_adc_duration_sample;
}

void mc_interface_sample_print_data(debug_sampling_mode mode, uint16_t len, uint8_t decimation, bool raw, 
		void(*reply_func)(unsigned char *data, unsigned int len)) {

	if (len > ADC_SAMPLE_MAX_LEN) {
		len = ADC_SAMPLE_MAX_LEN;
	}

	if (mode == DEBUG_SAMPLING_SEND_LAST_SAMPLES) {
		chEvtSignal(sample_send_tp, (eventmask_t) 1);
	} else {
		m_sample_trigger = -1;
		m_sample_now = 0;
		m_sample_len = len;
		m_sample_int = decimation;
		m_sample_mode = mode;
		m_sample_raw = raw;
		send_func_sample = reply_func;
#ifdef HW_HAS_DUAL_MOTORS
		m_sample_is_second_motor = motor_now() == &m_motor_2;
#endif
	}
}

/**
 * Get filtered MOSFET temperature. The temperature is pre-calculated, so this
 * functions is fast.
 *
 * @return
 * The filtered MOSFET temperature.
 */
float mc_interface_temp_fet_filtered(void) {
	return motor_now()->m_temp_fet;
}

/**
 * Get filtered motor temperature. The temperature is pre-calculated, so this
 * functions is fast.
 *
 * @return
 * The filtered motor temperature.
 */
float mc_interface_temp_motor_filtered(void) {
	return motor_now()->m_temp_motor;
}

/**
 * Get the battery level, based on battery settings in configuration. Notice that
 * this function is based on remaining watt hours, and not amp hours.
 *
 * @param wh_left
 * Pointer to where to store the remaining watt hours, can be null.
 *
 * @return
 * Battery level, range 0 to 1
 */
float mc_interface_get_battery_level(float *wh_left) {
	const volatile mc_configuration *conf = mc_interface_get_configuration();
	const float v_in = motor_now()->m_input_voltage_filtered_slower;
	float battery_avg_voltage = 0.0;
	float battery_avg_voltage_left = 0.0;
	float ah_left = 0;
	float ah_tot = conf->si_battery_ah;

	switch (conf->si_battery_type) {
	case BATTERY_TYPE_LIION_3_0__4_2:
		battery_avg_voltage = ((3.2 + 4.2) / 2.0) * (float)(conf->si_battery_cells);
		battery_avg_voltage_left = ((3.2 * (float)(conf->si_battery_cells) + v_in) / 2.0);
		float batt_left = utils_map(v_in / (float)(conf->si_battery_cells),
									3.2, 4.2, 0.0, 1.0);
		batt_left = utils_batt_liion_norm_v_to_capacity(batt_left);
		ah_tot *= 0.85; // 0.85 because the battery is not fully depleted at 3.2V / cell
		ah_left = batt_left * ah_tot;
		break;

	case BATTERY_TYPE_LIIRON_2_6__3_6:
		battery_avg_voltage = ((2.8 + 3.6) / 2.0) * (float)(conf->si_battery_cells);
		battery_avg_voltage_left = ((2.8 * (float)(conf->si_battery_cells) + v_in) / 2.0);
		ah_left = utils_map(v_in / (float)(conf->si_battery_cells),
				2.6, 3.6, 0.0, conf->si_battery_ah);
		break;

	case BATTERY_TYPE_LEAD_ACID:
		// TODO: This does not really work for lead-acid batteries
		battery_avg_voltage = ((2.1 + 2.36) / 2.0) * (float)(conf->si_battery_cells);
		battery_avg_voltage_left = ((2.1 * (float)(conf->si_battery_cells) + v_in) / 2.0);
		ah_left = utils_map(v_in / (float)(conf->si_battery_cells),
				2.1, 2.36, 0.0, conf->si_battery_ah);
		break;

	default:
		break;
	}

	const float wh_batt_tot = ah_tot * battery_avg_voltage;
	const float wh_batt_left = ah_left * battery_avg_voltage_left;

	if (wh_left) {
		*wh_left = wh_batt_left;
	}

	return wh_batt_left / wh_batt_tot;
}

/**
 * Get the speed based on wheel diameter, gearing and motor pole settings.
 *
 * @return
 * Speed, in m/s
 */
float mc_interface_get_speed(void) {
#ifdef HW_HAS_WHEEL_SPEED_SENSOR
	return hw_get_speed();
#else
	const volatile mc_configuration *conf = mc_interface_get_configuration();
	const float rpm = mc_interface_get_rpm() / (conf->si_motor_poles / 2.0);
	return (rpm / 60.0) * conf->si_wheel_diameter * M_PI / conf->si_gear_ratio;
#endif
}

/**
 * Get the distance traveled based on wheel diameter, gearing and motor pole settings.
 *
 * @return
 * Distance traveled since boot, in meters
 */
float mc_interface_get_distance(void) {
	const volatile mc_configuration *conf = mc_interface_get_configuration();
	const float tacho_scale = (conf->si_wheel_diameter * M_PI) / (3.0 * conf->si_motor_poles * conf->si_gear_ratio);
	return mc_interface_get_tachometer_value(false) * tacho_scale;
}

/**
 * Get the absolute distance traveled based on wheel diameter, gearing and motor pole settings.
 *
 * @return
 * Absolute distance traveled since boot, in meters
 */
float mc_interface_get_distance_abs(void) {
#ifdef HW_HAS_WHEEL_SPEED_SENSOR
	return hw_get_distance_abs();
#else
	const volatile mc_configuration *conf = mc_interface_get_configuration();
	const float tacho_scale = (conf->si_wheel_diameter * M_PI) / (3.0 * conf->si_motor_poles * conf->si_gear_ratio);
	return mc_interface_get_tachometer_abs_value(false) * tacho_scale;
#endif
}

setup_values mc_interface_get_setup_values(void) {
	setup_values val = {0, 0, 0, 0, 0, 0, 0};
	val.num_vescs = 1;

	val.ah_tot += mc_interface_get_amp_hours(false);
	val.ah_charge_tot += mc_interface_get_amp_hours_charged(false);
	val.wh_tot += mc_interface_get_watt_hours(false);
	val.wh_charge_tot += mc_interface_get_watt_hours_charged(false);
	val.current_tot += mc_interface_get_tot_current_filtered();
	val.current_in_tot += mc_interface_get_tot_current_in_filtered();

	for (int i = 0;i < CAN_STATUS_MSGS_TO_STORE;i++) {
		can_status_msg *msg = comm_can_get_status_msg_index(i);
		if (msg->id >= 0 && UTILS_AGE_S(msg->rx_time) < 0.1) {
			val.current_tot += msg->current;
			val.num_vescs++;
		}

		can_status_msg_2 *msg2 = comm_can_get_status_msg_2_index(i);
		if (msg2->id >= 0 && UTILS_AGE_S(msg2->rx_time) < 0.1) {
			val.ah_tot += msg2->amp_hours;
			val.ah_charge_tot += msg2->amp_hours_charged;
		}

		can_status_msg_3 *msg3 = comm_can_get_status_msg_3_index(i);
		if (msg3->id >= 0 && UTILS_AGE_S(msg3->rx_time) < 0.1) {
			val.wh_tot += msg3->watt_hours;
			val.wh_charge_tot += msg3->watt_hours_charged;
		}

		can_status_msg_4 *msg4 = comm_can_get_status_msg_4_index(i);
		if (msg4->id >= 0 && UTILS_AGE_S(msg4->rx_time) < 0.1) {
			val.current_in_tot += msg4->current_in;
		}
	}

	return val;
}

volatile gnss_data *mc_interface_gnss(void) {
	return &m_gnss;
}

/**
 * Set odometer value in meters.
 *
 * @param new_odometer_meters
 * new odometer value in meters
 */
void mc_interface_set_odometer(uint64_t new_odometer_meters) {
	g_backup.odometer = new_odometer_meters;
}

/**
 * Return current odometer value in meters.
 *
 * @return
 * Odometer value in meters, including current trip
 */
uint64_t mc_interface_get_odometer(void) {
	return g_backup.odometer;
}

/**
 * Ignore motor control commands for this amount of time.
 */
void mc_interface_ignore_input(int time_ms) {
	volatile motor_if_state_t *motor = motor_now();
	motor->m_ignore_iterations = time_ms;
}

/**
 * Ignore motor control commands for this amount of time on both motors.
 */
void mc_interface_ignore_input_both(int time_ms) {
	m_motor_1.m_ignore_iterations = time_ms;

#ifdef HW_HAS_DUAL_MOTORS
	m_motor_2.m_ignore_iterations = time_ms;
#endif
}

void mc_interface_release_motor_override_both(void) {
	int motor_last = mc_interface_get_motor_thread();
	mc_interface_select_motor_thread(1);
	mc_interface_release_motor_override();
	mc_interface_select_motor_thread(2);
	mc_interface_release_motor_override();
	mc_interface_select_motor_thread(motor_last);
}

bool mc_interface_wait_for_motor_release_both(float timeout) {
	int motor_last = mc_interface_get_motor_thread();

	mc_interface_select_motor_thread(1);
	if (!mc_interface_wait_for_motor_release(timeout)) {
		mc_interface_select_motor_thread(motor_last);
		return false;
	}

	mc_interface_select_motor_thread(2);
	if (!mc_interface_wait_for_motor_release(timeout)) {
		mc_interface_select_motor_thread(motor_last);
		return false;
	}

	return true;
}

void mc_interface_set_current_off_delay(float delay_sec) {
	if (mc_interface_try_input()) {
		return;
	}

	UTILS_NAN_ZERO(delay_sec);
	if (delay_sec > 5.0) {
		delay_sec = 5.0;
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:

		break;

	case MOTOR_TYPE_FOC:
		mcpwm_foc_set_current_off_delay(delay_sec);
		break;

	default:
		break;
	}
}

void mc_interface_override_temp_motor(float temp) {
	motor_now()->m_temp_override = temp;
}

// MC implementation functions

/**
 * A helper function that should be called before sending commands to control
 * the motor. If the state is detecting, the detection will be stopped.
 *
 * @return
 * The amount if milliseconds left until user commands are allowed again.
 *
 */
int mc_interface_try_input(void) {
	// TODO: Remove this later
	if (mc_interface_get_state() == MC_STATE_DETECTING) {
		mcpwm_stop_pwm();
		motor_now()->m_ignore_iterations = MCPWM_DETECT_STOP_TIME;
	}

	int retval = motor_now()->m_ignore_iterations;

	if (!motor_now()->m_ignore_iterations && motor_now()->m_lock_enabled) {
		if (!motor_now()->m_lock_override_once) {
			retval = 1;
		} else {
			motor_now()->m_lock_override_once = false;
		}
	}

	switch (motor_now()->m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		if (!mcpwm_init_done()) {
			retval = 1;
		}
		break;

	case MOTOR_TYPE_FOC:
		if (!mcpwm_foc_init_done()) {
			retval = 1;
		}
		break;

	default:
		break;
	}

	return retval;
}

void mc_interface_set_fault_info(const char *str, int argn, float arg0, float arg1) {
	m_fault_data.info_str = str;
	m_fault_data.info_argn = argn;
	m_fault_data.info_args[0] = arg0;
	m_fault_data.info_args[1] = arg1;
}

void mc_interface_fault_stop(mc_fault_code fault, bool is_second_motor, bool is_isr) {
	m_fault_data.fault_code = fault;
	m_fault_data.is_second_motor = is_second_motor;

	if (is_isr) {
		chSysLockFromISR();
		chEvtSignalI(fault_stop_tp, (eventmask_t) 1);
		chSysUnlockFromISR();
	} else {
		chEvtSignal(fault_stop_tp, (eventmask_t) 1);
	}
}

void mc_interface_mc_timer_isr(bool is_second_motor) {
	ledpwm_update_pwm();

#ifdef HW_HAS_DUAL_MOTORS
	motor_if_state_t *motor = is_second_motor ? (motor_if_state_t*)&m_motor_2 : (motor_if_state_t*)&m_motor_1;
#else
	motor_if_state_t *motor = (motor_if_state_t*)&m_motor_1;
	(void)is_second_motor;
#endif

	mc_configuration *conf_now = (mc_configuration*)&motor->m_conf;
	const float input_voltage = GET_INPUT_VOLTAGE();
	UTILS_LP_FAST(motor->m_input_voltage_filtered, input_voltage, 0.02);

	// Check for faults that should stop the motor

	static float wrong_voltage_integrator = 0.0;
	float voltage_diff_now = 0.0;

	if (input_voltage < conf_now->l_min_vin) {
		voltage_diff_now = conf_now->l_min_vin - input_voltage;
	} else if (input_voltage > conf_now->l_max_vin) {
		voltage_diff_now = input_voltage - conf_now->l_max_vin;
	}

	if (voltage_diff_now > 1.0e-3) {
		wrong_voltage_integrator += voltage_diff_now;

		const float max_voltage = (conf_now->l_max_vin * 0.05);
		if (wrong_voltage_integrator > max_voltage) {
			mc_interface_fault_stop(input_voltage < conf_now->l_min_vin ?
					FAULT_CODE_UNDER_VOLTAGE : FAULT_CODE_OVER_VOLTAGE, is_second_motor, true);

			// Windup protection
			wrong_voltage_integrator = max_voltage * 2.0;
		}
	} else {
		if (wrong_voltage_integrator > 1.0) {
			wrong_voltage_integrator -= 1.0;
		} else {
			wrong_voltage_integrator = 0.0;
		}
	}

	// Fetch these values in a config-specific way to avoid some overhead of the general
	// functions. That will make this interrupt run a bit faster.
	mc_state state;
	float current;
	float current_filtered;
	float current_in_filtered;
	float abs_current;
	float abs_current_filtered;
	if (conf_now->motor_type == MOTOR_TYPE_FOC) {
		state = mcpwm_foc_get_state_motor(is_second_motor);
		current = mcpwm_foc_get_tot_current_motor(is_second_motor);
		current_filtered = mcpwm_foc_get_tot_current_filtered_motor(is_second_motor);
		current_in_filtered = mcpwm_foc_get_tot_current_in_filtered_motor(is_second_motor);
		abs_current = mcpwm_foc_get_abs_motor_current_motor(is_second_motor);
		abs_current_filtered = mcpwm_foc_get_abs_motor_current_filtered_motor(is_second_motor);
	} else {
		state = mcpwm_get_state();
		current = mcpwm_get_tot_current();
		current_filtered = mcpwm_get_tot_current_filtered();
		current_in_filtered = mcpwm_get_tot_current_in_filtered();
		abs_current = mcpwm_get_tot_current();
		abs_current_filtered = current_filtered;
	}

	if (state == MC_STATE_RUNNING) {
		motor->m_cycles_running++;
	} else {
		motor->m_cycles_running = 0;
	}

	if (pwn_done_func) {
		pwn_done_func();
	}

	motor->m_motor_current_sum += current_filtered;
	motor->m_input_current_sum += current_in_filtered;
	motor->m_motor_current_iterations++;
	motor->m_input_current_iterations++;

	motor->m_motor_id_sum += mcpwm_foc_get_id();
	motor->m_motor_iq_sum += mcpwm_foc_get_iq();
	motor->m_motor_id_iterations++;
	motor->m_motor_iq_iterations++;

	motor->m_motor_vd_sum += mcpwm_foc_get_vd();
	motor->m_motor_vq_sum += mcpwm_foc_get_vq();
	motor->m_motor_vd_iterations++;
	motor->m_motor_vq_iterations++;

	// Current fault code
	if (conf_now->l_slow_abs_current) {
		if (fabsf(abs_current_filtered) > conf_now->l_abs_current_max) {
			mc_interface_fault_stop(FAULT_CODE_ABS_OVER_CURRENT, is_second_motor, true);
		}
	} else {
		if (fabsf(abs_current) > conf_now->l_abs_current_max) {
			mc_interface_fault_stop(FAULT_CODE_ABS_OVER_CURRENT, is_second_motor, true);
		}
	}

	// DRV fault code
#ifdef HW_HAS_DUAL_PARALLEL
	if (IS_DRV_FAULT() || IS_DRV_FAULT_2()) {
		is_second_motor = IS_DRV_FAULT_2();
#else
	if (is_second_motor ? IS_DRV_FAULT_2() : IS_DRV_FAULT()) {
#endif
		mc_interface_fault_stop(FAULT_CODE_DRV, is_second_motor, true);
	}

#ifdef HW_USE_BRK
	// BRK fault code
	if (TIM_GetFlagStatus(TIM1, TIM_FLAG_Break) != RESET) {
		mc_interface_fault_stop(FAULT_CODE_BRK, is_second_motor, true);
		// latch the BRK/FAULT pin to low until next MCU reset
		palSetPadMode(BRK_GPIO, BRK_PIN, PAL_MODE_OUTPUT_PUSHPULL);
		palClearPad(BRK_GPIO, BRK_PIN);
	}
#endif

#ifdef HW_HAS_GATE_DRIVER_SUPPLY_MONITOR
	if(motor->m_gate_driver_voltage > HW_GATE_DRIVER_SUPPLY_MAX_VOLTAGE) {
		mc_interface_fault_stop(FAULT_CODE_GATE_DRIVER_OVER_VOLTAGE, is_second_motor, true);
	}

	if(motor->m_gate_driver_voltage < HW_GATE_DRIVER_SUPPLY_MIN_VOLTAGE) {
		mc_interface_fault_stop(FAULT_CODE_GATE_DRIVER_UNDER_VOLTAGE, is_second_motor, true);
	}
#endif

#ifdef HW_HAS_LV_OUTPUT_PROTECTION
	if(IS_LV_OUTPUT_FAULT()) {
		mc_interface_fault_stop(FAULT_CODE_LV_OUTPUT_FAULT, is_second_motor, true);
	}
#endif

	float t_samp = 1.0 / motor->m_f_samp_now;

	// Watt and ah counters
	if (fabsf(current_filtered) > 1.0) {
		// Some extra filtering
		static float curr_diff_sum = 0.0;
		static float curr_diff_samples = 0;

		curr_diff_sum += current_in_filtered * t_samp;
		curr_diff_samples += t_samp;

		if (curr_diff_samples >= 0.01) {
			if (curr_diff_sum > 0.0) {
				motor->m_amp_seconds += curr_diff_sum;
				motor->m_watt_seconds += curr_diff_sum * input_voltage;
			} else {
				motor->m_amp_seconds_charged -= curr_diff_sum;
				motor->m_watt_seconds_charged -= curr_diff_sum * input_voltage;
			}

			curr_diff_samples = 0.0;
			curr_diff_sum = 0.0;
		}
	}

	bool sample = false;
	debug_sampling_mode sample_mode =
			m_sample_is_second_motor == is_second_motor ?
					m_sample_mode : DEBUG_SAMPLING_OFF;

	switch (sample_mode) {
	case DEBUG_SAMPLING_NOW:
		if (m_sample_now == m_sample_len) {
			m_sample_mode = DEBUG_SAMPLING_OFF;
			m_sample_mode_last = DEBUG_SAMPLING_NOW;
			chSysLockFromISR();
			chEvtSignalI(sample_send_tp, (eventmask_t) 1);
			chSysUnlockFromISR();
		} else {
			sample = true;
		}
		break;

	case DEBUG_SAMPLING_START:
		if (state == MC_STATE_RUNNING || m_sample_now > 0) {
			sample = true;
		}

		if (m_sample_now == m_sample_len) {
			m_sample_mode_last = m_sample_mode;
			m_sample_mode = DEBUG_SAMPLING_OFF;
			chSysLockFromISR();
			chEvtSignalI(sample_send_tp, (eventmask_t) 1);
			chSysUnlockFromISR();
		}
		break;

	case DEBUG_SAMPLING_TRIGGER_START:
	case DEBUG_SAMPLING_TRIGGER_START_NOSEND: {
		sample = true;

		int sample_last = -1;
		if (m_sample_trigger >= 0) {
			sample_last = m_sample_trigger - m_sample_len;
			if (sample_last < 0) {
				sample_last += ADC_SAMPLE_MAX_LEN;
			}
		}

		if (m_sample_now == sample_last) {
			m_sample_mode_last = m_sample_mode;
			sample = false;

			if (m_sample_mode == DEBUG_SAMPLING_TRIGGER_START) {
				chSysLockFromISR();
				chEvtSignalI(sample_send_tp, (eventmask_t) 1);
				chSysUnlockFromISR();
			}

			m_sample_mode = DEBUG_SAMPLING_OFF;
		}

		if (state == MC_STATE_RUNNING && m_sample_trigger < 0) {
			m_sample_trigger = m_sample_now;
		}
	} break;

	case DEBUG_SAMPLING_TRIGGER_FAULT:
	case DEBUG_SAMPLING_TRIGGER_FAULT_NOSEND: {
		sample = true;

		int sample_last = -1;
		if (m_sample_trigger >= 0) {
			sample_last = m_sample_trigger - m_sample_len;
			if (sample_last < 0) {
				sample_last += ADC_SAMPLE_MAX_LEN;
			}
		}

		if (m_sample_now == sample_last) {
			m_sample_mode_last = m_sample_mode;
			sample = false;

			if (m_sample_mode == DEBUG_SAMPLING_TRIGGER_FAULT) {
				chSysLockFromISR();
				chEvtSignalI(sample_send_tp, (eventmask_t) 1);
				chSysUnlockFromISR();
			}

			m_sample_mode = DEBUG_SAMPLING_OFF;
		}

		if (motor->m_fault_now != FAULT_CODE_NONE && m_sample_trigger < 0) {
			m_sample_trigger = m_sample_now;
		}
	} break;

	default:
		break;
	}

	if (sample) {
		static int a = 0;
		a++;

		if (a >= m_sample_int) {
			a = 0;

			if (m_sample_now >= ADC_SAMPLE_MAX_LEN) {
				m_sample_now = 0;
			}

			int16_t zero;
			if (conf_now->motor_type == MOTOR_TYPE_FOC) {
				if (is_second_motor) {
					zero = (ADC_V_L4 + ADC_V_L5 + ADC_V_L6) / 3;
				} else {
					zero = (ADC_V_L1 + ADC_V_L2 + ADC_V_L3) / 3;
				}
				m_phase_samples[m_sample_now] = (uint8_t)(mcpwm_foc_get_phase() / 360.0 * 250.0);
//				m_phase_samples[m_sample_now] = (uint8_t)(mcpwm_foc_get_phase_observer() / 360.0 * 250.0);
//				float ang = utils_angle_difference(mcpwm_foc_get_phase_observer(), mcpwm_foc_get_phase_encoder()) + 180.0;
//				m_phase_samples[m_sample_now] = (uint8_t)(ang / 360.0 * 250.0);
			} else {
				zero = mcpwm_vzero;
				m_phase_samples[m_sample_now] = 0;
			}

			if (state == MC_STATE_DETECTING) {
				m_curr0_samples[m_sample_now] = (int16_t)mcpwm_detect_currents[mcpwm_get_comm_step() - 1];
				m_curr1_samples[m_sample_now] = (int16_t)mcpwm_detect_currents_diff[mcpwm_get_comm_step() - 1];

				m_ph1_samples[m_sample_now] = (int16_t)mcpwm_detect_voltages[0];
				m_ph2_samples[m_sample_now] = (int16_t)mcpwm_detect_voltages[1];
				m_ph3_samples[m_sample_now] = (int16_t)mcpwm_detect_voltages[2];
			} else {
				if (is_second_motor) {
					m_curr0_samples[m_sample_now] = ADC_curr_norm_value[3];
					m_curr1_samples[m_sample_now] = ADC_curr_norm_value[4];

					m_ph1_samples[m_sample_now] = ADC_V_L4 - zero;
					m_ph2_samples[m_sample_now] = ADC_V_L5 - zero;
					m_ph3_samples[m_sample_now] = ADC_V_L6 - zero;
				} else {
					m_curr0_samples[m_sample_now] = ADC_curr_norm_value[0];
					m_curr1_samples[m_sample_now] = ADC_curr_norm_value[1];

					m_ph1_samples[m_sample_now] = ADC_V_L1 - zero;
					m_ph2_samples[m_sample_now] = ADC_V_L2 - zero;
					m_ph3_samples[m_sample_now] = ADC_V_L3 - zero;
				}
			}

			m_vzero_samples[m_sample_now] = zero;
			m_curr_fir_samples[m_sample_now] = (int16_t)(current * (8.0 / FAC_CURRENT));
			m_f_sw_samples[m_sample_now] = (int16_t)(0.1 / t_samp);
			m_status_samples[m_sample_now] = mcpwm_get_comm_step() | (mcpwm_read_hall_phase() << 3);

			m_sample_now++;

			m_last_adc_duration_sample = mc_interface_get_last_inj_adc_isr_duration();
		}
	}
}

void mc_interface_adc_inj_int_handler(void) {
	switch (m_motor_1.m_conf.motor_type) {
	case MOTOR_TYPE_BLDC:
	case MOTOR_TYPE_DC:
		mcpwm_adc_inj_int_handler();
		break;

	case MOTOR_TYPE_FOC:
		break;

	default:
		break;
	}
}

/**
 * Update the override limits for a configuration based on MOSFET temperature etc.
 *
 * @param conf
 * The configaration to update.
 */
static void update_override_limits(volatile motor_if_state_t *motor, volatile mc_configuration *conf) {
	bool is_motor_1 = motor == &m_motor_1;

	const float v_in = motor->m_input_voltage_filtered;
	float rpm_now = 0.0;

	if (motor->m_conf.motor_type == MOTOR_TYPE_FOC) {
		// Low latency is important for avoiding oscillations
		rpm_now = DIR_MULT * mcpwm_foc_get_rpm_fast();
	} else {
		rpm_now = mc_interface_get_rpm();
	}

	float rpm_abs = fabsf(rpm_now);

	const float duty_now_abs = fabsf(mc_interface_get_duty_cycle_now());

#ifdef HW_HAS_DUAL_PARALLEL
	UTILS_LP_FAST(motor->m_temp_fet, fmaxf(NTC_TEMP(ADC_IND_TEMP_MOS), NTC_TEMP(ADC_IND_TEMP_MOS_M2)), 0.1);
#else
	UTILS_LP_FAST(motor->m_temp_fet, NTC_TEMP(is_motor_1 ? ADC_IND_TEMP_MOS : ADC_IND_TEMP_MOS_M2), 0.1);
#endif

	float temp_motor = 0.0;

	switch(conf->m_motor_temp_sens_type) {
	case TEMP_SENSOR_NTC_10K_25C:
		temp_motor = is_motor_1 ? NTC_TEMP_MOTOR(conf->m_ntc_motor_beta) : NTC_TEMP_MOTOR_2(conf->m_ntc_motor_beta);
		break;

	case TEMP_SENSOR_NTC_100K_25C:
		temp_motor = is_motor_1 ? NTC100K_TEMP_MOTOR(conf->m_ntc_motor_beta) : NTC100K_TEMP_MOTOR_2(conf->m_ntc_motor_beta);
		break;

	case TEMP_SENSOR_PTC_1K_100C:
		temp_motor = is_motor_1 ? PTC_TEMP_MOTOR(1000.0, conf->m_ptc_motor_coeff, 100) : PTC_TEMP_MOTOR_2(1000.0, conf->m_ptc_motor_coeff, 100);
		break;

	case TEMP_SENSOR_KTY83_122: {
		// KTY83_122 datasheet used to approximate resistance at given temperature to cubic polynom
		// https://docs.google.com/spreadsheets/d/1iJA66biczfaXRNClSsrVF9RJuSAKoDG-bnRZFMOcuwU/edit?usp=sharing
		// Thanks to: https://vasilisks.wordpress.com/2017/12/14/getting-temperature-from-ntc-kty83-kty84-on-mcu/#more-645
		// You can change pull up resistor and update NTC_RES_MOTOR for your hardware without changing polynom
		float res = NTC_RES_MOTOR(ADC_Value[is_motor_1 ? ADC_IND_TEMP_MOTOR : ADC_IND_TEMP_MOTOR_2]);
		float pow2 = res * res;
		temp_motor = 0.0000000102114874947423 * pow2 * res - 0.000069967997703501 * pow2 +
				0.243402040973194 * res - 160.145048329356;
	} break;

	case TEMP_SENSOR_KTY84_130: {
		float res = NTC_RES_MOTOR(ADC_Value[is_motor_1 ? ADC_IND_TEMP_MOTOR : ADC_IND_TEMP_MOTOR_2]);
		temp_motor = -7.82531699e-12 * res * res * res * res + 6.34445902e-8 * res * res * res -
				0.00020119157  * res * res + 0.407683016 * res - 161.357536;
	} break;

	case TEMP_SENSOR_NTCX:
		temp_motor = is_motor_1 ? NTCX_TEMP_MOTOR(conf->m_ntcx_ptcx_res, conf->m_ntc_motor_beta, conf->m_ntcx_ptcx_temp_base) :
				NTCX_TEMP_MOTOR_2(conf->m_ntcx_ptcx_res, conf->m_ntc_motor_beta, conf->m_ntcx_ptcx_temp_base);
		break;

	case TEMP_SENSOR_PTCX:
		temp_motor = is_motor_1 ? PTC_TEMP_MOTOR(conf->m_ntcx_ptcx_res, conf->m_ptc_motor_coeff, conf->m_ntcx_ptcx_temp_base) :
				PTC_TEMP_MOTOR_2(conf->m_ntcx_ptcx_res, conf->m_ptc_motor_coeff, conf->m_ntcx_ptcx_temp_base);
		break;

	case TEMP_SENSOR_PT1000: {
		float res = NTC_RES_MOTOR(ADC_Value[is_motor_1 ? ADC_IND_TEMP_MOTOR : ADC_IND_TEMP_MOTOR_2]);
		temp_motor = -(sqrtf(-0.00232 * res + 17.59246) - 3.908) / 0.00116;
	} break;

	case TEMP_SENSOR_DISABLED:
		temp_motor = motor->m_temp_override;
		break;
	}

	// If the reading is messed up (by e.g. reading 0 on the ADC and dividing by 0) we avoid putting an
	// invalid value in the filter, as it will never recover. It is probably safest to keep running the
	// motor even if the temperature reading fails. A config option to reduce power on invalid temperature
	// readings might be useful.
	if (UTILS_IS_NAN(temp_motor) || UTILS_IS_INF(temp_motor) || temp_motor > 600.0 || temp_motor < -200.0) {
		temp_motor = -100.0;
	}

	UTILS_LP_FAST(motor->m_temp_motor, temp_motor, MOTOR_TEMP_LPF);

#ifdef HW_HAS_GATE_DRIVER_SUPPLY_MONITOR
	UTILS_LP_FAST(motor->m_gate_driver_voltage, GET_GATE_DRIVER_SUPPLY_VOLTAGE(), 0.01);
#endif

	const float l_current_min_tmp = conf->l_current_min * conf->l_current_min_scale;
	const float l_current_max_tmp = conf->l_current_max * conf->l_current_max_scale;

	// Temperature MOSFET
	float lo_min_mos = l_current_min_tmp;
	float lo_max_mos = l_current_max_tmp;
	if (motor->m_temp_fet < (conf->l_temp_fet_start + 0.1)) {
		// Keep values
	} else if (motor->m_temp_fet > (conf->l_temp_fet_end - 0.1)) {
		lo_min_mos = 0.0;
		lo_max_mos = 0.0;
		mc_interface_fault_stop(FAULT_CODE_OVER_TEMP_FET, !is_motor_1, false);
	} else {
		float maxc = fabsf(l_current_max_tmp);
		if (fabsf(l_current_min_tmp) > maxc) {
			maxc = fabsf(l_current_min_tmp);
		}

		maxc = utils_map(motor->m_temp_fet, conf->l_temp_fet_start, conf->l_temp_fet_end, maxc, 0.0);

		if (fabsf(l_current_min_tmp) > maxc) {
			lo_min_mos = SIGN(l_current_min_tmp) * maxc;
		}

		if (fabsf(l_current_max_tmp) > maxc) {
			lo_max_mos = SIGN(l_current_max_tmp) * maxc;
		}
	}

	// Temperature MOTOR
	float lo_min_mot = l_current_min_tmp;
	float lo_max_mot = l_current_max_tmp;
	if (motor->m_temp_motor < (conf->l_temp_motor_start + 0.1)) {
		// Keep values
	} else if (motor->m_temp_motor > (conf->l_temp_motor_end - 0.1)) {
		lo_min_mot = 0.0;
		lo_max_mot = 0.0;
		mc_interface_fault_stop(FAULT_CODE_OVER_TEMP_MOTOR, !is_motor_1, false);
	} else {
		float maxc = fabsf(l_current_max_tmp);
		if (fabsf(l_current_min_tmp) > maxc) {
			maxc = fabsf(l_current_min_tmp);
		}

		maxc = utils_map(motor->m_temp_motor, conf->l_temp_motor_start, conf->l_temp_motor_end, maxc, 0.0);

		if (fabsf(l_current_min_tmp) > maxc) {
			lo_min_mot = SIGN(l_current_min_tmp) * maxc;
		}

		if (fabsf(l_current_max_tmp) > maxc) {
			lo_max_mot = SIGN(l_current_max_tmp) * maxc;
		}
	}

	// Decreased temperatures during acceleration
	// in order to still have braking torque available
	const float temp_fet_accel_start = utils_map(conf->l_temp_accel_dec, 0.0, 1.0, conf->l_temp_fet_start, 25.0);
	const float temp_fet_accel_end = utils_map(conf->l_temp_accel_dec, 0.0, 1.0, conf->l_temp_fet_end, 25.0);
	const float temp_motor_accel_start = utils_map(conf->l_temp_accel_dec, 0.0, 1.0, conf->l_temp_motor_start, 25.0);
	const float temp_motor_accel_end = utils_map(conf->l_temp_accel_dec, 0.0, 1.0, conf->l_temp_motor_end, 25.0);

	float lo_fet_temp_accel = 0.0;
	if (motor->m_temp_fet < (temp_fet_accel_start + 0.1)) {
		lo_fet_temp_accel = l_current_max_tmp;
	} else if (motor->m_temp_fet > (temp_fet_accel_end - 0.1)) {
		lo_fet_temp_accel = 0.0;
	} else {
		lo_fet_temp_accel = utils_map(motor->m_temp_fet, temp_fet_accel_start,
				temp_fet_accel_end, l_current_max_tmp, 0.0);
	}

	float lo_motor_temp_accel = 0.0;
	if (motor->m_temp_motor < (temp_motor_accel_start + 0.1)) {
		lo_motor_temp_accel = l_current_max_tmp;
	} else if (motor->m_temp_motor > (temp_motor_accel_end - 0.1)) {
		lo_motor_temp_accel = 0.0;
	} else {
		lo_motor_temp_accel = utils_map(motor->m_temp_motor, temp_motor_accel_start,
				temp_motor_accel_end, l_current_max_tmp, 0.0);
	}

	// RPM max
	float lo_max_rpm = 0.0;
	const float rpm_pos_cut_start = conf->l_max_erpm * conf->l_erpm_start;
	const float rpm_pos_cut_end = conf->l_max_erpm;
	if (rpm_now < (rpm_pos_cut_start + 0.1)) {
		lo_max_rpm = l_current_max_tmp;
	} else if (rpm_now > (rpm_pos_cut_end - 0.1)) {
		lo_max_rpm = 0.0;
	} else {
		lo_max_rpm = utils_map(rpm_now, rpm_pos_cut_start, rpm_pos_cut_end, l_current_max_tmp, 0.0);
	}

	// RPM min
	float lo_min_rpm = 0.0;
	const float rpm_neg_cut_start = conf->l_min_erpm * conf->l_erpm_start;
	const float rpm_neg_cut_end = conf->l_min_erpm;
	if (rpm_now > (rpm_neg_cut_start - 0.1)) {
		lo_min_rpm = l_current_max_tmp;
	} else if (rpm_now < (rpm_neg_cut_end + 0.1)) {
		lo_min_rpm = 0.0;
	} else {
		lo_min_rpm = utils_map(rpm_now, rpm_neg_cut_start, rpm_neg_cut_end, l_current_max_tmp, 0.0);
	}

	// Start Current Decrease
	float lo_max_curr_dec = l_current_max_tmp;
	if (rpm_abs < conf->foc_start_curr_dec_rpm) {
		lo_max_curr_dec = utils_map(rpm_abs, 0, conf->foc_start_curr_dec_rpm,
				conf->foc_start_curr_dec * l_current_max_tmp, l_current_max_tmp);
	}

	// Duty max
	float lo_max_duty = 0.0;
	if (duty_now_abs < (conf->l_duty_start * conf->l_max_duty) || conf->l_duty_start > 0.99) {
		lo_max_duty = l_current_max_tmp;
	} else {
		lo_max_duty = utils_map(duty_now_abs, (conf->l_duty_start * conf->l_max_duty),
				conf->l_max_duty, l_current_max_tmp, conf->cc_min_current * 5.0);
	}

	float lo_max = utils_min_abs(lo_max_mos, lo_max_mot);
	float lo_min = utils_min_abs(lo_min_mos, lo_min_mot);

	lo_max = utils_min_abs(lo_max, lo_max_rpm);
	lo_max = utils_min_abs(lo_max, lo_min_rpm);
	lo_max = utils_min_abs(lo_max, lo_max_curr_dec);
	lo_max = utils_min_abs(lo_max, lo_fet_temp_accel);
	lo_max = utils_min_abs(lo_max, lo_motor_temp_accel);
	lo_max = utils_min_abs(lo_max, lo_max_duty);

	if (lo_max < conf->cc_min_current) {
		lo_max = conf->cc_min_current;
	}

	if (lo_min > -conf->cc_min_current) {
		lo_min = -conf->cc_min_current;
	}

	conf->lo_current_max = lo_max;
	conf->lo_current_min = lo_min;

	// Battery cutoff
	float lo_in_max_batt = 0.0;
	if (v_in > (conf->l_battery_cut_start - 0.1)) {
		lo_in_max_batt = conf->l_in_current_max;
	} else if (v_in < (conf->l_battery_cut_end + 0.1)) {
		lo_in_max_batt = 0.0;
	} else {
		lo_in_max_batt = utils_map(v_in, conf->l_battery_cut_start,
				conf->l_battery_cut_end, conf->l_in_current_max, 0.0);
	}

	// Wattage limits
	const float lo_in_max_watt = conf->l_watt_max / v_in;
	const float lo_in_min_watt = conf->l_watt_min / v_in;

	float lo_in_max = utils_min_abs(lo_in_max_watt, lo_in_max_batt);
	float lo_in_min = lo_in_min_watt;

	// BMS limits
	bms_update_limits(&lo_in_min,  &lo_in_max, conf->l_in_current_min, conf->l_in_current_max);

	conf->lo_in_current_max = utils_min_abs(conf->l_in_current_max, lo_in_max);
	conf->lo_in_current_min = utils_min_abs(conf->l_in_current_min, lo_in_min);

	// Maximum current right now
//	float duty_abs = fabsf(mc_interface_get_duty_cycle_now());
//
//	// TODO: This is not an elegant solution.
//	if (m_conf.motor_type == MOTOR_TYPE_FOC) {
//		duty_abs *= SQRT3_BY_2;
//	}
//
//	if (duty_abs > 0.001) {
//		conf->lo_current_motor_max_now = utils_min_abs(conf->lo_current_max, conf->lo_in_current_max / duty_abs);
//		conf->lo_current_motor_min_now = utils_min_abs(conf->lo_current_min, conf->lo_in_current_min / duty_abs);
//	} else {
//		conf->lo_current_motor_max_now = conf->lo_current_max;
//		conf->lo_current_motor_min_now = conf->lo_current_min;
//	}

	// Note: The above code should work, but many people have reported issues with it. Leaving it
	// disabled for now until I have done more investigation.
	conf->lo_current_motor_max_now = conf->lo_current_max;
	conf->lo_current_motor_min_now = conf->lo_current_min;
}

/**
 * 获取当前电机状态指针
 *
 * 根据当前选择的电机返回对应的状态结构体指针。
 * 这是一个核心的内部函数，被所有接口函数使用。
 *
 * 选择逻辑：
 * - 双电机系统：根据mc_interface_motor_now()的返回值选择
 * - 单电机系统：始终返回电机1的状态
 *
 * 设计优势：
 * - 统一的电机状态访问
 * - 简化双电机代码逻辑
 * - 提供类型安全的访问
 *
 * @return 指向当前电机状态的易失性指针
 */
static volatile motor_if_state_t *motor_now(void) {
#ifdef HW_HAS_DUAL_MOTORS
	// 双电机系统：根据当前电机编号选择状态结构体
	return mc_interface_motor_now() == 1 ? &m_motor_1 : &m_motor_2;
#else
	// 单电机系统：始终返回电机1状态
	return &m_motor_1;
#endif
}

/**
 * 运行定时器任务
 *
 * 执行周期性的系统维护任务，包括：
 * 1. 电压滤波更新
 * 2. 备份数据更新
 * 3. 采样频率更新
 * 4. 故障状态管理
 *
 * 该函数在定时器线程中周期性调用，
 * 确保系统状态的实时更新和维护。
 *
 * @param motor 电机状态指针
 */
static void run_timer_tasks(volatile motor_if_state_t *motor) {
	// 确定当前是哪个电机并设置线程上下文
	bool is_motor_1 = motor == &m_motor_1;
	mc_interface_select_motor_thread(is_motor_1 ? 1 : 2);

	/*
	 * 电压滤波更新
	 *
	 * 使用低通滤波器对输入电压进行慢速滤波，
	 * 用于显示和保护功能。
	 */
	float voltage_fc = powf(2.0, -(float)motor->m_conf.m_batt_filter_const * 0.25);
	if (UTILS_AGE_S(0) < 10) {
		/*
		 * 启动时快速收敛
		 *
		 * 在系统启动的前10秒内使用更快的滤波器，
		 * 避免启动时的收敛延迟
		 */
		voltage_fc = 0.01;
	}
	UTILS_LP_FAST(motor->m_input_voltage_filtered_slower, motor->m_input_voltage_filtered, voltage_fc);

	/*
	 * 备份数据更新(仅电机1)
	 *
	 * 更新需要持久化存储的数据：
	 * - 里程计数据
	 * - 运行时间数据
	 */
	if (is_motor_1) {
		// 里程计数据更新
		uint64_t odometer = mc_interface_get_distance_abs();
		g_backup.odometer += odometer - m_motor_1.m_odometer_last;
		m_motor_1.m_odometer_last = odometer;

		// 运行时间数据更新
		uint64_t runtime = chVTGetSystemTimeX() / CH_CFG_ST_FREQUENCY;

		/*
		 * 处理时间回绕
		 *
		 * 当系统时间发生回绕时，重置上次记录的时间
		 */
		if (runtime < m_motor_1.m_runtime_last) {
			m_motor_1.m_runtime_last = 0;
		}

		g_backup.runtime += runtime - m_motor_1.m_runtime_last;
		m_motor_1.m_runtime_last = runtime;
	}

	// 更新当前采样频率
	motor->m_f_samp_now = mc_interface_get_sampling_frequency_now();

	// Decrease fault iterations
	if (motor->m_ignore_iterations > 0) {
		motor->m_ignore_iterations--;
	} else {
		if (!(is_motor_1 ? IS_DRV_FAULT() : IS_DRV_FAULT_2())) {
			motor->m_fault_now = FAULT_CODE_NONE;
		}
	}

	if (is_motor_1 ? IS_DRV_FAULT() : IS_DRV_FAULT_2()) {
		motor->m_drv_fault_iterations++;
		if (motor->m_drv_fault_iterations >= motor->m_conf.m_fault_stop_time_ms) {
			HW_RESET_DRV_FAULTS();
			motor->m_drv_fault_iterations = 0;
		}
	} else {
		motor->m_drv_fault_iterations = 0;
	}

	update_override_limits(motor, &motor->m_conf);

	// Update auxiliary output
	switch (motor->m_conf.m_out_aux_mode) {
	case OUT_AUX_MODE_UNUSED:
		break;

	case OUT_AUX_MODE_OFF:
		AUX_OFF();
		break;

	case OUT_AUX_MODE_ON_AFTER_2S:
		if (chVTGetSystemTimeX() >= MS2ST(2000)) {
			AUX_ON();
		}
		break;

	case OUT_AUX_MODE_ON_AFTER_5S:
		if (chVTGetSystemTimeX() >= MS2ST(5000)) {
			AUX_ON();
		}
		break;

	case OUT_AUX_MODE_ON_AFTER_10S:
		if (chVTGetSystemTimeX() >= MS2ST(10000)) {
			AUX_ON();
		}
		break;

	case OUT_AUX_MODE_ON_WHEN_RUNNING:
		if (mc_interface_get_state() == MC_STATE_RUNNING) {
			AUX_ON();
		} else {
			AUX_OFF();
		}
		break;

	case OUT_AUX_MODE_ON_WHEN_NOT_RUNNING:
		if (mc_interface_get_state() == MC_STATE_RUNNING) {
			AUX_OFF();
		} else {
			AUX_ON();
		}
		break;

	case OUT_AUX_MODE_MOTOR_50:
		if (mc_interface_temp_motor_filtered() > 50.0) {AUX_ON();} else {AUX_OFF();}
		break;

	case OUT_AUX_MODE_MOSFET_50:
		if (mc_interface_temp_fet_filtered() > 50.0) {AUX_ON();} else {AUX_OFF();}
		break;

	case OUT_AUX_MODE_MOTOR_70:
		if (mc_interface_temp_motor_filtered() > 70.0) {AUX_ON();} else {AUX_OFF();}
		break;

	case OUT_AUX_MODE_MOSFET_70:
		if (mc_interface_temp_fet_filtered() > 70.0) {AUX_ON();} else {AUX_OFF();}
		break;

	case OUT_AUX_MODE_MOTOR_MOSFET_50:
		if (mc_interface_temp_motor_filtered() > 50.0 ||
				mc_interface_temp_fet_filtered() > 50.0) {AUX_ON();} else {AUX_OFF();}
		break;

	case OUT_AUX_MODE_MOTOR_MOSFET_70:
		if (mc_interface_temp_motor_filtered() > 70.0 ||
				mc_interface_temp_fet_filtered() > 70.0) {AUX_ON();} else {AUX_OFF();}
		break;
	}

	encoder_check_faults(&motor->m_conf, !is_motor_1);

	bool dc_cal_done = mc_interface_dccal_done();
	// TODO: Implement for BLDC and GPDRIVE
	if(motor->m_conf.motor_type == MOTOR_TYPE_FOC && dc_cal_done) {
		float curr0_offset;
		float curr1_offset;
		float curr2_offset;

#ifdef HW_HAS_DUAL_MOTORS
		mcpwm_foc_get_current_offsets(&curr0_offset, &curr1_offset, &curr2_offset, motor == &m_motor_2);
#else
		mcpwm_foc_get_current_offsets(&curr0_offset, &curr1_offset, &curr2_offset, false);
#endif

#ifdef HW_HAS_DUAL_PARALLEL
#define MIDDLE_ADC 4096
#else
#define MIDDLE_ADC 2048
#endif

		if (fabsf(curr0_offset - MIDDLE_ADC) > HW_MAX_CURRENT_OFFSET) {
			mc_interface_fault_stop(FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_1, !is_motor_1, false);
		}
		if (fabsf(curr1_offset - MIDDLE_ADC) > HW_MAX_CURRENT_OFFSET) {
			mc_interface_fault_stop(FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_2, !is_motor_1, false);
		}
#ifdef HW_HAS_3_SHUNTS
		if (fabsf(curr2_offset - MIDDLE_ADC) > HW_MAX_CURRENT_OFFSET) {
			mc_interface_fault_stop(FAULT_CODE_HIGH_OFFSET_CURRENT_SENSOR_3, !is_motor_1, false);
		}
#endif
	}

	// Monitor currents balance. The sum of the 3 currents should be zero
#ifdef HW_HAS_3_SHUNTS
	if (!motor->m_conf.foc_sample_high_current && dc_cal_done) { // This won't work when high current sampling is used
		motor->m_motor_current_unbalance = mc_interface_get_abs_motor_current_unbalance();

		if (fabsf(motor->m_motor_current_unbalance) > fabsf(MCCONF_MAX_CURRENT_UNBALANCE)) {
			UTILS_LP_FAST(motor->m_motor_current_unbalance_error_rate, 1.0, (1 / 1000.0));
		} else {
			UTILS_LP_FAST(motor->m_motor_current_unbalance_error_rate, 0.0, (1 / 1000.0));
		}

		if (motor->m_motor_current_unbalance_error_rate > MCCONF_MAX_CURRENT_UNBALANCE_RATE) {
			mc_interface_fault_stop(FAULT_CODE_UNBALANCED_CURRENTS, !is_motor_1, false);
		}
	}
#endif

#ifdef HW_HAS_WHEEL_SPEED_SENSOR
	hw_update_speed_sensor();
#endif
}

static THD_FUNCTION(timer_thread, arg) {
	(void)arg;

	chRegSetThreadName("mcif timer");

	for(;;) {
		run_timer_tasks(&m_motor_1);
#ifdef HW_HAS_DUAL_MOTORS
		run_timer_tasks(&m_motor_2);
#endif

		chThdSleepMilliseconds(1);
	}
}

static void update_stats(volatile motor_if_state_t *motor) {
	mc_interface_select_motor_thread(motor == (&m_motor_1) ? 1 : 2);

	setup_values val = mc_interface_get_setup_values();

	const double power = mc_interface_get_input_voltage_filtered() * fabsf(val.current_in_tot);
	const double speed = mc_interface_get_speed();
	const double temp_mos = mc_interface_temp_fet_filtered();
	const double temp_mot = mc_interface_temp_motor_filtered();

	motor->m_stats.power_sum += power;
	motor->m_stats.speed_sum += fabs(speed);
	motor->m_stats.temp_mos_sum += temp_mos;
	motor->m_stats.temp_motor_sum += temp_mot;
	motor->m_stats.current_sum += fabs((double)(val.current_tot));
	motor->m_stats.samples += (double)1.0;

	if (power > (double)motor->m_stats.max_power) {
		motor->m_stats.max_power = power;
	}

	if (fabs(speed) > (double)motor->m_stats.max_speed) {
		motor->m_stats.max_speed = fabsf(speed);
	}

	if (temp_mos > (double)motor->m_stats.max_temp_mos) {
		motor->m_stats.max_temp_mos = temp_mos;
	}

	if (temp_mot > (double)motor->m_stats.max_temp_motor) {
		motor->m_stats.max_temp_motor = temp_mot;
	}

	if (fabsf(val.current_tot) > motor->m_stats.max_current) {
		motor->m_stats.max_current = fabsf(val.current_tot);
	}
}

float mc_interface_stat_speed_avg(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	double res = s->speed_sum / s->samples;
	return res;
}

float mc_interface_stat_speed_max(void) {
	return motor_now()->m_stats.max_speed;
}

float mc_interface_stat_power_avg(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	double res = s->power_sum / s->samples;
	return res;
}

float mc_interface_stat_power_max(void) {
	return motor_now()->m_stats.max_power;
}

float mc_interface_stat_current_avg(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	double res = s->current_sum / s->samples;
	return res;
}

float mc_interface_stat_current_max(void) {
	return motor_now()->m_stats.max_current;
}

float mc_interface_stat_temp_mosfet_avg(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	double res = s->temp_mos_sum / s->samples;
	return res;
}

float mc_interface_stat_temp_mosfet_max(void) {
	return motor_now()->m_stats.max_temp_mos;
}

float mc_interface_stat_temp_motor_avg(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	double res = s->temp_motor_sum / s->samples;
	return res;
}

float mc_interface_stat_temp_motor_max(void) {
	return motor_now()->m_stats.max_temp_motor;
}

float mc_interface_stat_count_time(void) {
	return UTILS_AGE_S(motor_now()->m_stats.time_start);
}

void mc_interface_stat_reset(void) {
	volatile setup_stats *s = &motor_now()->m_stats;
	memset((void*)s, 0, sizeof(setup_stats));
	s->time_start = chVTGetSystemTimeX();
	s->max_temp_mos = -300.0;
	s->max_temp_motor = -300.0;
}

static THD_FUNCTION(stat_thread, arg) {
	(void)arg;

	chRegSetThreadName("StatCounter");

	for(;;) {
		update_stats(&m_motor_1);
#ifdef HW_HAS_DUAL_MOTORS
		update_stats(&m_motor_2);
#endif

		chThdSleepMilliseconds(10);
	}
}

static THD_FUNCTION(sample_send_thread, arg) {
	(void)arg;

	chRegSetThreadName("SampleSender");
	sample_send_tp = chThdGetSelfX();

	for(;;) {
		chEvtWaitAny((eventmask_t) 1);

		int len = 0;
		int offset = 0;

		switch (m_sample_mode_last) {
		case DEBUG_SAMPLING_NOW:
		case DEBUG_SAMPLING_START:
			len = m_sample_len;
			break;

		case DEBUG_SAMPLING_TRIGGER_START:
		case DEBUG_SAMPLING_TRIGGER_FAULT:
		case DEBUG_SAMPLING_TRIGGER_START_NOSEND:
		case DEBUG_SAMPLING_TRIGGER_FAULT_NOSEND:
			len = ADC_SAMPLE_MAX_LEN;
			offset = m_sample_trigger - m_sample_len;
			break;

		default:
			break;
		}

		for (int i = 0;i < len;i++) {
			uint8_t buffer[40];
			int32_t index = 0;
			int ind_samp = i + offset;

			while (ind_samp >= ADC_SAMPLE_MAX_LEN) {
				ind_samp -= ADC_SAMPLE_MAX_LEN;
			}

			while (ind_samp < 0) {
				ind_samp += ADC_SAMPLE_MAX_LEN;
			}

			buffer[index++] = COMM_SAMPLE_PRINT;

			if (m_sample_raw) {
				buffer_append_float32_auto(buffer, (float)m_curr0_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_curr1_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_ph1_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_ph2_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_ph3_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_vzero_samples[ind_samp], &index);
				buffer_append_float32_auto(buffer, (float)m_curr_fir_samples[ind_samp], &index);
			} else {
				buffer_append_float32_auto(buffer, (float)m_curr0_samples[ind_samp] * FAC_CURRENT, &index);
				buffer_append_float32_auto(buffer, (float)m_curr1_samples[ind_samp] * FAC_CURRENT, &index);
				buffer_append_float32_auto(buffer, ((float)m_ph1_samples[ind_samp] / 4096.0 * V_REG) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR, &index);
				buffer_append_float32_auto(buffer, ((float)m_ph2_samples[ind_samp] / 4096.0 * V_REG) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR, &index);
				buffer_append_float32_auto(buffer, ((float)m_ph3_samples[ind_samp] / 4096.0 * V_REG) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR, &index);
				buffer_append_float32_auto(buffer, ((float)m_vzero_samples[ind_samp] / 4096.0 * V_REG) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_INPUT_FACTOR, &index);
				buffer_append_float32_auto(buffer, (float)m_curr_fir_samples[ind_samp] / (8.0 / FAC_CURRENT), &index);
			}

			buffer_append_float32_auto(buffer, (float)m_f_sw_samples[ind_samp] * 10.0, &index);
			buffer[index++] = m_status_samples[ind_samp];
			buffer[index++] = m_phase_samples[ind_samp];

			send_func_sample(buffer, index);
		}
	}
}

static THD_FUNCTION(fault_stop_thread, arg) {
	(void)arg;

	chRegSetThreadName("Fault Stop");
	fault_stop_tp = chThdGetSelfX();

	for(;;) {
		chEvtWaitAny((eventmask_t) 1);

		fault_data_local fault_data_copy = m_fault_data;

#ifdef HW_HAS_DUAL_MOTORS
		volatile motor_if_state_t *motor = fault_data_copy.is_second_motor ? &m_motor_2 : &m_motor_1;
#else
		volatile motor_if_state_t *motor = &m_motor_1;
#endif

		mc_interface_select_motor_thread(fault_data_copy.is_second_motor ? 2 : 1);

		if (motor->m_fault_now == fault_data_copy.fault_code) {
			motor->m_ignore_iterations = motor->m_conf.m_fault_stop_time_ms;
			continue;
		}

		// Some hardwares always have a DRV-fault at boot. Therefore we do not log it in the
		// beginning to avoid confusing the user. After dccal all faults should be gone if
		// everything is ok.
		bool is_log_ok = (mc_interface_dccal_done() || motor->m_fault_now != FAULT_CODE_DRV);

		if (is_log_ok && motor->m_fault_now == FAULT_CODE_NONE) {
			// Send to terminal fault logger so that all faults and their conditions
			// can be printed for debugging.
			utils_sys_lock_cnt();
			volatile int val_samp = TIM8->CCR1;
			volatile int current_samp = TIM1->CCR4;
			volatile int tim_top = TIM1->ARR;
			utils_sys_unlock_cnt();

			fault_data fdata;
			fdata.motor = fault_data_copy.is_second_motor ? 2 : 1;
			fdata.fault = fault_data_copy.fault_code;
			fdata.current = mc_interface_get_tot_current();
			fdata.current_filtered = mc_interface_get_tot_current_filtered();
			fdata.voltage = GET_INPUT_VOLTAGE();
			fdata.gate_driver_voltage = motor->m_gate_driver_voltage;
			fdata.duty = mc_interface_get_duty_cycle_now();
			fdata.rpm = mc_interface_get_rpm();
			fdata.tacho = mc_interface_get_tachometer_value(false);
			fdata.cycles_running = motor->m_cycles_running;
			fdata.tim_val_samp = val_samp;
			fdata.tim_current_samp = current_samp;
			fdata.tim_top = tim_top;
			fdata.comm_step = mcpwm_get_comm_step();
			fdata.temperature = NTC_TEMP(ADC_IND_TEMP_MOS);
#ifdef HW_HAS_DRV8301
			if (fault_data_copy.fault_code == FAULT_CODE_DRV) {
				fdata.drv8301_faults = drv8301_read_faults();
			}
#elif defined(HW_HAS_DRV8320S)
			if (fault_data_copy.fault_code == FAULT_CODE_DRV) {
				fdata.drv8301_faults = drv8320s_read_faults();
			}
#elif defined(HW_HAS_DRV8323S)
			if (fault_data_copy.fault_code == FAULT_CODE_DRV) {
				fdata.drv8301_faults = drv8323s_read_faults();
			}
#endif
			fdata.info_str = fault_data_copy.info_str;
			fdata.info_argn = fault_data_copy.info_argn;
			fdata.info_args[0] = fault_data_copy.info_args[0];
			fdata.info_args[1] = fault_data_copy.info_args[1];
			fault_data_copy.info_str = 0;
			fault_data_copy.info_argn = 0;
			terminal_add_fault_data(&fdata);
		}

		motor->m_ignore_iterations = motor->m_conf.m_fault_stop_time_ms;

		switch (motor->m_conf.motor_type) {
		case MOTOR_TYPE_BLDC:
		case MOTOR_TYPE_DC:
			mcpwm_stop_pwm();
			break;

		case MOTOR_TYPE_FOC:
			mcpwm_foc_stop_pwm(fault_data_copy.is_second_motor);
			break;

		case MOTOR_TYPE_GPD:
			gpdrive_set_mode(GPD_OUTPUT_MODE_NONE);
			break;

		default:
			break;
		}

		motor->m_fault_now = fault_data_copy.fault_code;
	}
}

/**
 * Get mc_configuration CRC (motor 1 or 2)
 *
 * @param conf
 * Pointer to mc_configuration or NULL for current config
 *
 * @param is_motor_2
 * true if motor2, false if motor1
 * 
 * @return
 * CRC16 (with crc field in struct temporarily set to zero).
 */
unsigned mc_interface_calc_crc(mc_configuration* conf_in, bool is_motor_2) {
	volatile mc_configuration* conf = conf_in;

	if(conf == NULL) {
		if(is_motor_2) {
#ifdef HW_HAS_DUAL_MOTORS
			conf = &(m_motor_2.m_conf);
#else
			return 0; //shouldn't be here
#endif
		} else {
			conf = &(m_motor_1.m_conf);
		}
	}

	unsigned crc_old = conf->crc;
	conf->crc = 0;
	unsigned crc_new = crc16((uint8_t*)conf, sizeof(mc_configuration));
	conf->crc = crc_old;
	return crc_new;
}
