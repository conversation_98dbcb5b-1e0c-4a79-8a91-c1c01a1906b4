// #ifndef _SYS_BASEVALUE_H
// #define _SYS_BASEVALUE_H

// //===== 系统基值结构体 ====================================
// typedef struct 
// {   
// 	//输出
// 	float  fVoltBaseValueGrid;   	//系统输出电压基值
// 	float  fVoltBaseValue;   		//系统输出电压基值
// 	float  fCurrBaseValue;   		//系统电流基值
// 	float  fSpeedBaseValue;  		//系统转速基值
// 	float  fThetaBaseValue;  		//系统电角度基值
// 	float  fPowerBaseValue; 		//系统功率基值
// 	float  fZBaseValue;				//系统阻抗基值
// 	float  fLBaseValue;				//系统电感基值
// 	float  fPhirBaseValue;			//系统磁链基值
// 	float  fOmegBaseValue;			//系统机械角度基值
// 	float  fTeBaseValue;			//系统转矩基值
// 	float  fJBaseValue;				//转动惯量基值
// 	float  fFBaseValue;				//摩擦系数基值
// 	float  fHBaseValue;				//惯性时间常数基值

// 	//函数
// 	void   (*pfnSysBaseValueCal)(); //系统基值计算
// } ARM_TypeSysBaseValue;

// //初始值
// #define SysBaseValue_DEFAULTS {0.0,0.0,0.0,0.0,0.0,0.0,0.0, \
//                                0.0,0.0,0.0,0.0,0.0,0.0,0.0, \
//                                (void (*)(Uint32))fnSysBaseValueCal, \
//                                }
// //函数声明
// void ARM_fnSysBaseValueCal(ARM_TypeSysBaseValue *p);

// extern ARM_TypeSysBaseValue ARM_SysBaseValue;

// #endif  //END _SYS_BASEVALUE_H