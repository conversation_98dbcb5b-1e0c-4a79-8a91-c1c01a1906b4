#include "AnoPTv8Cmd.h"

/**
 * @brief 命令信息列表指针数组
 */
const _st_cmd_info * pCmdInfoList[ANOPTV8_CMD_MAXCOUNT];

/**
 * @brief 已注册的命令数量
 */
int cmdCount = 0;

/**
 * @brief 命令帧解析函数
 * @param p 待解析的数据帧指针
 * @details 根据帧ID进行不同的处理:
 *          - 0xC0: 执行命令
 *          - 0xC1: 读取命令信息
 */
void AnoPTv8CmdFrameAnl(const _un_frame_v8 *p)
{
    switch (p->frame.frameid)
    {
    case 0xC0:
        //CMD
        for(uint16_t i=0; i<AnoPTv8CmdGetCount(); i++)
        {
            _st_cmd_info _ci = *AnoPTv8CmdGetInfo(i);
            if(p->frame.data[0] == _ci.cmd.cmdid0 && p->frame.data[1] == _ci.cmd.cmdid1 && p->frame.data[2] == _ci.cmd.cmdid2)
            {
                AnoPTv8SendCheck(p->frame.sdevid, p->frame.frameid, p->frame.sumcheck, p->frame.addcheck);
                _ci.pFun();
                return;
            }
        }
        break;
    case 0xC1:
        //CMD读取命令
        if (p->frame.data[0] == 0)
        {
            //读取CMD个数
            AnoPTv8SendCmdNum(p->frame.sdevid);
        }
        else if (p->frame.data[0] == 1)
        {
            //读取CMD信息
            AnoPTv8SendCmdInfo(p->frame.sdevid, *(uint16_t *)(p->frame.data+1));
        }
        break;
    }
}

/**
 * @brief 获取已注册的命令数量
 * @return 已注册的命令数量
 */
int	AnoPTv8CmdGetCount(void)
{
    return cmdCount;
}

/**
 * @brief 注册新命令
 * @param _pi 命令信息结构体指针
 * @details 检查是否有重复命令(当前未启用),如果命令数量未超过最大值则注册新命令
 */
void AnoPTv8CmdRegister(const _st_cmd_info * _pi)
{
    //先检查是否有重复命令
    if(0)
    {
        for(int i=0; i<cmdCount; i++)
        {
            if(_pi->cmd.cmdid0 == pCmdInfoList[i]->cmd.cmdid0
                    && _pi->cmd.cmdid1 == pCmdInfoList[i]->cmd.cmdid1
                    && _pi->cmd.cmdid2 == pCmdInfoList[i]->cmd.cmdid2)
            {
                return;
            }
        }
    }
    if(cmdCount <= (ANOPTV8_CMD_MAXCOUNT-1))
    {
        pCmdInfoList[cmdCount++] = _pi;
    }
}

/**
 * @brief 获取指定命令的信息
 * @param cmdid 命令ID
 * @return 命令信息结构体指针,如果命令ID无效则返回NULL
 */
const _st_cmd_info * AnoPTv8CmdGetInfo(uint16_t cmdid)
{
    if(cmdid > AnoPTv8CmdGetCount())
        return 0;
    return pCmdInfoList[cmdid];
}
