/**
 * @file MotorCmd.c
 * @brief 电机控制命令相关功能实现
 * @details 包含电机启停、自检、故障处理等命令的定义和处理函数
 */

#include "MotorCmd.h"
#include "AnoPTv8Par.h"
#include "AnoPTv8Run.h"
#include "SysCtl_AllHeaders.h"
#include "AnoPTv8Cmd.h"
#include "AnoPTv8Par.h"
#include "SysFSM.h"
#include "delay.h"
// 帧发送使能标志管理
frame_enable_flags_t gFrameFlags;

/**
 * @brief 设置指定帧的发送使能标志
 * @param frame_id 帧ID(1:F1帧 2:F2帧 3:F3帧 4:F4帧 5:F5帧)
 * @param enable 使能标志 0:禁止发送 1:使能发送
 */
void SetFrameSendEnable(uint8_t frame_id, uint8_t enable)
{
    // 检查范围
    if(frame_id < 1 || frame_id > 6) {
        return;
    }
    
    // 计算位掩码
    uint8_t bit_mask = 1 << (frame_id - 1);
    
    // 使用三元运算符合并条件判断
    gFrameFlags.all = enable ? 
                     (gFrameFlags.all | bit_mask) :  // 使能: 设置位
                     (gFrameFlags.all & ~bit_mask);  // 禁用: 清除位
}

/**
 * @brief 获取所有帧发送标志
 * @return 返回帧发送标志组合值
 *         bit0: F1帧发送使能
 *         bit1: F2帧发送使能
 *         bit2: F3帧发送使能
 *         bit3: F4帧发送使能
 *         bit4: F5帧发送使能
 *         bit5: F6帧发送使能
 */
uint8_t GetFrameSendEnable(void)
{
    return gFrameFlags.all;
}

/**
 * @brief 波形控制命令处理函数
 * @details 处理上位机发送的波形控制命令,控制F1/F2/F3/F4/F5帧的发送和暂停
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_WaveCtrl(void)
{
    // 一次性获取所有控制命令所需的信息
    uint8_t *cmd_data = &AnoPTv8CurrentAnlFrame.data[3];  // 帧控制参数起始位置
    
    gFrameFlags.all = (gFrameFlags.all & 0xC0) |  // 保留高2位
                    ((cmd_data[0] & 0x01) |       // F1帧
                    ((cmd_data[1] & 0x01) << 1) | // F2帧
                    ((cmd_data[2] & 0x01) << 2) | // F3帧
                    ((cmd_data[3] & 0x01) << 3) | // F4帧
                    ((cmd_data[4] & 0x01) << 4) | // F5帧
                    ((cmd_data[5] & 0x01) << 5)); // F6帧
    
    // 发送命令响应信息
    AnoPTv8SendStr(ANOPTV8DEVID_SWJ, LOG_COLOR_GREEN, "Wave Control Update");
}

/**
 * @brief 电机启动命令处理函数
 * @details 处理上位机发送的电机启动命令,执行电机启动流程
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorStart(void)
{
    // TODO: 实现电机启动逻辑
     g_SystemStatus.cmd.bits.motor_start = TRUE;
     g_SystemStatus.cmd.bits.motor_stop = FALSE;
}

/**
 * @brief 电机停止命令处理函数
 * @details 处理上位机发送的电机停止命令,执行电机正常停止流程
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorStop(void)
{
    // TODO: 实现电机停止逻辑
    g_SystemStatus.cmd.bits.motor_stop = TRUE;
    g_SystemStatus.cmd.bits.motor_start = FALSE;
    //*pCsvParamSpeed_ref = 0;
    gMotorData.f1.motor_speed_ref = 0;

}

/**
 * @brief 电机紧急停止命令处理函数
 * @details 处理上位机发送的紧急停止命令,执行电机紧急制动流程
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorReset(void)
{
    // TODO: 实现电机紧急停止逻辑
    g_SystemStatus.cmd.bits.motor_stop = TRUE;
    g_SystemStatus.cmd.bits.motor_start = FALSE;
    g_SystemStatus.cmd.bits.motor_reset = TRUE;
}

/**
 * @brief 电机磁链对齐命令处理函数
 * @details 处理上位机发送的磁链对齐命令,执行电机磁链对齐流程
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorAlign(void)
{
    // TODO: 实现磁链对齐逻辑

}
/**
 * @brief 电机自检命令处理函数
 * @details 处理上位机发送的自检命令,执行电机手动自检流程
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorSelfTest(void)
{

}

/**
 * @brief 电机故障查询命令处理函数
 * @details 处理上位机发送的故障查询命令,返回当前故障状态
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorGetFault(void)
{
    // TODO: 实现故障查询逻辑
    g_SystemStatus.cmd.bits.motor_status_query = TRUE;
}

/**
 * @brief 电机故障清除命令处理函数
 * @details 处理上位机发送的故障清除命令,清除当前故障状态
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_MotorClearFault(void)
{
    // TODO: 实现故障清除逻辑
    g_SystemStatus.cmd.bits.motor_clear_fault = TRUE;
}

/**
 * @brief 停止所有波形发送命令处理函数
 * @details 处理上位机发送的停止命令,关闭所有波形的发送
 * @param void
 * @return void
 */
static void AnoPTv8CmdFun_WaveStopAll(void)
{
    gFrameFlags.all=0x00;

    // 发送命令响应信息
    AnoPTv8SendStr(ANOPTV8DEVID_SWJ, LOG_COLOR_GREEN, "所有波形停止发送！");
}

// 命令信息定义
static const _st_cmd_info _pCmdInfoMotorStart = {
    {CMD_MOTOR_START, 0xA0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_Start",
    "启动电机运行",
    AnoPTv8CmdFun_MotorStart
};

static const _st_cmd_info _pCmdInfoMotorStop = {
    {CMD_MOTOR_STOP, 0xA0, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_Stop",
    "正常停止电机",
    AnoPTv8CmdFun_MotorStop
};

static const _st_cmd_info _pCmdInfoMotorEStop = {
    {CMD_MOTOR_RESET, 0xA0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_Reset",
    "执行电机复位",
    AnoPTv8CmdFun_MotorReset
};

static const _st_cmd_info _pCmdInfoMotorSelfTest = {
    {CMD_MOTOR_SELFTEST, 0xA0, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_TestStart",
    "电机自检重启",
    AnoPTv8CmdFun_MotorSelfTest
};

static const _st_cmd_info _pCmdInfoMotorGetFault = {
    {CMD_MOTOR_GET_FAULT, 0xA0, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_GetFault",
    "查询电机状态",
    AnoPTv8CmdFun_MotorGetFault
};

static const _st_cmd_info _pCmdInfoMotorClearFault = {
    {CMD_MOTOR_CLR_FAULT, 0xA0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    "Motor_ClearFault",
    "清除电机故障状态",
    AnoPTv8CmdFun_MotorClearFault
};

// 波形控制命令信息定义
static const _st_cmd_info _pCmdInfoWaveCtrl = {
    {CMD_WAVE_CTRL, 0xA0, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00},  // 六个uint8参数
    "Wave_Ctrl",
    "波形发送控制",
    AnoPTv8CmdFun_WaveCtrl
};

// 停止所有波形命令信息定义
static const _st_cmd_info _pCmdInfoWaveStopAll = {
    {CMD_WAVE_STOP_ALL, 0xA0, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},  // 无参数
    "Wave_Stop_All",
    "停止所有波形发送",
    AnoPTv8CmdFun_WaveStopAll
};

/**
 * @brief 电机命令初始化函数
 * @details 注册所有电机相关命令到命令处理系统
 * @param void
 * @return void
 */
void MotorCmdInit(void)
{
    // 注册所有命令
    AnoPTv8CmdRegister(&_pCmdInfoMotorStart);
    AnoPTv8CmdRegister(&_pCmdInfoMotorStop);
    AnoPTv8CmdRegister(&_pCmdInfoMotorEStop);
    AnoPTv8CmdRegister(&_pCmdInfoMotorSelfTest);
    AnoPTv8CmdRegister(&_pCmdInfoMotorGetFault);
    AnoPTv8CmdRegister(&_pCmdInfoMotorClearFault);
    // 注册波形控制命令
    AnoPTv8CmdRegister(&_pCmdInfoWaveCtrl);
    AnoPTv8CmdRegister(&_pCmdInfoWaveStopAll);
}


void AnoPTv8_Init(void)
{
    MotorCmdInit();
}


/**
 * @brief 分散负载系统状态打印函数(简化版)
 * @note  每10ms调用一次，分步打印系统状态信息，每步骤只使用一个打印函数
 * @param void
 * @return void
 */
void Print_System_Status_Distributed(void)
{

}
