/**********************************************************
 * @file     Sys_Protect.c
 * @brief    电机驱动系统保护模块
 * <AUTHOR>
 * @date     2025-03-12
 * @version  V2.1.0
 *
 * 模块概述:
 * ----------------------------------------------------------
 * 本模块实现电机驱动系统的多级保护功能，提供全面、可靠的
 * 实时安全保护机制，确保系统在各种工况下的安全运行。
 * 
 * 主要功能:
 * ----------------------------------------------------------
 * 1. 电气参数保护：电流、电压、温度、转速的分级阈值保护
 * 2. 硬件故障保护：基于IO信号的快速硬件故障检测
 * 3. 智能限制控制：根据故障严重程度动态调整系统运行参数
 * 4. 故障状态管理：集中式告警状态管理和故障标志处理
 * 5. 延时确认机制：可配置的故障确认延时，避免误触发
 * 
 * 保护策略:
 * ----------------------------------------------------------
 * 1. 分级保护：L1预警、L2限制、L3故障三级保护体系
 * 2. 频率分离：高频检测快变参数，低频检测缓变参数
 * 3. 智能恢复：参数恢复正常时自动清除相应故障标志
 * 4. 安全限制：最低50%限制系数，避免急减速造成二次损害
 * 5. 职责分离：保护检测与控制执行分离，提高系统可靠性
 * 
 * 保护等级:
 * ----------------------------------------------------------
 * - L1级（预警）：参数轻微超限，发出告警但不限制运行
 * - L2级（限制）：参数中度超限，启动降额运行保护设备
 * - L3级（故障）：参数严重超限，触发故障标志要求立即停机
 * 
 * 执行机制:
 * ----------------------------------------------------------
 * - 高频保护：500μs周期，监测电流、电压、转速、IO状态
 * - 低频保护：5ms周期，监测温度等缓变参数
 * - 时间管理：基于ADC中断的精确时间基准
 * - 状态同步：与主状态机协调的故障处理流程
 * 
 * 性能特点:
 * ----------------------------------------------------------
 * - 响应速度：硬件IO保护<5μs，软件保护<20μs
 * - 资源占用：CPU占用率<10%，内存占用优化
 * - 可维护性：模块化设计，新增保护项配置简单
 * - 可靠性：多重安全机制，确保故障状态下的系统安全
 * 
 * 使用方法:
 * ----------------------------------------------------------
 * 1. 在ADC中断中调用Protection_Process_500us()执行保护检查
 * 2. 在主状态机中根据g_SystemStatus.work.bits.sys_fault执行故障处理
 * 3. 在故障状态中调用Clear_Recovered_Fault_Flags()清除已恢复故障
 * 4. 通过Get_Iq_Limit_Coefficient()等函数获取当前限制系数
 * 
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 保护模块只负责检测和设置标志，具体控制动作由主状态机执行
 * 2. 依赖ADC采样完成标志，确保数据有效性
 * 3. L3级保护触发后必须执行安全关断流程
 * 4. 保护参数可根据具体应用需求在g_ProtectParams中调整
 **********************************************************/

#include "Sys_Protect.h"
#include "Sensor_Drive.h"
#include "sysTypeDef.h"
#include "Sys_TimerEvent.h"
#include "at32a423_wk_config.h"
#include "SysFSM.h"
#include "HWInterface.h"


/********************    全局变量定义  start   ******************************/
/* 定义全局告警状态变量 */

/* 基于ADC中断的时间管理系统 - 直接毫秒计数优化版 */
volatile uint32_t g_protect_time_ms = 0;               // 毫秒计数器，保护模块专用时间基数
static volatile uint8_t g_ms_toggle = 0;               // 毫秒切换标志，用于500us到1ms的转换
static volatile uint32_t g_temp_protect_counter = 0;   // 温度保护计数器，每10次ADC中断+1

/* 保护系统时间管理宏定义 - 直接使用毫秒计数器 */
#define TEMP_PROTECT_INTERVAL       10                     // 温度保护间隔：10*500us = 5ms
#define PRO_GET_TIME_MS             (g_protect_time_ms)    // 保护模块专用时间获取（毫秒）

/* 定义延时保护变量 */
ProtectStatus_TypeDef g_ProtectStatus = {0}; 

/* IO保护状态数组*/
static IO_Protect_Status_TypeDef g_IO_ProtectStatus[IO_PROTECT_MAX] = {0};

/* IO保护结果*/
IO_Protect_Result_TypeDef g_IO_ProtectResult = {0};

/* 状态变量 */
static TimeProtect_Status_TypeDef g_TimeProtectStatus = {0};

/* IO保护参数数量 */
#define IO_PROTECT_PARAM_COUNT (sizeof(g_IO_ProtectParams) / sizeof(IO_Protect_Param_TypeDef))


/********************    全局变量定义  end   ******************************/

/********************    保护参数配置  start   ******************************/
/* 定义保护参数配置 */
const ProtectParams_TypeDef g_ProtectParams = {
    .current = {//电流保护参数(有效值)
        .L1 = {130.0f, 500},    /* 150A, 500ms - 允许短时过载 */
        .L2 = {140.0f, 200},    /* 180A, 200ms - 严重过流需快速限制 */
        .L3 = {200.0f, 0}       /* 200A, 0ms - 危险过流立即保护 */
    },
    .current_instant = {//相电流保护参数(瞬时值)（50us中断调用）
        .L1 = {80.0f, 0},     /* 200A, 0ms - 瞬时过流预警 */
        .L2 = {100.0f, 0},     /* 250A, 0ms - 瞬时过流限制 */
        .L3 = {200.0f, 0}      /* 300A, 0ms - 瞬时过流立即保护 */
    },
    .current_q = {//Q轴电流保护参数
        .L1 = {120.0f, 300},    /* 120A, 300ms - Q轴电流预警 */
        .L2 = {150.0f, 100},    /* 150A, 100ms - Q轴电流限制 */
        .L3 = {180.0f, 0}       /* 180A, 0ms - Q轴电流立即保护 */
    },
    .bus_overvol = {//母线过压保护参数
        .L1 = {760.0f, 1000},   /* 760V, 1s - 短暂过压可能由负载突变引起 */
        .L2 = {780.0f, 500},    /* 780V, 500ms - 持续过压降额运行 */
        .L3 = {800.0f, 0}       /* 800V, 0ms - 紧急关断保护 */
    }, 
    .bus_undervol = {//母线欠压保护参数
        .L1 = {420.0f, 2000},   /* 420V, 2s - 允许瞬时跌落 */
        .L2 = {390.0f, 1000},   /* 390V, 1s - 持续欠压需降功率运行 */
        .L3 = {40.0f, 0}       /* 360V, 0ms - 深度欠压立即停机,测试原因改到40Ｖ */
    },
    .motor_temp = {//电机温度保护参数
        .L1 = {80.0f, 600000},  /* 80°C, 10min - 温度缓变告警 */
        .L2 = {90.0f, 300000},  /* 90°C, 5min - 触发降额运行 */
        .L3 = {180.0f, 0}       /* 150°C, 0ms - 紧急停机，防止永磁体退磁/绝缘失效 */
    },
    .driver_temp = {//驱动器温度保护参数
        .L1 = {80.0f, 300000},  /* 80°C, 5min - 预警提示 */
        .L2 = {90.0f, 120000},  /* 90°C, 2min - 降频运行，避免IGBT结温超限 */
        .L3 = {105.0f, 0}        /* 105°C, 0ms - 立即关断，保护功率模块 */
    },
    .cap_temp = {//电容温度保护参数
        .L1 = {85.0f, 900000},  /* 85°C, 15min - 电解电容寿命与温度强相关，提前预警 */
        .L2 = {95.0f, 300000},  /* 95°C, 5min - 电容容量下降风险 */
        .L3 = {105.0f, 100}    /* 105°C, 100MS - 防止电容爆裂 */
    },
    .overspeed = {//电机超速保护参数
        .L1 = {11061.0f, 1000},  /* 11061rpm, 1000ms - 短时超速允许 */
        .L2 = {11456.0f, 500},  /* 11456rpm, 500ms - 机械强度风险 */
        .L3 = {11852.0f, 0}     /* 11852rpm, 0ms - 立即封锁PWM */
    },
    .v28_undervol = {//28V欠压保护参数
        .L1 = {19.0f, 5000},    /* 19V, 5s - 控制电源波动容忍 */
        .L2 = {18.0f, 2000},    /* 18V, 2s - 维持最小系统运行 */
        .L3 = {17.0f, 500}      /* 17V, 500ms - 防止MCU复位异常 */
    },
    .v28_overvol = {//28V过压保护参数
        .L1 = {32.0f, 1000},    /* 32V, 1s - 抑制电源浪涌 */ 
        .L2 = {34.0f, 200},     /* 34V, 200ms - 保护低压器件 */
        .L3 = {36.0f, 5}        /* 36V, 5ms - 切断辅助电源 */
    }
    ,
    .phase_unbalance = {//三相不平衡保护参数
        .L1 = {10.0f, 10000},   /* 10%, 10s - 轻微不平衡预警，可能是负载不均 */
        .L2 = {15.0f, 5000},    /* 15%, 5s - 中度不平衡，需降额运行避免电机振动 */
        .L3 = {20.0f, 1000}     /* 20%, 1s - 严重不平衡，防止电机损坏和轴承磨损 */
    },
};

/* IO保护参数配置表 */
const IO_Protect_Param_TypeDef g_IO_ProtectParams[] = {
    /*保护类型                 GPIO端口                GPIO引脚         有效电平 消抖计数  告警等级     故障掩码*/
    {IO_PROTECT_CURRENT_POS,   OVER_UI_CUR1_GPIO_PORT, OVER_UI_CUR1_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.ui_cur_pos = 1}}}, // U相正向过流
    {IO_PROTECT_CURRENT_NEG,   OVER_UI_CUR0_GPIO_PORT, OVER_UI_CUR0_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.ui_cur_neg = 1}}}, // U相负向过流
    {IO_PROTECT_CURRENT_POS,   OVER_VI_CUR1_GPIO_PORT, OVER_VI_CUR1_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.vi_cur_pos = 1}}}, // V相正向过流
    {IO_PROTECT_CURRENT_NEG,   OVER_VI_CUR0_GPIO_PORT, OVER_VI_CUR0_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.vi_cur_neg = 1}}}, // V相负向过流
    {IO_PROTECT_CURRENT_POS,   OVER_WI_CUR1_GPIO_PORT, OVER_WI_CUR1_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.wi_cur_pos = 1}}}, // W相正向过流
    {IO_PROTECT_CURRENT_NEG,   OVER_WI_CUR0_GPIO_PORT, OVER_WI_CUR0_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.wi_cur_neg = 1}}}, // W相负向过流
    {IO_PROTECT_BUS_VOLTAGE,   OVER_VOL_BUS_GPIO_PORT, OVER_VOL_BUS_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.bus_vol    = 1}}}, // 母线过压
    {IO_PROTECT_PHASE_VOLTAGE, OVER_VOL_UV1_GPIO_PORT, OVER_VOL_UV1_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.uv_vol_pos = 1}}}, // UV线正向过压
    {IO_PROTECT_PHASE_VOLTAGE, OVER_VOL_UV0_GPIO_PORT, OVER_VOL_UV0_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.uv_vol_neg = 1}}}, // UV线负向过压
    {IO_PROTECT_PHASE_VOLTAGE, OVER_VOL_UW1_GPIO_PORT, OVER_VOL_UW1_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.uw_vol_pos = 1}}}, // UW线正向过压
    {IO_PROTECT_PHASE_VOLTAGE, OVER_VOL_UW0_GPIO_PORT, OVER_VOL_UW0_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.uw_vol_neg = 1}}}, // UW线负向过压
    {IO_PROTECT_MCU_FAULT,     Fail_FLT_MCU_GPIO_PORT, Fail_FLT_MCU_PIN, 0,      0,       WARN_LEVEL_3, {.bits = {.flt_mcu    = 1}}}  // 光耦故障信号
};
/********************    保护参数配置  end   ******************************/

/********************    时间保护相关  start   ******************************/

/* 告警等级配置参数（常量） */
static const TimeBasedLimit_Config_TypeDef g_TimeLimitConfig[3] = {
    // WARN_LEVEL_1 配置
    {
        .time_thresholds = {10000, 30000, UINT32_MAX},   /* 10秒, 30秒, 无限大 */
        .iq_coeffs       = {0.95f, 0.90f, 0.85f},        /* 95%,  90%,  85% */
        .speed_coeffs    = {0.95f, 0.90f, 0.85f}         /* 95%,  90%,  85% */
    },
    // WARN_LEVEL_2 配置
    {
        .time_thresholds = {5000, 15000, UINT32_MAX},    /* 5秒, 15秒, 无限大 */
        .iq_coeffs       = {0.80f, 0.75f, 0.65f},        /* 80%, 75%,  65% */
        .speed_coeffs    = {0.80f, 0.75f, 0.65f}         /* 80%, 75%,  65% */
    },
    // WARN_LEVEL_3 配置
    {
        .time_thresholds = {0, 0, 0},                   /* 三级告警无时间分级 */
        .iq_coeffs       = {0.6f, 0.6f, 0.6f},           /* 50% - 最低限制，避免急减速 */
        .speed_coeffs    = {0.6f, 0.6f, 0.6f}            /* 50% - 最低限制，避免急减速 */
    }
};
/********************    时间保护相关  end   ******************************/

/********************    通用保护检查模板函数  start   ******************************/

// 函数声明
static void Update_Warning_And_Limits(WarnLevel_TypeDef max_warn_level);

/**
 * @brief  通用保护检查函数
 * @param  current_value: 当前测量值
 * @param  thresholds: 三级阈值配置
 * @param  status: 延时状态结构
 * @param  warn_level_ptr: 告警等级指针
 * @param  fault_flag_ptr: 故障标志指针
 * @param  compare_direction: 比较方向 (0=负比较, 1=正比较)
 * @retval WarnLevel_TypeDef: 检查结果等级
 * @note   三级保护检查逻辑
 */
WarnLevel_TypeDef Check_Protection_Generic(
    float current_value,
    const ProtectLevels_TypeDef* thresholds,
    ProtectDelay_TypeDef* status,
    WarnLevel_TypeDef* warn_level_ptr,
    uint8_t* fault_flag_ptr,
    uint8_t compare_direction)
{
    WarnLevel_TypeDef new_level = WARN_LEVEL_NONE;
    uint32_t delay_ms = 0;
    
    // 三级阈值检查
    if (compare_direction) {
        // （过压、过流、过温、超速等）
        if (current_value > thresholds->L3.threshold) {
            new_level = WARN_LEVEL_3;
            delay_ms = thresholds->L3.delay_ms;
        } else if (current_value > thresholds->L2.threshold) {
            new_level = WARN_LEVEL_2;
            delay_ms = thresholds->L2.delay_ms;
        } else if (current_value > thresholds->L1.threshold) {
            new_level = WARN_LEVEL_1;
            delay_ms = thresholds->L1.delay_ms;
        }
    } else {
        // （欠压、欠流等）
        if (current_value < thresholds->L3.threshold) {
            new_level = WARN_LEVEL_3;
            delay_ms = thresholds->L3.delay_ms;
        } else if (current_value < thresholds->L2.threshold) {
            new_level = WARN_LEVEL_2;
            delay_ms = thresholds->L2.delay_ms;
        } else if (current_value < thresholds->L1.threshold) {
            new_level = WARN_LEVEL_1;
            delay_ms = thresholds->L1.delay_ms;
        }
    }
    
    // 处理延时逻辑
    if (new_level == WARN_LEVEL_NONE) {
        // 无告警，停止延时并清除状态
        Stop_Protect_Delay(status);
        if (warn_level_ptr) *warn_level_ptr = WARN_LEVEL_NONE;
        if (fault_flag_ptr) *fault_flag_ptr = 0;
        return WARN_LEVEL_NONE;
    } else {
        // 检查是否需要开始新的延时
        if (!CHECK_FLAG(status, PROTECT_FLAG_COUNTING) || status->level != new_level) {
            Start_Protect_Delay(status, delay_ms, new_level);
        }
    }
    
    // 检查延时是否到达
    if (Check_Protect_Delay(status)) {
        // 延时到达，设置告警状态
        if (warn_level_ptr) *warn_level_ptr = status->level;
        
        // 设置故障标志（仅L3级别）
        if (fault_flag_ptr && status->level == WARN_LEVEL_3) {
            *fault_flag_ptr = 1;
        }
        
        return status->level;
    }
    
    // 延时未到达，保持之前的状态
    return warn_level_ptr ? *warn_level_ptr : WARN_LEVEL_NONE;
}
/********************    通用保护检查模板函数  end   ******************************/


/********************    告警计时器功能  start   ******************************/
/**
 * @brief  更新告警计时器和限制系数
 * @note   根据当前告警状态更新计时器和系统限制系数
 * @retval None
 */
void Update_Warning_And_Limits(WarnLevel_TypeDef max_warn_level)
{
    // 幅值处理
    if (max_warn_level > WARN_LEVEL_3) {
        max_warn_level = WARN_LEVEL_3;
    }
    
    // 非告警状态直接设置为1.0（无限制）
    if (max_warn_level == WARN_LEVEL_NONE) {
        g_SystemStatus.status.iq_limit_coeff = 1.0f;
        g_SystemStatus.status.speed_limit_coeff = 1.0f;
        
        // 清除所有告警计时器
        for (uint8_t i = WARN_LEVEL_1; i <= WARN_LEVEL_3; i++) {
            g_TimeProtectStatus.levels[i-1].active = 0;
        }
        return;
    }
    
    // 更新告警计时器状态
    for (uint8_t i = WARN_LEVEL_1; i <= WARN_LEVEL_3; i++) {
        uint8_t idx = i - 1;
        if (max_warn_level == i) {
            if (!g_TimeProtectStatus.levels[idx].active) {
                g_TimeProtectStatus.levels[idx].start_time = PRO_GET_TIME_MS;  // 毫秒
                g_TimeProtectStatus.levels[idx].active = 1;
            }
        } else {
            g_TimeProtectStatus.levels[idx].active = 0;
        }
    }
    
    // 索引调整并检查有效性
    uint8_t idx = max_warn_level - 1;
    if (idx >= 3) {
        g_SystemStatus.status.iq_limit_coeff = 1.0f;
        g_SystemStatus.status.speed_limit_coeff = 1.0f;
        return;
    }
    
    // 检查告警状态是否激活
    if (!g_TimeProtectStatus.levels[idx].active) {
        // 设置基础限制系数（无时间增强限制）
        g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[0];
        g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[0];
    } else {
        // 计算告警持续时间并确定系数
        uint32_t duration_ms = PRO_GET_TIME_MS - g_TimeProtectStatus.levels[idx].start_time;
        uint8_t i;
        for (i = 0; i < 3; i++) {
            if (duration_ms < g_TimeLimitConfig[idx].time_thresholds[i]) {
                g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[i];
                g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[i];
                break;
            }
        }
        
        // 超过所有时间阈值，使用最严格限制
        if (i >= 3) {
            g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[2];
            g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[2];
        }
    }
    
    // 确保限制系数在有效范围内（最低0.5，避免急减速）
    if (g_SystemStatus.status.iq_limit_coeff < 0.5f) {
        g_SystemStatus.status.iq_limit_coeff = 0.5f;
    }
    if (g_SystemStatus.status.speed_limit_coeff < 0.5f) {
        g_SystemStatus.status.speed_limit_coeff = 0.5f;
    }
}


/**
 * @brief  主保护处理函数
 * @note   在ADC中断中调用，每500us执行一次，集成所有保护逻辑
 * @param  None
 * @retval None
 */
void Protection_Process_500us(void)
{
    // ==================== 1. 更新时间基准 ====================
    // 无分支的毫秒计数器更新，每2次500us调用更新一次毫秒
    static uint32_t call_count = 0;
    g_protect_time_ms = (++call_count) >> 1;  // 右移1位避免
    
    // 温度保护计数器更新（每5ms更新一次）
    g_temp_protect_counter++;
    if (g_temp_protect_counter >= TEMP_PROTECT_INTERVAL) {
        g_temp_protect_counter = 0;
    }
    
    // ==================== 2. 检查ADC数据有效性 ====================
    if (gADC_Manager.flags.calc_complete != 1) {
        return;  // ADC数据未准备好，直接返回
    }
    
    // ==================== 3. 高频保护检测 ====================
    WarnLevel_TypeDef high_freq_max_level = WARN_LEVEL_NONE;
    WarnLevel_TypeDef level;
    
    // 3.1 电流保护检查
    level = Get_Current_Warning_Level();
    if (level > high_freq_max_level) high_freq_max_level = level;
    
    // 3.2 电压保护检查
    level = Get_Voltage_Warning_Level();
    if (level > high_freq_max_level) high_freq_max_level = level;
    
    // 3.3 超速保护检查
    level = Get_Overspeed_Warning_Level();
    if (level > high_freq_max_level) high_freq_max_level = level;
    
    // 3.4 IO硬件保护扫描（最高优先级）
    uint8_t io_fault = Scan_IO_Protection_Optimized();
    if (io_fault) {
        high_freq_max_level = WARN_LEVEL_3;  // IO故障直接设为最高级
    }
    
    // ==================== 4. 低频保护检测（温度） ====================
    WarnLevel_TypeDef low_freq_max_level = WARN_LEVEL_NONE;
    uint8_t temp_protection_active = 0;  // 温度保护激活标志
    
    // 只在温度保护计数器为0时执行（每5ms一次）
    if (g_temp_protect_counter == 0) {
        low_freq_max_level = Get_Temperature_Warning_Level();
        temp_protection_active = (low_freq_max_level != WARN_LEVEL_NONE);
    } else {
        // 非温度检测周期，保持上次的温度告警状态
        low_freq_max_level = g_SystemStatus.status.warn_level;
        temp_protection_active = (g_SystemStatus.warn.motor_temp != WARN_LEVEL_NONE ||
                                 g_SystemStatus.warn.driver_temp != WARN_LEVEL_NONE ||
                                 g_SystemStatus.warn.cap_temp != WARN_LEVEL_NONE);
    }
    
    // ==================== 5. 综合告警等级计算 ====================
    WarnLevel_TypeDef final_max_level;
    
    if (temp_protection_active) {
        // 温度保护激活时，综合高低频告警等级
        final_max_level = (high_freq_max_level > low_freq_max_level) ? 
                         high_freq_max_level : low_freq_max_level;
    } else {
        // 温度保护未激活时，仅使用高频告警等级
        final_max_level = high_freq_max_level;
    }
    
    // ==================== 6. 更新系统状态 ====================
    // 6.1 更新告警状态
    g_SystemStatus.status.warn_level = final_max_level;
    g_SystemStatus.status.flags.is_warning = (final_max_level != WARN_LEVEL_NONE);
    
    // 6.2 设置系统故障标志
    if (final_max_level >= WARN_LEVEL_3) {
        g_SystemStatus.work.bits.sys_fault = 1;
        g_SystemStatus.status.flags.is_falut = 1;
        // 注意：L3级故障的具体处理措施（如封波、急停等）
        // 应在主状态机中调用专门的故障处理函数完成
        // 保护系统只负责检测和设置标志，不直接执行控制动作
    }
    
    // ==================== 7. 智能限制系数计算 ====================
    Update_Warning_And_Limits(final_max_level);
}

/********************    告警计时器功能  end   ******************************/

/********************    保护延时功能  start   ******************************/

/**
 * @brief  检查延时是否到达
 * @param  delay: 延时计数器指针
 * @retval uint8_t: 1-延时到达，0-未到达
 */
uint8_t Check_Protect_Delay(ProtectDelay_TypeDef* delay)
{
    if (!CHECK_FLAG(delay, PROTECT_FLAG_COUNTING)) {
        return 0;
    }
    
    uint32_t current_time_ms = PRO_GET_TIME_MS;
    
    if (current_time_ms - delay->start_time >= delay->delay_ms) {
        SET_FLAG(delay, PROTECT_FLAG_TRIGGERED);
        return 1;
    }
    return 0;
}

/**
 * @brief  开始延时
 * @param  delay: 延时计数器指针
 * @param  delay_ms: 延时时间(ms)
 * @param  level: 告警等级
 * @retval None
 */
void Start_Protect_Delay(ProtectDelay_TypeDef* delay, uint32_t delay_ms, uint8_t level)
{
    delay->start_time = PRO_GET_TIME_MS;  // 毫秒
    delay->delay_ms = delay_ms;
    delay->level = level;
    SET_FLAG(delay, PROTECT_FLAG_COUNTING);
}

/**
 * @brief  停止延时
 * @param  delay: 延时计数器指针
 * @retval None
 */
void Stop_Protect_Delay(ProtectDelay_TypeDef* delay)
{
    delay->flags = 0;
    delay->level = WARN_LEVEL_NONE;
}
/********************    保护延时功能  end   ******************************/

/********************    告警等级获取功能  start   ******************************/
/**
 * @brief  获取电流告警等级
 * @retval WarnLevel_TypeDef: 告警等级
 */
WarnLevel_TypeDef Get_Current_Warning_Level(void)
{
    float current = Get_Motor_Current();
    
    // 设置告警状态
    return Check_Protection_Generic(
        current,
        &g_ProtectParams.current,
        &g_ProtectStatus.current,
        &g_SystemStatus.warn.current,
        &g_SystemStatus.warn_fault.current,
        1  // （过流）
    );
}

/**
 * @brief  获取电压告警等级
 * @retval WarnLevel_TypeDef: 告警等级
 * @note   使用通用模板函数，简化代码逻辑
 */
WarnLevel_TypeDef Get_Voltage_Warning_Level(void)
{
    WarnLevel_TypeDef max_level = WARN_LEVEL_NONE;
    WarnLevel_TypeDef level;
    
    // 1. 母线过压检查
    level = Check_Protection_Generic(
        Get_Bus_Voltage(),
        &g_ProtectParams.bus_overvol,
        &g_ProtectStatus.bus_overvol,
        &g_SystemStatus.warn.bus_voltage,
        &g_SystemStatus.warn_fault.bus_voltage,
        1  // （过压）
    );
    if (level > max_level) max_level = level;
    
    // 2. 母线欠压检查
    level = Check_Protection_Generic(
        Get_Bus_Voltage(),
        &g_ProtectParams.bus_undervol,
        &g_ProtectStatus.bus_undervol,
        &g_SystemStatus.warn.bus_undervol,
        &g_SystemStatus.warn_fault.bus_undervol,
        0  // （欠压）
    );
    if (level > max_level) max_level = level;
    
    // 3. 28V过压检查
    level = Check_Protection_Generic(
        Get_V28_Main(),
        &g_ProtectParams.v28_overvol,
        &g_ProtectStatus.v28_overvol,
        &g_SystemStatus.warn.v28_overvol,
        &g_SystemStatus.warn_fault.v28_overvol,
        1  // （过压）
    );
    if (level > max_level) max_level = level;
    
    // 4. 28V欠压检查
    level = Check_Protection_Generic(
        Get_V28_Main(),
        &g_ProtectParams.v28_undervol,
        &g_ProtectStatus.v28_undervol,
        &g_SystemStatus.warn.v28_undervol,
        &g_SystemStatus.warn_fault.v28_undervol,
        0  // （欠压）
    );
    if (level > max_level) max_level = level;
    
    return max_level;
}

/**
 * @brief  获取温度告警等级
 * @retval WarnLevel_TypeDef: 告警等级
 */
WarnLevel_TypeDef Get_Temperature_Warning_Level(void)
{
    // 温度保护配置数组
    static const struct {
        float (*get_temp_func)(void);
        const ProtectLevels_TypeDef* thresholds;
        ProtectDelay_TypeDef* status;
        WarnLevel_TypeDef* warn_level_ptr;
        uint8_t* fault_flag_ptr;
    } temp_configs[3] = {
        {Get_Motor_Temperature,     &g_ProtectParams.motor_temp,  &g_ProtectStatus.motor_temp,  &g_SystemStatus.warn.motor_temp,  &g_SystemStatus.warn_fault.motor_temp},
        {Get_Driver_Temperature,    &g_ProtectParams.driver_temp, &g_ProtectStatus.driver_temp, &g_SystemStatus.warn.driver_temp, &g_SystemStatus.warn_fault.driver_temp},
        {Get_Capacitor_Temperature, &g_ProtectParams.cap_temp,    &g_ProtectStatus.cap_temp,    &g_SystemStatus.warn.cap_temp,    &g_SystemStatus.warn_fault.cap_temp}
    };
    
    WarnLevel_TypeDef max_level = WARN_LEVEL_NONE;
    
    // 循环处理所有温度保护项
    for (uint8_t i = 0; i < 3; i++) {
        float temp_value = temp_configs[i].get_temp_func();
        
        // 使用通用模板函数检查温度保护，直接设置告警状态
        WarnLevel_TypeDef result = Check_Protection_Generic(
            temp_value,
            temp_configs[i].thresholds,
            temp_configs[i].status,
            temp_configs[i].warn_level_ptr,
            temp_configs[i].fault_flag_ptr,
            1  // 大于比较（过温）
        );
        
        // 更新最高告警等级
        if (result > max_level) {
            max_level = result;
        }
    }
    
    return max_level;
}

/**
 * @brief  获取电机超速告警等级
 * @retval WarnLevel_TypeDef: 告警等级
 */
WarnLevel_TypeDef Get_Overspeed_Warning_Level(void)
{
    float speed = Get_Motor_Speed();
    
    // 设置告警状态
    return Check_Protection_Generic(
        speed,
        &g_ProtectParams.overspeed,
        &g_ProtectStatus.overspeed,
        &g_SystemStatus.warn.overspeed,
        &g_SystemStatus.warn_fault.overspeed,
        1  // （超速）
    );
}
/********************    告警等级获取功能  end   ******************************/

/********************    IO保护扫描功能  start   ******************************/
/**
 * @brief  扫描所有IO保护信号并执行快速响应（寄存器直读版）
 * @note   直接读取端口寄存器并通过位移构建位图，避免多次函数调用
 * @retval 是否检测到故障(1-检测到，0-未检测到)
 */
uint8_t Scan_IO_Protection_Optimized(void)
{
    // 一次性读取所有相关GPIO端口（低电平有效，所以取反）
    uint16_t gpiod_port = ~(GPIOD->idt);
    uint16_t gpioa_port = ~(GPIOA->idt);
    uint16_t gpiof_port = ~(GPIOF->idt);
    
    // 构建位图
    uint16_t io_status = 0;
    
    // 使用位掩码提取信号
    io_status |= ((gpiod_port & (1 << 15)) ? 1 : 0) << 0;  // UI_CUR1
    io_status |= ((gpiod_port & (1 << 14)) ? 1 : 0) << 3;  // VI_CUR0
    io_status |= ((gpiod_port & (1 << 13)) ? 1 : 0) << 2;  // VI_CUR1
    io_status |= ((gpiod_port & (1 << 12)) ? 1 : 0) << 8;  // VOL_UV0
    io_status |= ((gpiod_port & (1 << 11)) ? 1 : 0) << 7;  // VOL_UV1
    io_status |= ((gpiod_port & (1 << 10)) ? 1 : 0) << 6;  // VOL_BUS
    io_status |= ((gpiod_port & (1 << 9)) ? 1 : 0) << 5;   // WI_CUR0
    io_status |= ((gpiod_port & (1 << 8)) ? 1 : 0) << 4;   // WI_CUR1
    io_status |= ((gpiod_port & (1 << 7)) ? 1 : 0) << 9;   // VOL_UW1
    io_status |= ((gpiod_port & (1 << 6)) ? 1 : 0) << 10;  // VOL_UW0
    
    // 单独处理GPIOA上的OVER_UI_CUR0_PIN (引脚8)
    io_status |= ((gpioa_port & (1 << 8)) ? 1 : 0) << 1;   // UI_CUR0
    
    // 单独处理GPIOF上的Fail_FLT_MCU_PIN (引脚8)
    io_status |= ((gpiof_port & (1 << 8)) ? 1 : 0) << 11;  // FLT_MCU
    
    // 直接赋值到结果结构体
    g_IO_ProtectResult.all = io_status;
    
    // 使用位掩码快速检测不同类型的故障
    uint8_t has_fault = (io_status != 0);  // 是否存在任何故障
    uint16_t current_fault = io_status & 0x3F;  // 电流故障 (0-5位)
    uint16_t bus_vol_fault = io_status & 0x40;  // 母线过压 (6位)
    uint16_t phase_vol_fault = io_status & 0x780;  // 相电压故障 (7-10位) 
    uint16_t mcu_fault = io_status & 0x800;  // MCU故障 (11位)
    
    // 更新告警状态和故障标志
    g_SystemStatus.warn.current = current_fault ? WARN_LEVEL_3 : g_SystemStatus.warn.current;
    g_SystemStatus.warn.bus_voltage = bus_vol_fault ? WARN_LEVEL_3 : g_SystemStatus.warn.bus_voltage;
    g_SystemStatus.warn.phase_voltage = phase_vol_fault ? WARN_LEVEL_3 : g_SystemStatus.warn.phase_voltage;
    g_SystemStatus.warn.mcu_fault = mcu_fault ? WARN_LEVEL_3 : g_SystemStatus.warn.mcu_fault;
    
    // 更新系统故障标志位
    g_SystemStatus.warn_fault.current = current_fault ? 1 : g_SystemStatus.warn_fault.current;
    g_SystemStatus.warn_fault.bus_voltage = bus_vol_fault ? 1 : g_SystemStatus.warn_fault.bus_voltage;
    g_SystemStatus.warn_fault.phase_voltage = phase_vol_fault ? 1 : g_SystemStatus.warn_fault.phase_voltage;
    g_SystemStatus.sys_fault.bits.driver = mcu_fault ? 1 : g_SystemStatus.sys_fault.bits.driver;
    g_SystemStatus.sys_fault.bits.io_fault = has_fault;
    
    return has_fault;
}


/********************    IO保护扫描功能  end   ******************************/

/********************    保护状态重置功能  start   ******************************/
/**
 * @brief  重置指定的保护延时计数器
 * @param  delay: 要重置的延时计数器指针
 * @retval None
 */
void Reset_Protect_Delay(ProtectDelay_TypeDef* p)
{
    if (p == NULL) {
        return;  // 参数检查
    }
    // 清零所有成员
    p->start_time = 0;
    p->delay_ms = 0;
    p->level = WARN_LEVEL_NONE;
    p->flags = 0;
    p->last_value = 0.0f;
}

/**
 * @brief  重置所有保护延时计数器
 * @retval None
 */
void Reset_All_Protect_Delays(void)
{
    // 重置电流保护延时
    Reset_Protect_Delay(&g_ProtectStatus.current);
    Reset_Protect_Delay(&g_ProtectStatus.current_instant);
    Reset_Protect_Delay(&g_ProtectStatus.current_q);
    
    // 重置电压保护延时
    Reset_Protect_Delay(&g_ProtectStatus.bus_overvol);
    Reset_Protect_Delay(&g_ProtectStatus.bus_undervol);
    Reset_Protect_Delay(&g_ProtectStatus.v28_overvol);
    Reset_Protect_Delay(&g_ProtectStatus.v28_undervol);
    
    // 重置温度保护延时
    Reset_Protect_Delay(&g_ProtectStatus.motor_temp);
    Reset_Protect_Delay(&g_ProtectStatus.driver_temp);
    Reset_Protect_Delay(&g_ProtectStatus.cap_temp);
    
    // 重置其他保护延时
    Reset_Protect_Delay(&g_ProtectStatus.overspeed);
    Reset_Protect_Delay(&g_ProtectStatus.phase_unbalance);

    // 重置时间分级保护状态
    memset(&g_TimeProtectStatus.levels[0], 0, sizeof(WarnState_TypeDef));
    memset(&g_TimeProtectStatus.levels[1], 0, sizeof(WarnState_TypeDef));
    memset(&g_TimeProtectStatus.levels[2], 0, sizeof(WarnState_TypeDef));

    // 重置IO保护状态
    Reset_IO_Protect_Status();
}

/**
 * @brief  重置IO保护状态
 * @note   清除所有IO保护的触发标志和全局结果
 * @retval None
 */
void Reset_IO_Protect_Status(void)
{
    // 重置所有IO保护状态结构体
    for(uint8_t i = 0; i < IO_PROTECT_PARAM_COUNT; i++) {
        IO_Protect_Status_TypeDef* status = &g_IO_ProtectStatus[i];
        status->triggered = 0;
    }
    
    // 清除全局IO保护结果
    g_IO_ProtectResult.all = 0;
    
}

/**
 * @brief  重置指定的保护状态结构体
 * @param  status: 要重置的保护状态结构体指针
 * @retval None
 */
void Reset_Protect_Status(ProtectDelay_TypeDef* status)
{
    if (status == NULL) {
        return;  // 参数检查
    }
    
    // 清零整个结构体
    memset(status, 0, sizeof(ProtectDelay_TypeDef));
}

/**
 * @brief  重置全局保护状态
 * @retval None
 */
void Reset_Global_Protect_Status(ProtectStatus_TypeDef* status)
{
    if (status == NULL) {
        return;  // 参数检查
    }
    
    // 清零整个结构体
    memset(status, 0, sizeof(ProtectStatus_TypeDef));
}

/**
 * @brief  获取Q轴电流限制系数（兼容旧接口）
 * @    retval float: 限制系数(0.0-1.0)
 */
float Get_Iq_Limit_Coefficient()
{
    return g_SystemStatus.status.iq_limit_coeff;
}

/**
 * @brief  获取速度限制系数
 * @    retval float: 限制系数(0.0-1.0)
 */
float Get_Speed_Limit_Coefficient()
{
    return g_SystemStatus.status.speed_limit_coeff;
}

/**
 * @brief  根据最新数据立即清除已恢复正常的故障标志位
 * @param  None
 * @retval None
 */
void Clear_Recovered_Fault_Flags(void)
{
    // 获取当前参数值
    float bus_voltage = gADC_Result.bus_voltage;
    float v28_voltage = gADC_Result.v28_voltage;
    float motor_temp = Get_Motor_Temperature();
    float driver_temp = Get_Driver_Temperature();
    float cap_temp = Get_Capacitor_Temperature();
    float motor_current = Get_Motor_Current();
    float motor_speed = Get_Motor_Speed();
    
    // 检查并清除母线过压故障
    if (g_SystemStatus.warn_fault.bus_voltage && 
        bus_voltage < g_ProtectParams.bus_overvol.L1.threshold) {
        g_SystemStatus.warn_fault.bus_voltage = 0;
    }
    
    // 检查并清除母线欠压故障
    if (g_SystemStatus.warn_fault.bus_undervol && 
        bus_voltage > g_ProtectParams.bus_undervol.L1.threshold) {
        g_SystemStatus.warn_fault.bus_undervol = 0;
    }
    
    // 检查并清除28V过压故障
    if (g_SystemStatus.warn_fault.v28_overvol && 
        v28_voltage < g_ProtectParams.v28_overvol.L1.threshold) {
        g_SystemStatus.warn_fault.v28_overvol = 0;
    }
    
    // 检查并清除28V欠压故障
    if (g_SystemStatus.warn_fault.v28_undervol && 
        v28_voltage > g_ProtectParams.v28_undervol.L1.threshold) {
        g_SystemStatus.warn_fault.v28_undervol = 0;
    }
    
    // 检查并清除电容温度故障
    if (g_SystemStatus.warn_fault.cap_temp && 
        cap_temp < g_ProtectParams.cap_temp.L1.threshold) {
        g_SystemStatus.warn_fault.cap_temp = 0;
    }
    
    // 检查并清除电机温度故障
    if (g_SystemStatus.warn_fault.motor_temp && 
        motor_temp < g_ProtectParams.motor_temp.L1.threshold) {
        g_SystemStatus.warn_fault.motor_temp = 0;
    }
    
    // 检查并清除驱动器温度故障
    if (g_SystemStatus.warn_fault.driver_temp && 
        driver_temp < g_ProtectParams.driver_temp.L1.threshold) {
        g_SystemStatus.warn_fault.driver_temp = 0;
    }
    
    // 检查并清除电流故障
    if (g_SystemStatus.warn_fault.current && 
        motor_current < g_ProtectParams.current.L1.threshold) {
        g_SystemStatus.warn_fault.current = 0;
    }
    
    // 检查并清除超速故障
    if (g_SystemStatus.warn_fault.overspeed && 
        motor_speed < g_ProtectParams.overspeed.L1.threshold) {
        g_SystemStatus.warn_fault.overspeed = 0;
    }
    
    // 处理相线故障（基于IO保护状态）
    // 如果IO保护的电流和相电压保护位全部恢复正常，才清除相线故障
    uint16_t phase_fault_mask = 0x0FFF; // 前12位（所有IO保护位）
    if (g_SystemStatus.warn_fault.phase_fault && 
        (g_IO_ProtectResult.all & phase_fault_mask) == 0) {
        g_SystemStatus.warn_fault.phase_fault = 0;
    }
    
    // 系统告警状态同步清除
    if (!g_SystemStatus.warn_fault.current) {
        g_SystemStatus.warn.current = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.bus_voltage) {
        g_SystemStatus.warn.bus_voltage = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.bus_undervol) {
        g_SystemStatus.warn.bus_undervol = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.cap_temp) {
        g_SystemStatus.warn.cap_temp = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.motor_temp) {
        g_SystemStatus.warn.motor_temp = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.driver_temp) {
        g_SystemStatus.warn.driver_temp = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.v28_overvol) {
        g_SystemStatus.warn.v28_overvol = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.v28_undervol) {
        g_SystemStatus.warn.v28_undervol = WARN_LEVEL_NONE;
    }
    
    if (!g_SystemStatus.warn_fault.overspeed) {
        g_SystemStatus.warn.overspeed = WARN_LEVEL_NONE;
    }
}

/**
 * @brief  告警限制系数更新函数
 * @note   温度保护激活时使用，综合考虑高低频保护等级，最低限制系数0.5
 * @param  final_level: 最终告警等级
 * @param  high_freq_level: 高频保护等级
 * @param  low_freq_level: 低频保护等级（温度）
 * @retval None
 */
static void Update_Warning_And_Limits_Enhanced(WarnLevel_TypeDef final_level, 
                                              WarnLevel_TypeDef high_freq_level, 
                                              WarnLevel_TypeDef low_freq_level)
{
    // 幅值处理
    if (final_level > WARN_LEVEL_3) {
        final_level = WARN_LEVEL_3;
    }
    
    // 非告警状态直接设置为1.0（无限制）
    if (final_level == WARN_LEVEL_NONE) {
        g_SystemStatus.status.iq_limit_coeff = 1.0f;
        g_SystemStatus.status.speed_limit_coeff = 1.0f;
        
        // 清除所有告警计时器
        for (uint8_t i = WARN_LEVEL_1; i <= WARN_LEVEL_3; i++) {
            g_TimeProtectStatus.levels[i-1].active = 0;
        }
        return;
    }
    
    // 更新告警计时器状态
    for (uint8_t i = WARN_LEVEL_1; i <= WARN_LEVEL_3; i++) {
        uint8_t idx = i - 1;
        if (final_level == i) {
            if (!g_TimeProtectStatus.levels[idx].active) {
                g_TimeProtectStatus.levels[idx].start_time = PRO_GET_TIME_MS;  // 毫秒
                g_TimeProtectStatus.levels[idx].active = 1;
            }
        } else {
            g_TimeProtectStatus.levels[idx].active = 0;
        }
    }
    
    // 索引调整并检查有效性
    uint8_t idx = final_level - 1;
    if (idx >= 3) {
        g_SystemStatus.status.iq_limit_coeff = 1.0f;
        g_SystemStatus.status.speed_limit_coeff = 1.0f;
        return;
    }
    
    // 检查告警状态是否激活
    if (!g_TimeProtectStatus.levels[idx].active) {
        // 设置基础限制系数（无时间增强限制）
        g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[0];
        g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[0];
    } else {
        // 计算告警持续时间并确定系数
        uint32_t duration_ms = PRO_GET_TIME_MS - g_TimeProtectStatus.levels[idx].start_time;
        uint8_t i;
        for (i = 0; i < 3; i++) {
            if (duration_ms < g_TimeLimitConfig[idx].time_thresholds[i]) {
                g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[i];
                g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[i];
                break;
            }
        }
        
        // 超过所有时间阈值，使用最严格限制
        if (i >= 3) {
            g_SystemStatus.status.iq_limit_coeff = g_TimeLimitConfig[idx].iq_coeffs[2];
            g_SystemStatus.status.speed_limit_coeff = g_TimeLimitConfig[idx].speed_coeffs[2];
        }
    }
    
    // 确保限制系数在有效范围内（最低0.5，避免急减速）
    if (g_SystemStatus.status.iq_limit_coeff < 0.5f) {
        g_SystemStatus.status.iq_limit_coeff = 0.5f;
    }
    if (g_SystemStatus.status.speed_limit_coeff < 0.5f) {
        g_SystemStatus.status.speed_limit_coeff = 0.5f;
    }
}
/********************    保护状态重置功能  end   ******************************/
