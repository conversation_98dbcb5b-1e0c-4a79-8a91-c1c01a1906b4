/**********************************************************
 * @file     start_self_test.c
 * @brief    系统自检功能实现文件
 * <AUTHOR>
 * @date     2024-01-06
 * @version  V2.0.0
 * 模块内容:
 * ----------------------------------------------------------
 * 本模块实现了航空电机控制系统的全面自检功能，采用统一接口和状态机
 * 架构，在系统初始化和关键状态切换时执行一系列硬件和功能测试，
 * 确保系统安全可靠运行。主要包括电气参数检测、传感器状态验证、
 * 通信接口测试和功率模块安全性检查。自检系统设计为分级结构，
 * 可根据不同运行场景(地面或空中)自动选择适当的检测标准。
 * 
 * 架构特点:
 * ----------------------------------------------------------
 * 1. 统一接口设计：通过SelfTest_Manager()一个函数处理初始化、执行、复位
 * 2. 内部状态机：使用状态机组织自检流程，提高代码可维护性和扩展性
 * 3. ADC数据复用：直接使用gADC_Result中的已处理数据，避免重复采样
 * 4. 多模式支持：区分地面启动和空中启动两种模式，使用不同检测标准
 * 5. 传感器冗余设计：关键参数采用多路传感器，提高检测可靠性
 * 6. 智能故障分级：根据故障性质和严重程度提供分级故障响应
 * 7. 通信接口验证：支持RS422和CAN总线的环回测试与数据完整性验证
 * 8. 碳化硅功率模块测试：包含静态和动态测试，全面评估功率单元状态
 * 
 * 核心接口:
 * ----------------------------------------------------------
 * SelfTest_Manager() - 自检管理接口，支持三种操作：
 *   - SELF_TEST_OP_INIT: 初始化自检系统
 *   - SELF_TEST_OP_EXECUTE: 执行完整自检流程
 *   - SELF_TEST_OP_RESET: 复位自检状态，支持重新自检
 * 
 * 自检流程(状态机):
 * ----------------------------------------------------------
 * IDLE -> INIT -> CLOCK -> POWER -> ADC_VOLTAGE -> ADC_TEMP -> COMM -> SIC -> COMPLETE
 * 1. CLOCK: 系统时钟测试，验证时钟频率和CRC计算正确性
 * 2. POWER: 28V控制电压状态检查
 * 3. ADC_VOLTAGE: 母线电压和相电压检测
 * 4. ADC_TEMP: 电机温度、电容温度和碳化硅模块温度检测
 * 5. COMM: RS422和CAN总线的收发功能验证
 * 6. SIC: SiC功率模块静态和动态特性测试
 * 
 * 优化改进:
 * ----------------------------------------------------------
 * V2.0.0相比V1.0.0的主要改进：
 * 1. 移除了独立的ADC采样逻辑，直接复用ADC_Process_500us的结果
 * 2. 简化了ADC数据就绪检查
 * 3. 删除了调试打印信息，提高运行效率
 * 4. 统一了接口设计，简化了系统集成
 * 5. 增加了自检复位功能，支持运行时重新自检
 * 6. 采用状态机架构，提高代码结构清晰度
 * 
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 自检结果直接影响系统运行状态，严重故障将阻止系统启动
 * 2. 依赖ADC_Process_500us提供稳定的ADC数据，确保ADC系统正常工作
 * 3. 温度传感器和电压传感器的失效可能导致系统误判，需要定期维护校准
 * 4. 碳化硅模块测试时会产生短时间的功率脉冲，确保测试时无负载连接
 * 5. 在空中启动模式下，部分检测项的阈值会被适当放宽，优先保证系统连续工作
 * 6. 若AD2S1210W传感器检测到过高速度，系统将自动切换至空中启动模式
 * 7. 自检系统依赖wk_system提供的延时功能，确保系统时钟已正确初始化
 **********************************************************/

#include "start_self_test.h"
#include "at32a423_wk_config.h"
#include "wk_system.h"
#include <string.h> 
#include "Sensor_Drive.h"
#include "Sic_SelfTest.h"  
#include "AnoPTv8.h"
#include "ENC_Speed.h"

/******************************自检总控函数 START*********************************/
/* 全局变量定义 */
SelfTest_Manager_t gSelfTest_Manager = {
    .mode = SELF_TEST_GROUND,
    .items.all = 0,
    .output.all = 0,
    .sensor_fault.all = 0,
    .power_status.all = 0,
    .Init = SelfTest_Init,
    .Reset = SelfTest_Reset
};

/**
  * @brief  自检系统初始化
  * @param  None
  * @retval None
  */
void SelfTest_Init(void)
{
    // 清空自检状态
    gSelfTest_Manager.items.all = 0;
    gSelfTest_Manager.output.all = 0;
    gSelfTest_Manager.sensor_fault.all = 0;
    gSelfTest_Manager.power_status.all = 0;
    
    // 默认为地面自检模式
    gSelfTest_Manager.mode = SELF_TEST_GROUND;

    // 初始化碳化硅模块自检
    Sic_SelfTest_Init();
}


/**
  * @brief  复位自检状态
  * @param  None
  * @retval None
  */
void SelfTest_Reset(void)
{
    gSelfTest_Manager.items.all = 0;
    gSelfTest_Manager.output.all = 0;
    gSelfTest_Manager.sensor_fault.all = 0;
    gSelfTest_Manager.power_status.all = 0;
    gSelfTest_Manager.state = SELF_TEST_STATE_IDLE;
    gSelfTest_Manager.initialized = 0;
}

/**
  * @brief  等待ADC数据稳定
  * @param  timeout_ms: 超时时间(ms)
  * @retval check_t: CHECK_PASS-数据稳定, CHECK_FAIL-超时或数据无效
  */
static check_t Wait_ADC_Data_Ready(uint32_t timeout_ms)
{
        if(ADC_Get_Enable_Flag()) {
            return CHECK_PASS;
        }
    
    return CHECK_FAIL;
}

/**
  * @brief  从ADC结果结构体获取自检数据
  * @param  test_value: 输出的测试数据结构体指针
  * @retval check_t: CHECK_PASS-获取成功, CHECK_FAIL-数据无效
  */
static check_t Get_ADC_TestData_From_Results(ADC_TestValue_t* test_value)
{
    if(test_value == NULL) {
        return CHECK_FAIL;
    }
    
    // 直接从ADC结果结构体复制数据
    test_value->bus_voltage = gADC_Result.bus_voltage;
    test_value->uv_voltage = gADC_Result.uv_voltage;
    test_value->uw_voltage = gADC_Result.uw_voltage;
    test_value->v28_voltage = gADC_Result.v28_voltage;
    test_value->temp_mot1 = gADC_Result.temp_mot1;
    test_value->temp_mot2 = gADC_Result.temp_mot2;
    test_value->temp_cap1 = gADC_Result.temp_cap1;
    test_value->temp_cap2 = gADC_Result.temp_cap2;
    test_value->temp_bmf_u = gADC_Result.temp_bmf_u;
    test_value->temp_bmf_v = gADC_Result.temp_bmf_v;
    test_value->temp_bmf_w = gADC_Result.temp_bmf_w;
    test_value->temp_PT100_6 = gADC_Result.temp_PT100_6;
    test_value->temp_PT1000_7 = gADC_Result.temp_PT1000_7;
    test_value->u_current = gADC_Result.current_u;
    test_value->v_current = gADC_Result.current_v;
    test_value->w_current = gADC_Result.current_w;
    
    return CHECK_PASS;
}

/**
  * @brief  内部状态机执行函数
  * @param  p: 指向自检管理器结构体的指针
  * @param  test_data: ADC测试数据
  * @retval SelfTestResult: 自检结果
  */
static SelfTestResult SelfTest_ExecuteStateMachine(SelfTest_Manager_t* p, const ADC_TestValue_t* test_data)
{
    SelfTestResult result = SELF_TEST_PASS;
    
    while(p->state != SELF_TEST_STATE_COMPLETE && p->state != SELF_TEST_STATE_ERROR) {
        switch(p->state) {
            case SELF_TEST_STATE_IDLE:
                p->state = SELF_TEST_STATE_INIT;
                break;
                
            case SELF_TEST_STATE_INIT:
                // 清除所有状态标志
                p->items.all = 0;
                p->output.all = 0;
                p->sensor_fault.all = 0;
                p->state = SELF_TEST_STATE_CLOCK;
                break;
                
            case SELF_TEST_STATE_CLOCK:
                // 执行时钟自检
                if(ClockSelfTest() != SELF_TEST_PASS) {
                    p->items.bits.clock = 1;
                    result = SELF_TEST_FAIL;
                }
                p->state = SELF_TEST_STATE_POWER;
                break;
                
            case SELF_TEST_STATE_POWER:
                // 执行28V控制电压自检
                if(V28VoltageSelfTest(test_data->v28_voltage) != SELF_TEST_PASS) {
                    p->items.bits.v28 = 1;
                    result = SELF_TEST_FAIL;
                }
                p->state = SELF_TEST_STATE_ADC_VOLTAGE;
                break;
                
            case SELF_TEST_STATE_ADC_VOLTAGE:
                // 执行母线电压自检
                if(BusVoltageSelfTest(test_data->bus_voltage) != SELF_TEST_PASS) {
                    p->items.bits.bus_voltage = 1;
                    result = SELF_TEST_FAIL;
                }
                
                // 执行相电压自检
                if(LineVoltageSelfTest(test_data->uv_voltage, test_data->uw_voltage) != SELF_TEST_PASS) {
                    p->items.bits.bus_voltage = 1;  // 置位电压自检失败标志
                    result = SELF_TEST_FAIL;
                }
                p->state = SELF_TEST_STATE_ADC_TEMP;
                break;
                
            case SELF_TEST_STATE_ADC_TEMP:
                // 执行电机温度自检
                if(MotorTempSelfTest(test_data->temp_mot1, test_data->temp_mot2) != SELF_TEST_PASS) {
                    p->items.bits.motor_temp = 1;
                    result = SELF_TEST_FAIL;
                }
                
                // 执行电容温度自检
                if(CapTempSelfTest(test_data->temp_cap1, test_data->temp_cap2) != SELF_TEST_PASS) {
                    p->items.bits.cap_temp = 1;
                    result = SELF_TEST_FAIL;
                }
                
                // 执行碳化硅温度自检
                if(SicTempSelfTest(test_data->temp_bmf_u, 
                                   test_data->temp_bmf_v, 
                                   test_data->temp_bmf_w) != SELF_TEST_PASS) {
                    p->items.bits.sic_temp = 1;
                    result = SELF_TEST_FAIL;
                }
                p->state = SELF_TEST_STATE_COMM;
                break;
                
            case SELF_TEST_STATE_COMM:
                // 执行RS422通信自检
                if(RS422SelfTest() != SELF_TEST_PASS) {
                    p->items.bits.rs422 = 1;
                    result = SELF_TEST_FAIL;
                }
                
                // 注释掉的CAN自检可以在这里添加
                // if(CANSelfTest() != SELF_TEST_PASS) {
                //     p->items.bits.can = 1;
                //     result = SELF_TEST_FAIL;
                // }
                
                p->state = SELF_TEST_STATE_SIC;
                break;
                
            case SELF_TEST_STATE_SIC:
                // 注释掉的SiC自检可以在这里添加
                // if(Sic_StaticTest() != SELF_TEST_PASS) {
                //     p->items.bits.sic = 1;
                //     result = SELF_TEST_FAIL;
                // }
                
                p->state = SELF_TEST_STATE_COMPLETE;
                break;
                
            default:
                p->state = SELF_TEST_STATE_ERROR;
                result = SELF_TEST_FAIL;
                break;
        }
    }
    
    // 设置最终结果
    if(result == SELF_TEST_PASS && p->state == SELF_TEST_STATE_COMPLETE) {
        p->output.bits.pass = 1;
    } else {
        p->output.bits.fault = 1;
    }
    
    return result;
}

/**
  * @brief  自检管理接口
  * @param  operation: 操作类型
  * @param  p: 指向自检管理器结构体的指针
  * @param  v: 指向ADC测试值的指针(仅在EXECUTE操作时使用)
  * @retval SelfTestResult: 操作结果
  */
SelfTestResult SelfTest_Manager(SelfTestOperation_t operation, 
                               SelfTest_Manager_t* p, 
                               const ADC_TestValue_t* v)
{
    if(p == NULL) {
        return SELF_TEST_FAIL;
    }
    
    switch(operation) {
        case SELF_TEST_OP_INIT:
            // 初始化自检系统
            p->items.all = 0;
            p->output.all = 0;
            p->sensor_fault.all = 0;
            p->power_status.all = 0;
            p->state = SELF_TEST_STATE_IDLE;
            p->mode = SELF_TEST_GROUND;  // 默认地面自检模式
            p->initialized = 1;
            
            // 初始化碳化硅模块自检
            Sic_SelfTest_Init();
            
            return SELF_TEST_PASS;
            
        case SELF_TEST_OP_EXECUTE:
            // 检查是否已初始化
            if(!p->initialized) {
                return SELF_TEST_FAIL;
            }
            
            // 等待ADC数据稳定
            if(Wait_ADC_Data_Ready(1000) != CHECK_PASS) {  // 1秒超时
                return SELF_TEST_FAIL;
            }
            
            // 获取ADC测试数据
            ADC_TestValue_t test_data;
            if(v != NULL) {
                // 使用传入的数据
                test_data = *v;
            } else {
                // 从ADC结果获取数据
                if(Get_ADC_TestData_From_Results(&test_data) != CHECK_PASS) {
                    return SELF_TEST_FAIL;
                }
            }
            
            // 执行状态机
            p->state = SELF_TEST_STATE_IDLE;
            return SelfTest_ExecuteStateMachine(p, &test_data);
            
        case SELF_TEST_OP_RESET:
            // 复位自检状态
            p->items.all = 0;
            p->output.all = 0;
            p->sensor_fault.all = 0;
            p->power_status.all = 0;
            p->state = SELF_TEST_STATE_IDLE;
            // 保持initialized标志，不需要重新初始化
            
            return SELF_TEST_PASS;
            
        default:
            return SELF_TEST_FAIL;
    }
}

/******************************自检总控函数 END*********************************/


/*****************************系统时钟自检 START*********************************/
/**
 * @brief  系统时钟自检函数
 * @note   通过以下方法检查系统时钟是否正常:
 *         1. 获取实际系统时钟频率并与预期值比较
 *         2. 开启时钟失效检测功能
 *         3. 使用CRC计算固定测试数据并验证结果一致性
 * @param  None
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 时钟频率在允许范围内且CRC验证通过
 *         SELF_TEST_FAIL - 时钟频率超出允许范围或CRC验证失败
 */
SelfTestResult ClockSelfTest(void)
{
    crm_clocks_freq_type clocks;
    uint32_t actual_clock;
    int32_t clock_diff;
    
    // 获取实际系统时钟频率
    crm_clocks_freq_get(&clocks);
    actual_clock = clocks.sclk_freq;
    
    // 开启时钟失效检测
    crm_clock_failure_detection_enable(TRUE);
    
    // 计算实际频率与预期频率的差值
    clock_diff = actual_clock/3 - SYSTEM_CLOCK_EXPECTED;
    // 取绝对值
    if(clock_diff < 0) {
        clock_diff = -clock_diff;
    }
    
    // 检查时钟频率是否在允许范围内
    if(clock_diff <= CLOCK_ERROR_THRESHOLD) {
        // 使用CRC计算一个测试值，验证时钟工作正常
        crc_data_reset();
        crc_init_data_set(0xFFFFFFFF);
        
        // 计算一个测试数据的CRC
        uint32_t test_data[4] = {0x12345678, 0x87654321, 0xABCDEF01, 0x10FEDCBA};
        uint32_t crc_result = crc_block_calculate(test_data, 4);
        
        // 再次计算相同数据的CRC，结果应该一致
        crc_data_reset();
        crc_init_data_set(0xFFFFFFFF);
        uint32_t crc_verify = crc_block_calculate(test_data, 4);
        
        // 验证CRC计算结果一致，确认时钟工作正常
        if(crc_result == crc_verify) {
            return SELF_TEST_PASS;
        } else {
            return SELF_TEST_FAIL;
        }
    } else {
        return SELF_TEST_FAIL;
    }
}
/*****************************系统时钟自检 END*********************************/

/************************** AD2S1210自检相关定义 START*********************************/

/* 全局变量定义 */
AD2S1210W_SelfTest_TypeDef gAD2S1210W_Sample = AD2S1210W_SELFTEST_INIT;

/**
 * @brief  AD2S1210W故障状态读取函数
 * @note   进入配置模式读取故障寄存器，然后恢复角度读取模式
 * @param  fault_status: 指向存储故障状态的变量
 * @retval uint8_t: 1-读取成功, 0-读取失败
 */
uint8_t Ad2s_ReadFault(AD2S1210W_Fault_t *fault_status)
{
    if(fault_status == NULL) {
        return 0;
    }
    
    uint8_t fault_reg = 0;
    
    // 进入配置模式
    AD2S2S1210_ModeCfg(Mode_COFIG);
    // 读取故障寄存器 (地址0xFF)
    fault_reg = AD2S1210_READ(FAULT);
    
    // 恢复角度读取模式
    AD2S2S1210_ModeCfg(Mode_POSIT);
        
    // 将读取的数据赋值给故障状态结构体
    fault_status->all = fault_reg;
    
    return 1;
}

/**
 * @brief  AD2S1210W速度获取函数 - 简化版本
 * @note   故障检查通过后，直接使用ENC_Speed模块的速度函数
 * @param  speed: 指向存储速度值的变量 (单位：rpm)
 * @retval uint8_t: 1-读取成功, 0-读取失败
 */
uint16_t Ad2s_ReadSpeed(uint16_t *speed)
{
    if(speed == NULL) {
        return 0;
    }
    
    // 使用ENC_Speed模块的速度函数，获取带符号的转速
    float signed_speed_rpm = GetSignedSpeed_RPM();
    
    // 转换为绝对值并返回整数类型
    *speed = (uint16_t)fabsf(signed_speed_rpm);
    
    return 1;
}

/**
 * @brief  AD2S1210W自检函数
 * @param  resolver_data: 指向存储速度传感器数据的结构体
 * @retval SelfTestResult: 自检结果
 */
SelfTestResult AD2S1210W_SelfTest(AD2S1210W_Sample_TypeDef* resolver_data)
{
    SelfTestResult result = SELF_TEST_PASS;
    AD2S1210W_Fault_t fault_status = {0};
    uint16_t current_speed_raw = 0;
    
    // 清零数据缓存
    memset(resolver_data, 0, sizeof(AD2S1210W_Sample_TypeDef));
    
    // 1.检查AD2S1210的故障状态
    if(!Ad2s_ReadFault(&fault_status)) {
        resolver_data->sensor_status = 1;  // 标记传感器通信故障
        return SELF_TEST_FAIL;
    }
    
    // 检查是否有严重故障
    if(fault_status.bits.signal_clip ||        // 信号削波
       fault_status.bits.los_threshold ||      // 信号低于LOS阈值
       fault_status.bits.dos_overrange ||      // 信号超过DOS超量程
       fault_status.bits.parity_error) {       // 配置奇偶校验错误
        resolver_data->sensor_status = 2;       // 标记传感器硬件故障
        return SELF_TEST_FAIL;
    }
    
    // 如果故障状态检查通过，说明AD2S1210硬件正常，可以使用ENC_Speed模块的速度函数
    
    // 直接读取ENC_Speed模块计算的速度（已经过滤波处理）
    if(!Ad2s_ReadSpeed(&current_speed_raw)) { 
        resolver_data->sensor_status = 3;  // 标记速度读取故障
        return SELF_TEST_FAIL;
    }
    
    // 保存速度数据
    resolver_data->max_speed = current_speed_raw;
    resolver_data->filtered_speed = (float)current_speed_raw; 
    
    // 根据速度判断是否为空中启动
    if(resolver_data->filtered_speed > AIR_SPEED_THRESHOLD) {
        // 速度超过阈值，判定为空中启动
        gSelfTest_Manager.mode = SELF_TEST_AIR;
    } else {
        // 速度在地面启动阈值范围内
        gSelfTest_Manager.mode = SELF_TEST_GROUND;
    }
    
    // 检查是否有警告级别的故障（根据自检模式决定处理方式）
    if(fault_status.bits.tracking_error ||     // 跟踪误差
       fault_status.bits.tracking_rate ||      // 速度超过最大跟踪速率
       fault_status.bits.phase_error ||        // 相位误差
       fault_status.bits.dos_mismatch) {       // DOS失配
        
        // 根据当前自检模式决定处理方式
        if(gSelfTest_Manager.mode == SELF_TEST_GROUND) {
            // 地面自检模式：有警告级别故障时返回失败
            resolver_data->sensor_status = 4;  // 标记警告级别故障
            result = SELF_TEST_FAIL;
        } else {
            // 空中自检模式：有警告级别故障时仍返回成功，但可以设置警告标志
            result = SELF_TEST_PASS;
            // 这里可以设置警告标志，供上层决策使用
        }
    }
    
    return result;
}

/************************** AD2S1210自检相关定义 END*********************************/

/************************** 母线电压自检相关定义 START*********************************/
/**
 * @brief  母线电压自检函数
 * @param  bus_voltage: 母线电压值
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 电压在正常范围内
 *         SELF_TEST_FAIL - 电压超出允许范围
 */
SelfTestResult BusVoltageSelfTest(float bus_voltage)
{
    // 1. 检查传感器是否失效
    if(bus_voltage >= BUS_VOLTAGE_SENSOR_FAULT) {
        gSelfTest_Manager.sensor_fault.bits.bus_voltage = 1;  // 置位传感器失效标志
        gSelfTest_Manager.items.bits.bus_voltage = 1;         // 置位母线电压自检失败
        return SELF_TEST_FAIL;
    }
    
    // 2. 根据启动模式判断
    if(gSelfTest_Manager.mode == SELF_TEST_AIR) {
        // 空中启动模式
        if(bus_voltage > BUS_VOLTAGE_MIN_NORMAL && bus_voltage < BUS_VOLTAGE_MAX_WARNING) {
            gSelfTest_Manager.power_status.bits.bus_power_ok = 1;  // 母线电压供电
            return SELF_TEST_PASS;
        }
    } else {
        // 地面启动模式
        if(bus_voltage < BUS_VOLTAGE_POWEROFF) {
            // 电压<10V，等待高压上电
            gSelfTest_Manager.power_status.bits.wait_hv_power = 1;
            return SELF_TEST_PASS;
        }
        else if(bus_voltage < BUS_VOLTAGE_MIN_WARNING) {
            // 10V < V < 50V，母线自检欠压故障
            gSelfTest_Manager.items.bits.bus_voltage = 1;
            return SELF_TEST_FAIL;
        }
        else if(bus_voltage < BUS_VOLTAGE_MIN_NORMAL) {
            // 50V < V < 380V，轻度故障告警运行
            gSelfTest_Manager.output.bits.warning = 1;
            gSelfTest_Manager.power_status.bits.bus_power_ok = 1;  // 母线电压供电
            return SELF_TEST_PASS;
        }
        else if(bus_voltage <= BUS_VOLTAGE_MAX_NORMAL) {
            // 380V < V < 780V，正常运行
            gSelfTest_Manager.power_status.bits.bus_power_ok = 1;
            gSelfTest_Manager.power_status.bits.bus_power_ok = 1;  // 母线电压供电
            return SELF_TEST_PASS;
        }
        else if(bus_voltage <= BUS_VOLTAGE_MAX_WARNING) {
            // 780V < V < 850V，轻度故障告警运行
            gSelfTest_Manager.output.bits.warning = 1;
            gSelfTest_Manager.power_status.bits.bus_power_ok = 1;  // 母线电压供电
            gSelfTest_Manager.power_status.bits.soft_stop = 1;
            return SELF_TEST_PASS;
        }
        else {
            // V > 850V，母线自检过压故障
            gSelfTest_Manager.power_status.bits.bus_power_ok = 0;
            gSelfTest_Manager.items.bits.bus_voltage = 1;
            return SELF_TEST_FAIL;
        }
    }
    
    return SELF_TEST_PASS;
}

/************************** 母线电压自检相关定义 END*********************************/

/************************** 相电压自检相关定义 START*********************************/

/**
 * @brief  相电压自检函数
 * @param  uv_voltage: UV相电压值
 * @param  uw_voltage: UW相电压值
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 相电压在正常范围内
 *         SELF_TEST_FAIL - 相电压超出允许范围
 * @note   检查相电压偏置是否在允许范围内(±5.6235V)，并更新偏置校准值
 */
SelfTestResult LineVoltageSelfTest(float uv_voltage, float uw_voltage)
{
    // 空中重启，有反电动势，直接返回通过
    if(gSelfTest_Manager.mode == SELF_TEST_AIR) {
        return SELF_TEST_PASS;
    }
    
    // 检查UV相电压偏置
    if(uv_voltage > LINE_VOLTAGE_THRESHOLD || uv_voltage < -LINE_VOLTAGE_THRESHOLD) {
        // UV相电压传感器失效
        gSelfTest_Manager.sensor_fault.bits.uv_voltage = 1;

    }
    
    // 检查UW相电压偏置
    if(uw_voltage > LINE_VOLTAGE_THRESHOLD || uw_voltage < -LINE_VOLTAGE_THRESHOLD) {
        // UW相电压传感器失效
        gSelfTest_Manager.sensor_fault.bits.uw_voltage = 1;
    }

    // 检查是否故障
    if(gSelfTest_Manager.sensor_fault.bits.uv_voltage | gSelfTest_Manager.sensor_fault.bits.uw_voltage) {
        return SELF_TEST_FAIL;
    }

    // 更新ADC初始偏置校准值
    gADC_Manager.uv_offset = uv_voltage;  // 保存UV相初始偏置校准值
    gADC_Manager.uw_offset = uw_voltage;  // 保存UW相初始偏置校准值

    return SELF_TEST_PASS;
}

/************************** 相电压自检相关定义 END*********************************/

/************************** 温度自检相关定义 START*********************************/

/**
 * @brief  电机温度自检函数
 * @param  temp_mot1: 电机温度1
 * @param  temp_mot2: 电机温度2
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 温度在正常范围内
 *         SELF_TEST_FAIL - 温度超出允许范围
 */
SelfTestResult MotorTempSelfTest(float temp_mot1, float temp_mot2)
{
    // 检查温度传感器1是否失效
    if(temp_mot1 <= TEMP_SENSOR_MIN || temp_mot1 >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.motor_temp1 = 1;
    }
    
    // 检查温度传感器2是否失效
    if(temp_mot2 <= TEMP_SENSOR_MIN || temp_mot2 >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.motor_temp2 = 1;
    }
    
    // 检查工作温度范围
    if(temp_mot1 > MOTOR_TEMP_MAX || temp_mot2 > MOTOR_TEMP_MAX) {
        // 超过最高工作温度
        gSelfTest_Manager.items.bits.motor_temp = 1;
    }
    else if(temp_mot1 > MOTOR_TEMP_WARNING || temp_mot2 > MOTOR_TEMP_WARNING) {
        // 超过告警温度
        gSelfTest_Manager.output.bits.warning = 1;
    }

    if (gSelfTest_Manager.sensor_fault.bits.motor_temp1 | gSelfTest_Manager.sensor_fault.bits.motor_temp2 | gSelfTest_Manager.items.bits.motor_temp)
    {
        return SELF_TEST_FAIL;
    }
    
    return SELF_TEST_PASS;
}

/**
 * @brief  电容温度自检函数
 * @param  temp_cap1: 电容温度1
 * @param  temp_cap2: 电容温度2
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 温度在正常范围内
 *         SELF_TEST_FAIL - 温度超出允许范围
 */
SelfTestResult CapTempSelfTest(float temp_cap1, float temp_cap2)
{
    // 检查温度传感器1是否失效
    if(temp_cap1 <= TEMP_SENSOR_MIN || temp_cap1 >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.cap_temp1 = 1;
    }
    
    // 检查温度传感器2是否失效
    if(temp_cap2 <= TEMP_SENSOR_MIN || temp_cap2 >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.cap_temp2 = 1;
    }
    
    // 检查工作温度范围
    if(temp_cap1 > CAP_TEMP_MAX || temp_cap2 > CAP_TEMP_MAX) {
        // 超过最高工作温度
        gSelfTest_Manager.items.bits.cap_temp = 1;
    }
    else if(temp_cap1 > CAP_TEMP_WARNING || temp_cap2 > CAP_TEMP_WARNING) {
        // 超过告警温度
        gSelfTest_Manager.output.bits.warning = 1;
    }
    if (gSelfTest_Manager.sensor_fault.bits.cap_temp1 | gSelfTest_Manager.sensor_fault.bits.cap_temp2 | gSelfTest_Manager.items.bits.cap_temp)
    {
        return SELF_TEST_FAIL;
    }
    return SELF_TEST_PASS;
}

/**
 * @brief  碳化硅温度自检函数
 * @param  temp_u: U相温度
 * @param  temp_v: V相温度
 * @param  temp_w: W相温度
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 温度在正常范围内
 *         SELF_TEST_FAIL - 温度超出允许范围
 */
SelfTestResult SicTempSelfTest(float temp_u, float temp_v, float temp_w)
{
    // 检查U相温度传感器是否失效
    if(temp_u <= TEMP_SENSOR_MIN || temp_u >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.sic_temp_u = 1;
    }
    
    // 检查V相温度传感器是否失效
    if(temp_v <= TEMP_SENSOR_MIN || temp_v >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.sic_temp_v = 1;
    }
    
    // 检查W相温度传感器是否失效
    if(temp_w <= TEMP_SENSOR_MIN || temp_w >= TEMP_SENSOR_MAX) {
        gSelfTest_Manager.sensor_fault.bits.sic_temp_w = 1;
    }
    
    // 检查工作温度范围
    if(temp_u > SIC_TEMP_MAX || temp_v > SIC_TEMP_MAX || temp_w > SIC_TEMP_MAX) {
        // 超过最高工作温度
        gSelfTest_Manager.items.bits.sic_temp = 1;
    }
    else if(temp_u > SIC_TEMP_WARNING || temp_v > SIC_TEMP_WARNING || temp_w > SIC_TEMP_WARNING) {
        // 超过告警温度
        gSelfTest_Manager.output.bits.warning = 1;
    }
    if (gSelfTest_Manager.sensor_fault.bits.sic_temp_u | gSelfTest_Manager.sensor_fault.bits.sic_temp_v | gSelfTest_Manager.sensor_fault.bits.sic_temp_w | gSelfTest_Manager.items.bits.sic_temp)
    {
        return SELF_TEST_FAIL;
    }
    return SELF_TEST_PASS;
}

/************************** 温度自检相关定义 END*********************************/

/************************** 28V控制电压自检相关定义 START*********************************/

/**
 * @brief  28V控制电压自检函数
 * @param  v28_voltage: 28V控制电压值
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 电压在正常范围内
 *         SELF_TEST_FAIL - 电压超出允许范围
 */
SelfTestResult V28VoltageSelfTest(float v28_voltage)
{
    // 1. 检查传感器是否失效 (>32V)
    if(v28_voltage > V28_VOLTAGE_MAX) {
        gSelfTest_Manager.sensor_fault.bits.v28_voltage = 1;
        gSelfTest_Manager.items.bits.v28 = 1;
        return SELF_TEST_FAIL;
    }
    
    // 2. 检查电压范围
    if(v28_voltage < V28_VOLTAGE_MIN) {
        // <19V: 严重欠压，自检失败
        gSelfTest_Manager.items.bits.v28 = 1;
        return SELF_TEST_FAIL;
    }
    else if(v28_voltage > V28_VOLTAGE_MAX) {
        // >32V: 过压，自检失败
        gSelfTest_Manager.items.bits.v28 = 1;
        return SELF_TEST_FAIL;
    }
    else {
        // 19-32V: 正常工作范围
        gSelfTest_Manager.power_status.bits.v28_power_ok = 1;
    }
    
    return SELF_TEST_PASS;
}

/************************** 28V控制电压自检相关定义 END*********************************/

/************************** RS422通信自检相关定义 START*********************************/

/* 全局变量定义 */
RS422_SelfTest_TypeDef gRS422_SelfTest = RS422_SELFTEST_INIT;

/**
 * @brief  RS422发送握手数据函数
 * @param  handshake_code: 握手码
 * @retval None
 */
void RS422_SendHandshake(uint32_t handshake_code)
{

}

/**
 * @brief  RS422检查握手状态函数
 * @param  None
 * @retval uint8_t: 
 *         0 - 握手未成功
 *         1 - 握手成功
 */
uint8_t RS422_CheckHandshake(void)
{

    return 1;
}

/**
 * @brief  RS422通信自检函数
 * @param  None
 * @retval SelfTestResult: 
 *         SELF_TEST_PASS - 通信正常
 *         SELF_TEST_FAIL - 通信异常
 *         SELF_TEST_TIMEOUT - 通信超时
 */
SelfTestResult RS422SelfTest(void)
{
    uint16_t timeout;
    
    // 重置自检状态
    gRS422_SelfTest.handshake_ok = 0;
    
    // 尝试多次握手
    for(uint8_t i = 0; i < RS422_RETRY_COUNT; i++) {
        
        // 1. 发送握手码
        RS422_SendHandshake(RS422_HANDSHAKE_CODE);
        
        // 2. 等待并检查握手结果
        timeout = 0;
        while(!RS422_CheckHandshake()) {
            timeout++;
            if(timeout > RS422_TIMEOUT_MS) {
                break;  // 超时则跳出当前等待循环
            }
            wk_delay_ms(1);
        }
        
        // 3. 如果握手成功，返回通过
        if(RS422_CheckHandshake()) {
            gRS422_SelfTest.handshake_ok = 1;
            return SELF_TEST_PASS;
        }
        
        // 4. 未成功则延时后重试
        wk_delay_ms(RS422_RETRY_DELAY_MS);
    }
    
    // 多次重试失败，置位通信故障标志
    gSelfTest_Manager.items.bits.rs422 = 1;
    return SELF_TEST_FAIL;
}

/************************** RS422通信自检相关定义 END*********************************/

/************************** CAN通信自检相关定义 START*********************************/

/* CAN自检全局变量 */
CAN_SelfTest_TypeDef gCAN_SelfTest;

/**
 * @brief  配置CAN用于自检的过滤器
 * @param  None
 * @note   同时为CAN1和CAN2配置过滤器13，接收测试ID
 */
static void CAN_ConfigTestFilter(void)
{
    can_filter_init_type can_filter_init_struct;
    
    // 配置过滤器13，接收测试ID1和ID2
    can_filter_init_struct.filter_activate_enable = TRUE;
    can_filter_init_struct.filter_number = 13;
    can_filter_init_struct.filter_fifo = CAN_FILTER_FIFO1;
    can_filter_init_struct.filter_bit = CAN_FILTER_32BIT;
    can_filter_init_struct.filter_mode = CAN_FILTER_MODE_ID_LIST;
    
    // 配置为接收ID1 (扩展ID格式)
    can_filter_init_struct.filter_id_high = (((CAN_SELFTEST_ID1 << 3) >> 16) & 0xFFFF);
    can_filter_init_struct.filter_id_low = ((CAN_SELFTEST_ID1 << 3) & 0xFFFF) | 0x04;  // 0x04表示扩展帧
    
    // 配置为接收ID2 (扩展ID格式)
    can_filter_init_struct.filter_mask_high = (((CAN_SELFTEST_ID2 << 3) >> 16) & 0xFFFF);
    can_filter_init_struct.filter_mask_low = ((CAN_SELFTEST_ID2 << 3) & 0xFFFF) | 0x04;  // 0x04表示扩展帧
    
    // 为CAN1配置过滤器13
    can_filter_init(CAN1, &can_filter_init_struct);
    
    // 为CAN2配置同样的过滤器13
    can_filter_init(CAN2, &can_filter_init_struct);
    
}

/**
 * @brief  反初始化CAN自检用的过滤器
 * @param  None
 * @note   同时禁用CAN1和CAN2的过滤器13
 */
static void CAN_DeInitTestFilter(void)
{
    can_filter_init_type can_filter_init_struct;
    
    // 禁用过滤器13
    can_filter_init_struct.filter_activate_enable = FALSE;
    can_filter_init_struct.filter_number = 13;
    
    // 同时禁用CAN1和CAN2的过滤器13
    can_filter_init(CAN1, &can_filter_init_struct);
    can_filter_init(CAN2, &can_filter_init_struct);
    
}

/**
 * @brief  发送CAN自检测试帧
 * @param  can_x: CAN外设指针 (CAN1或CAN2)
 * @param  tx_id: 发送帧ID
 * @param  tx_data: 发送数据指针
 * @retval uint8_t: 1=发送成功, 0=发送失败
 */
static uint8_t CAN_SendTestFrame(can_type* can_x, uint32_t tx_id, const uint8_t* tx_data)
{
    can_tx_message_type tx_message;
    uint8_t mailbox;
    uint16_t timeout = 0;
    
    // 配置发送消息 - 使用扩展ID
    tx_message.standard_id = 0;
    tx_message.extended_id = tx_id;
    tx_message.id_type = CAN_ID_EXTENDED;  // 使用扩展ID
    tx_message.frame_type = CAN_TFT_DATA;
    tx_message.dlc = CAN_SELFTEST_DATA_LEN;
    
    // 复制数据
    for(uint8_t i = 0; i < CAN_SELFTEST_DATA_LEN; i++) {
        tx_message.data[i] = tx_data[i];
    }
    
    // 等待发送完成
    while(can_transmit_status_get(can_x, mailbox) != CAN_TX_STATUS_SUCCESSFUL) {
        timeout++;
        if(timeout > CAN_SELFTEST_TIMEOUT_MS) {
            return 0;  // 发送超时
        }
        wk_delay_ms(1);
    }
    
    return 1;  // 发送成功
}

/**
 * @brief  接收CAN自检测试帧
 * @param  can_x: CAN外设指针 (CAN1或CAN2)
 * @param  rx_data: 接收数据存储指针
 * @param  expected_id: 期望接收的ID
 * @retval uint8_t: 1=接收成功, 0=接收失败
 */
static uint8_t CAN_ReceiveTestFrame(can_type* can_x, uint8_t* rx_data, uint32_t expected_id)
{
    can_rx_message_type rx_message;
    uint16_t timeout = 0;
    
    // 等待接收到消息
    while(can_receive_message_pending_get(can_x, CAN_RX_FIFO1) == 0) {
        timeout++;
        if(timeout > CAN_SELFTEST_TIMEOUT_MS) {
            return 0;  // 接收超时
        }
        wk_delay_ms(1);
    }
    
    // 接收消息
    can_message_receive(can_x, CAN_RX_FIFO1, &rx_message);
    
    // 验证是否为扩展ID以及ID是否匹配
    if(rx_message.id_type != CAN_ID_EXTENDED || rx_message.extended_id != expected_id) {
        return 0;  // ID类型或值不匹配
    }
    
    // 复制接收到的数据
    for(uint8_t i = 0; i < CAN_SELFTEST_DATA_LEN; i++) {
        rx_data[i] = rx_message.data[i];
    }
    
    return 1;  // 接收成功
}

/**
 * @brief  比较两个数据缓冲区
 * @param  buf1: 第一个缓冲区
 * @param  buf2: 第二个缓冲区
 * @param  len: 比较长度
 * @retval uint8_t: 1=相同, 0=不同
 */
static uint8_t CAN_CompareBuffers(const uint8_t* buf1, const uint8_t* buf2, uint8_t len)
{
    for(uint8_t i = 0; i < len; i++) {
        if(buf1[i] != buf2[i]) {
            return 0;  // 不相同
        }
    }
    return 1;  // 相同
}

/**
 * @brief  CAN通信自检函数 - 简化版
 * @param  None
 * @retval SelfTestResult: SELF_TEST_PASS - 通信正常, SELF_TEST_FAIL - 通信异常
 */
SelfTestResult CANSelfTest(void)
{
    can_tx_message_type tx_message;
    can_rx_message_type rx_message;
    uint8_t test_data = 0xA5;  // 测试数据，只用一个字节
    uint8_t received_data = 0;
    uint16_t timeout = 0;
    
    // 清空CAN1和CAN2的FIFO1
    while(can_receive_message_pending_get(CAN1, CAN_RX_FIFO1) > 0) {
        can_message_receive(CAN1, CAN_RX_FIFO1, &rx_message);
    }
    while(can_receive_message_pending_get(CAN2, CAN_RX_FIFO1) > 0) {
        can_message_receive(CAN2, CAN_RX_FIFO1, &rx_message);
    }
    
    // 测试1: CAN1发送到CAN2
    // 准备发送消息
    tx_message.standard_id = 0;
    tx_message.extended_id = CAN_SELFTEST_ID1;
    tx_message.id_type = CAN_ID_EXTENDED;
    tx_message.frame_type = CAN_TFT_DATA;
    tx_message.dlc = 1;  // 只发送1个字节
    tx_message.data[0] = test_data;
    
    // 发送消息
    if(can_message_transmit(CAN1, &tx_message) == CAN_TX_STATUS_NO_EMPTY) {
        return SELF_TEST_FAIL;  // 发送失败
    }
    
    // 等待CAN2接收
    timeout = 0;
    while(can_receive_message_pending_get(CAN2, CAN_RX_FIFO1) == 0) {
        timeout++;
        if(timeout > CAN_SELFTEST_TIMEOUT_MS) {
            return SELF_TEST_FAIL;  // 接收超时
        }
        wk_delay_ms(1);
    }
    
    // 接收消息
    can_message_receive(CAN2, CAN_RX_FIFO1, &rx_message);
    
    // 验证接收的消息
    if(rx_message.id_type != CAN_ID_EXTENDED || 
       rx_message.extended_id != CAN_SELFTEST_ID1 ||
       rx_message.dlc != 1 || 
       rx_message.data[0] != test_data) {
        return SELF_TEST_FAIL;  // 数据不匹配
    }
    
    // 测试2: CAN2发送到CAN1
    // 准备发送消息
    tx_message.extended_id = CAN_SELFTEST_ID2;
    tx_message.data[0] = ~test_data;  // 取反作为第二个测试值
    
    // 发送消息
    if(can_message_transmit(CAN2, &tx_message) == CAN_TX_STATUS_NO_EMPTY) {
        return SELF_TEST_FAIL;  // 发送失败
    }
    
    // 等待CAN1接收
    timeout = 0;
    while(can_receive_message_pending_get(CAN1, CAN_RX_FIFO1) == 0) {
        timeout++;
        if(timeout > CAN_SELFTEST_TIMEOUT_MS) {
            return SELF_TEST_FAIL;  // 接收超时
        }
        wk_delay_ms(1);
    }
    
    // 接收消息
    can_message_receive(CAN1, CAN_RX_FIFO1, &rx_message);
    
    // 验证接收的消息
    if(rx_message.id_type != CAN_ID_EXTENDED || 
       rx_message.extended_id != CAN_SELFTEST_ID2 ||
       rx_message.dlc != 1 || 
       rx_message.data[0] != ~test_data) {
        return SELF_TEST_FAIL;  // 数据不匹配
    }
    
    // 所有测试通过
    return SELF_TEST_PASS;
}






