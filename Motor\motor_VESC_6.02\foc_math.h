/*
	Copyright 2016 - 2022 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

/*
 * FOC数学库头文件
 *
 * 本文件包含了磁场定向控制(Field Oriented Control, FOC)算法的核心数学函数和数据结构定义。
 * FOC是一种高效的三相交流电机控制技术，通过将三相电流转换到旋转坐标系(dq坐标系)中，
 * 实现对电机磁通和转矩的独立控制，从而获得类似直流电机的控制特性。
 *
 * 主要功能模块：
 * 1. 观测器算法 - 用于无传感器控制中的转子位置估计
 * 2. 空间矢量调制(SVM) - 高效的PWM生成算法
 * 3. PID控制器 - 位置和速度闭环控制
 * 4. 坐标变换 - Clarke变换和Park变换
 * 5. 弱磁控制 - 扩展电机高速运行范围
 * 6. 高频注入(HFI) - 低速无传感器控制技术
 */

#ifndef FOC_MATH_H_
#define FOC_MATH_H_

#include "datatypes.h"

// 数据结构定义

/**
 * 电机状态结构体
 * 包含FOC控制中所有关键的电机状态变量
 */
typedef struct {
	// 三相电压 (V)
	float va;					// A相电压
	float vb;					// B相电压
	float vc;					// C相电压

	// 电压幅值和调制信号
	float v_mag_filter;			// 滤波后的电压幅值
	float mod_alpha_filter;		// α轴调制信号(滤波后)
	float mod_beta_filter;		// β轴调制信号(滤波后)
	float mod_alpha_measured;	// α轴调制信号(测量值)
	float mod_beta_measured;	// β轴调制信号(测量值)
	float mod_alpha_raw;		// α轴调制信号(原始值)
	float mod_beta_raw;			// β轴调制信号(原始值)

	// dq轴目标电流 (A)
	float id_target;			// d轴目标电流(磁通电流)
	float iq_target;			// q轴目标电流(转矩电流)

	// 占空比相关
	float max_duty;				// 最大占空比
	float duty_now;				// 当前占空比

	// 转子位置和三角函数值
	float phase;				// 转子电角度 (rad)
	float phase_cos;			// cos(转子电角度)
	float phase_sin;			// sin(转子电角度)

	// αβ坐标系电流 (A) - Clarke变换结果
	float i_alpha;				// α轴电流
	float i_beta;				// β轴电流
	float i_abs;				// 电流幅值 |I| = √(i_α² + i_β²)
	float i_abs_filter;			// 滤波后的电流幅值

	// 总线电流和电压
	float i_bus;				// 总线电流 (A)
	float v_bus;				// 总线电压 (V)

	// αβ坐标系电压 (V) - Clarke变换结果
	float v_alpha;				// α轴电压
	float v_beta;				// β轴电压

	// dq坐标系调制信号 - Park变换结果
	float mod_d;				// d轴调制信号
	float mod_q;				// q轴调制信号
	float mod_q_filter;			// 滤波后的q轴调制信号

	// dq坐标系电流 (A) - Park变换结果
	float id;					// d轴电流(磁通电流)
	float iq;					// q轴电流(转矩电流)
	float id_filter;			// 滤波后的d轴电流
	float iq_filter;			// 滤波后的q轴电流

	// dq坐标系电压 (V) - Park变换结果
	float vd;					// d轴电压
	float vq;					// q轴电压
	float vd_int;				// d轴电压积分项
	float vq_int;				// q轴电压积分项

	// 转速
	float speed_rad_s;			// 机械角速度 (rad/s)

	// 空间矢量调制相关
	uint32_t svm_sector;		// SVM扇区号 (1-6)
	bool is_using_phase_filters;// 是否使用相电流滤波器
} motor_state_t;

/**
 * 电机控制采样结构体
 * 用于存储ADC采样的平均值计算
 */
typedef struct {
	int sample_num;				// 采样计数器
	float avg_current_tot;		// 电流总和(用于平均值计算)
	float avg_voltage_tot;		// 电压总和(用于平均值计算)
} mc_sample_t;

/**
 * 高频注入(HFI)状态结构体
 *
 * 高频注入是一种用于低速无传感器控制的技术，通过向电机注入高频信号
 * 并分析响应来估计转子位置。适用于永磁同步电机的零速和低速控制。
 *
 * 工作原理：
 * 1. 向电机注入高频电压信号(通常几kHz)
 * 2. 由于电机的磁路饱和特性，高频电流响应包含位置信息
 * 3. 通过FFT分析提取位置相关的谐波分量
 * 4. 使用锁相环(PLL)跟踪转子位置
 */
typedef struct {
	// FFT函数指针 - 用于频域分析
	void(*fft_bin0_func)(float*, float*, float*);	// FFT第0个频率分量
	void(*fft_bin1_func)(float*, float*, float*);	// FFT第1个频率分量
	void(*fft_bin2_func)(float*, float*, float*);	// FFT第2个频率分量

	// 采样和缓冲区管理
	int samples;				// 采样点数
	int table_fact;				// 查找表因子
	float buffer[32];			// 电压采样缓冲区
	float buffer_current[32];	// 电流采样缓冲区
	bool ready;					// HFI准备就绪标志
	int ind;					// 缓冲区索引
	bool is_samp_n;				// 采样标志

	// 信号处理变量
	float sign_last_sample;		// 上次采样符号
	float cos_last, sin_last;	// 上次三角函数值
	float prev_sample;			// 上次采样值

	// 位置估计相关
	float angle;				// 估计的转子角度 (rad)
	float double_integrator;	// 双积分器状态
	int est_done_cnt;			// 估计完成计数
	float observer_zero_time;	// 观测器零点时间
	int flip_cnt;				// 翻转计数
} hfi_state_t;

/**
 * 观测器状态结构体
 *
 * 用于无传感器FOC控制中的转子位置和速度观测。
 * 主要实现Ortega观测器和MXLEMMING观测器算法。
 *
 * 观测器原理：
 * 基于电机的数学模型，利用测量的电压和电流信息，
 * 通过状态观测器估计转子的磁链位置，进而得到转子位置。
 *
 * 状态方程：
 * dx/dt = Ax + Bu + L(y - Cx)
 * 其中 x = [x1, x2]^T 为观测器状态变量
 */
typedef struct {
	float x1;					// 观测器状态变量1 (α轴磁链)
	float x2;					// 观测器状态变量2 (β轴磁链)
	float lambda_est;			// 估计的磁链幅值 |λ| = √(x1² + x2²)
	float i_alpha_last;			// 上次α轴电流值
	float i_beta_last;			// 上次β轴电流值
} observer_state;

/**
 * 电机完整状态结构体
 *
 * 这是FOC控制系统的核心数据结构，包含了电机控制所需的所有状态变量、
 * 配置参数、控制器状态和观测器状态等信息。
 */
typedef struct {
	// 基本配置和状态
	mc_configuration *m_conf;		// 电机配置参数指针
	mc_state m_state;				// 电机当前状态(停止/运行/故障等)
	mc_control_mode m_control_mode;	// 控制模式(电流/速度/位置/占空比)
	motor_state_t m_motor_state;	// 电机状态变量集合

	// 电流相关
	float m_curr_unbalance;			// 三相电流不平衡度
	float m_currents_adc[3];		// ADC采样的三相电流原始值

	// 相位控制
	bool m_phase_override;			// 相位覆盖使能标志
	float m_phase_now_override;		// 覆盖的相位值

	// 控制设定值
	float m_duty_cycle_set;			// 设定占空比
	float m_id_set;					// 设定d轴电流 (A)
	float m_iq_set;					// 设定q轴电流 (A)
	float m_i_fw_set;				// 弱磁电流设定值 (A)
	float m_current_off_delay;		// 电流关闭延时

	// 开环控制
	float m_openloop_speed;			// 开环速度设定 (rad/s)
	float m_openloop_phase;			// 开环相位 (rad)
	bool m_output_on;				// 输出使能标志

	// PID控制器设定值
	float m_pos_pid_set;			// 位置PID设定值 (rad)
	float m_speed_pid_set_rpm;		// 速度PID设定值 (RPM)
	float m_speed_command_rpm;		// 速度命令值 (RPM)

	// 观测器相关
	float m_phase_now_observer;		// 观测器估计的当前相位
	float m_phase_now_observer_override;	// 观测器相位覆盖值
	float m_observer_x1_override;	// 观测器x1状态覆盖值
	float m_observer_x2_override;	// 观测器x2状态覆盖值
	bool m_phase_observer_override;	// 观测器相位覆盖使能

	// 编码器相关
	float m_phase_now_encoder;		// 编码器当前相位
	float m_phase_now_encoder_no_index;	// 无索引编码器相位

	// 状态观测器和PLL
	observer_state m_observer_state;// 观测器状态
	float m_pll_phase;				// PLL相位输出
	float m_pll_speed;				// PLL速度输出

	// 采样和计数器
	mc_sample_t m_samples;			// ADC采样数据
	int m_tachometer;				// 转速计数器(带符号)
	int m_tachometer_abs;			// 转速计数器(绝对值)
	float m_pos_pid_now;			// 当前位置PID反馈值
	float m_gamma_now;				// 当前观测器增益

	// 传感器选择
	bool m_using_encoder;			// 是否使用编码器标志

	// 速度估计
	float m_speed_est_fast;			// 快速速度估计值
	float m_speed_est_faster;		// 更快速度估计值

	// PWM占空比(下一周期)
	int m_duty1_next, m_duty2_next, m_duty3_next;	// 下一周期三相PWM占空比
	bool m_duty_next_set;			// 下一周期占空比设置标志

	// 电流采样(下一周期)
	float m_i_alpha_sample_next;	// 下一周期α轴电流采样
	float m_i_beta_sample_next;		// 下一周期β轴电流采样
	float m_i_alpha_sample_with_offset;	// 带偏移的α轴电流采样
	float m_i_beta_sample_with_offset;	// 带偏移的β轴电流采样
	float m_i_alpha_beta_has_offset;// αβ电流偏移标志

	// 高频注入状态
	hfi_state_t m_hfi;				// HFI状态结构体
	int m_hfi_plot_en;				// HFI绘图使能
	float m_hfi_plot_sample;		// HFI绘图采样值

	// 制动相关变量
	float m_br_speed_before;		// 制动前速度
	float m_br_vq_before;			// 制动前q轴电压
	int m_br_no_duty_samples;		// 无占空比采样计数

	// 占空比控制相关
	float m_duty_abs_filtered;		// 滤波后的占空比绝对值
	float m_duty_filtered;			// 滤波后的占空比
	bool m_was_control_duty;		// 之前是否为占空比控制模式
	float m_duty_i_term;			// 占空比控制积分项
	bool duty_was_pi;				// 占空比是否为PI控制
	float duty_pi_duty_last;		// 上次占空比PI控制值

	// 开环和观测器历史值
	float m_openloop_angle;			// 开环角度
	float m_x1_prev;				// 观测器x1前值
	float m_x2_prev;				// 观测器x2前值
	float m_phase_before_speed_est;	// 速度估计前的相位

	// 转速计和角度相关
	int m_tacho_step_last;			// 上次转速计步数
	float m_pid_div_angle_last;		// 上次PID分频角度
	float m_pid_div_angle_accumulator;	// PID分频角度累加器
	float m_min_rpm_hyst_timer;		// 最小转速滞回定时器
	float m_min_rpm_timer;			// 最小转速定时器
	bool m_cc_was_hfi;				// 电流控制是否曾使用HFI

	// 位置PID控制器状态变量
	float m_pos_i_term;				// 位置PID积分项
	float m_pos_prev_error;			// 位置PID上次误差
	float m_pos_dt_int;				// 位置PID时间积分
	float m_pos_prev_proc;			// 位置PID上次过程值
	float m_pos_dt_int_proc;		// 位置PID过程时间积分
	float m_pos_d_filter;			// 位置PID微分滤波器
	float m_pos_d_filter_proc;		// 位置PID过程微分滤波器

	// 速度PID控制器状态变量
	float m_speed_i_term;			// 速度PID积分项
	float m_speed_prev_error;		// 速度PID上次误差
	float m_speed_d_filter;			// 速度PID微分滤波器

	// 霍尔传感器相关
	int m_ang_hall_int_prev;		// 上次霍尔角度整数值
	bool m_using_hall;				// 是否使用霍尔传感器标志
	float m_ang_hall;				// 霍尔传感器角度
	float m_ang_hall_rate_limited;	// 限速后的霍尔角度
	float m_hall_dt_diff_last;		// 上次霍尔时间差
	float m_hall_dt_diff_now;		// 当前霍尔时间差
	bool m_motor_released;			// 电机释放标志

	// 电阻观测器
	float m_res_est;				// 估计电阻值 (Ω)
	float m_r_est_state;			// 电阻估计状态

	// 温度补偿参数
	float m_res_temp_comp;			// 温度补偿后的电阻值 (Ω)
	float m_current_ki_temp_comp;	// 温度补偿后的电流积分增益

	// 预计算值 - 用于提高实时性能
	float p_lq;						// q轴电感 Lq (H)
	float p_ld;						// d轴电感 Ld (H)
	float p_inv_ld_lq;				// 电感倒数差 (1/Lq - 1/Ld)
	float p_v2_v3_inv_avg_half;		// 电感倒数平均值的一半 (0.5/Ld + 0.5/Lq)
} motor_all_state_t;

// FOC数学函数声明

/**
 * 观测器更新函数
 *
 * 实现无传感器FOC控制中的转子位置观测算法，支持多种观测器类型：
 * - Ortega观测器：基于磁链幅值约束的非线性观测器
 * - MXLEMMING观测器：改进的线性观测器
 * - 带磁链补偿的观测器：适应电机饱和特性
 *
 * @param v_alpha α轴电压 (V)
 * @param v_beta  β轴电压 (V)
 * @param i_alpha α轴电流 (A)
 * @param i_beta  β轴电流 (A)
 * @param dt      采样时间间隔 (s)
 * @param state   观测器状态指针
 * @param phase   输出的转子相位指针 (rad)
 * @param motor   电机状态结构体指针
 */
void foc_observer_update(float v_alpha, float v_beta, float i_alpha, float i_beta,
		float dt, observer_state *state, float *phase, motor_all_state_t *motor);

/**
 * 锁相环(PLL)运行函数
 *
 * 实现数字锁相环算法，用于跟踪观测器输出的相位信号，
 * 提供平滑的相位和速度估计。
 *
 * PLL方程：
 * θ̇ = ω + Kp·Δθ
 * ω̇ = Ki·Δθ
 * 其中 Δθ = θ_input - θ_pll
 *
 * @param phase     输入相位 (rad)
 * @param dt        采样时间间隔 (s)
 * @param phase_var PLL相位变量指针 (rad)
 * @param speed_var PLL速度变量指针 (rad/s)
 * @param conf      电机配置参数
 */
void foc_pll_run(float phase, float dt, float *phase_var,
		float *speed_var, mc_configuration *conf);

/**
 * 空间矢量调制(SVM)函数
 *
 * 将αβ坐标系的电压矢量转换为三相PWM占空比。
 * SVM相比正弦PWM具有更高的直流电压利用率(约15%提升)。
 *
 * 算法原理：
 * 1. 根据αβ电压确定所在扇区(1-6)
 * 2. 计算相邻基本矢量的作用时间
 * 3. 按照对称分布原则安排开关时序
 *
 * 约束条件：|V| ≤ √3/2 ≈ 0.866 (避免过调制)
 *
 * @param alpha           α轴电压 (标幺值)
 * @param beta            β轴电压 (标幺值)
 * @param PWMFullDutyCycle PWM计数器峰值
 * @param tAout           A相PWM占空比输出
 * @param tBout           B相PWM占空比输出
 * @param tCout           C相PWM占空比输出
 * @param svm_sector      SVM扇区号输出(1-6)
 */
void foc_svm(float alpha, float beta, uint32_t PWMFullDutyCycle,
		uint32_t* tAout, uint32_t* tBout, uint32_t* tCout, uint32_t *svm_sector);

/**
 * 位置PID控制函数
 *
 * 实现位置闭环控制，输出转矩电流命令。
 * 支持增益调度、积分抗饱和、微分滤波等高级功能。
 *
 * PID方程：
 * u(t) = Kp·e(t) + Ki·∫e(t)dt + Kd·de(t)/dt
 *
 * @param index_found 编码器索引是否找到
 * @param dt          控制周期 (s)
 * @param motor       电机状态结构体指针
 */
void foc_run_pid_control_pos(bool index_found, float dt, motor_all_state_t *motor);

/**
 * 速度PID控制函数
 *
 * 实现速度闭环控制，输出转矩电流命令。
 * 包含速度斜坡、积分抗饱和、制动禁用等功能。
 *
 * @param dt    控制周期 (s)
 * @param motor 电机状态结构体指针
 */
void foc_run_pid_control_speed(float dt, motor_all_state_t *motor);

/**
 * 编码器角度校正函数
 *
 * 在观测器角度和编码器角度之间进行切换，
 * 根据转速自动选择最可靠的角度源。
 *
 * @param obs_angle 观测器角度 (rad)
 * @param enc_angle 编码器角度 (rad)
 * @param speed     当前速度 (rad/s)
 * @param sl_erpm   切换转速阈值 (ERPM)
 * @param motor     电机状态结构体指针
 * @return          校正后的角度 (rad)
 */
float foc_correct_encoder(float obs_angle, float enc_angle, float speed, float sl_erpm, motor_all_state_t *motor);

/**
 * 霍尔传感器角度校正函数
 *
 * 处理霍尔传感器信号，提供角度估计和插值。
 * 支持120度和60度霍尔传感器配置。
 *
 * @param angle    输入角度 (rad)
 * @param dt       采样时间间隔 (s)
 * @param motor    电机状态结构体指针
 * @param hall_val 霍尔传感器值(0-7)
 * @return         校正后的角度 (rad)
 */
float foc_correct_hall(float angle, float dt, motor_all_state_t *motor, int hall_val);

/**
 * 弱磁控制函数
 *
 * 实现弱磁控制算法，通过注入负d轴电流扩展电机高速运行范围。
 * 当占空比超过阈值时自动启动弱磁控制。
 *
 * 弱磁原理：
 * 通过负d轴电流削弱永磁体磁场，降低反电动势，
 * 使电机能在更高转速下运行。
 *
 * @param motor 电机状态结构体指针
 * @param dt    控制周期 (s)
 */
void foc_run_fw(motor_all_state_t *motor, float dt);

/**
 * 高频注入角度调整函数
 *
 * 根据HFI检测到的角度误差调整转子位置估计。
 * 使用双积分器结构提高跟踪性能。
 *
 * @param ang_err 角度误差 (rad)
 * @param motor   电机状态结构体指针
 * @param dt      控制周期 (s)
 */
void foc_hfi_adjust_angle(float ang_err, motor_all_state_t *motor, float dt);

/**
 * 预计算值函数
 *
 * 预计算电机参数相关的常用值，提高实时控制性能。
 * 包括d/q轴电感、电感倒数等。
 *
 * @param motor 电机状态结构体指针
 */
void foc_precalc_values(motor_all_state_t *motor);

#endif /* FOC_MATH_H_ */
