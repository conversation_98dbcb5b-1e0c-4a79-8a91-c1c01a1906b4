#include "AnoPTv8FrameFactory.h"
#include "AnoPTv8.h"
#include "AnoPTv8Run.h"
#include "AnoPTv8Par.h"
#include "AnoPTv8Cmd.h"
#include "HWInterface.h"
#include "usb_app.h"


// 外部声明大缓冲区
extern _largeBufST AnoPTv8LargeTxBuf;
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 计算帧校验值
 * @details 计算帧的校验和(sumcheck)和附加校验和(addcheck)
 * @param[in,out] p 帧数据结构体指针
 * @note 校验计算范围为帧头到数据域的所有字节
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8CalFrameCheck(_un_frame_v8 *p)
{
    uint8_t sumcheck = 0, addcheck = 0;
    for (uint16_t i = 0; i < (ANOPTV8_FRAME_HEADLEN + p->frame.datalen); i++)
    {
        sumcheck += p->rawBytes[i];
        addcheck += sumcheck;
    }
    p->rawBytes[ANOPTV8_FRAME_HEADLEN + p->frame.datalen] = sumcheck;
    p->rawBytes[ANOPTV8_FRAME_HEADLEN + p->frame.datalen + 1] = addcheck;
    p->frame.sumcheck = sumcheck;
    p->frame.addcheck = addcheck;
}

/**
 * @brief 大缓冲区专用校验计算函数
 * @details 直接在大缓冲区的指定位置计算并写入校验值
 * @param[in] buffer 缓冲区指针
 * @param[in] startPos 帧起始位置
 * @param[in] frameLen 帧长度（不含校验字节）
 */
void AnoPTv8CalFrameCheckInBuffer(uint8_t *buffer, uint16_t startPos, uint16_t frameLen)
{
    uint8_t sumcheck = 0, addcheck = 0;
    
    // 计算校验值
    for(uint16_t i = 0; i < frameLen; i++)
    {
        sumcheck += buffer[startPos + i];
        addcheck += sumcheck;
    }
    
    // 直接写入校验字节到缓冲区
    buffer[startPos + frameLen] = sumcheck;
    buffer[startPos + frameLen + 1] = addcheck;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送数据缓冲区
 * @details 将数据打包成帧并发送（使用大缓冲区实现）
 * @param[in] daddr 目标设备地址
 * @param[in] fid 功能码(帧ID)
 * @param[in] buf 待发送的数据缓冲区
 * @param[in] len 数据长度
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendBuf(uint8_t daddr, uint8_t fid, uint8_t *buf, uint16_t len)
{
    // 外部声明大缓冲区
    extern _largeBufST AnoPTv8LargeTxBuf;
    
    // 计算完整帧的长度：帧头(6) + 数据长度(len) + 校验(2)
    uint16_t frameLen = ANOPTV8_FRAME_HEADLEN + len + 2;
    
    // 检查缓冲区空间是否足够
    if(AnoPTv8LargeTxBuf.writePos + frameLen > 672)
    {
        // 缓冲区空间不足，直接发送现有数据并重置
        if(AnoPTv8LargeTxBuf.writePos > 0)
        {
            AnoPTv8HwSendBytes(AnoPTv8LargeTxBuf.buffer, AnoPTv8LargeTxBuf.writePos);
            AnoPTv8LargeTxBuf.writePos = 0;
        }
    }
    
    uint16_t currentPos = AnoPTv8LargeTxBuf.writePos;
    
    // 直接在大缓冲区构建帧头
    AnoPTv8LargeTxBuf.buffer[currentPos++] = ANOPTV8_FRAME_HEAD;  // 帧头
    AnoPTv8LargeTxBuf.buffer[currentPos++] = ANOPTV8_MYDEVID;     // 源设备ID
    AnoPTv8LargeTxBuf.buffer[currentPos++] = daddr;               // 目标设备ID
    AnoPTv8LargeTxBuf.buffer[currentPos++] = fid;                 // 帧ID
    AnoPTv8LargeTxBuf.buffer[currentPos++] = len & 0xFF;          // 数据长度低字节
    AnoPTv8LargeTxBuf.buffer[currentPos++] = (len >> 8) & 0xFF;   // 数据长度高字节
    
    // 直接复制数据到大缓冲区
    if(buf != NULL && len > 0)
    {
        memcpy(&AnoPTv8LargeTxBuf.buffer[currentPos], buf, len);
        currentPos += len;
    }
    
    // 使用专用函数计算并写入校验值
    AnoPTv8CalFrameCheckInBuffer(AnoPTv8LargeTxBuf.buffer, 
                                 AnoPTv8LargeTxBuf.writePos, 
                                 ANOPTV8_FRAME_HEADLEN + len);
    
    // 更新写入位置
    AnoPTv8LargeTxBuf.writePos += frameLen;
    AnoPTv8LargeTxBuf.dataLength = AnoPTv8LargeTxBuf.writePos;
    AnoPTv8LargeTxBuf.readyToSend = 1;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送校验帧
 * @details 发送帧校验结果
 * @param[in] daddr 目标设备地址
 * @param[in] id 被校验的帧ID
 * @param[in] sc 校验和
 * @param[in] ac 附加校验和
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendCheck(uint8_t daddr, uint8_t id, uint8_t sc, uint8_t ac)
{
    uint8_t data[3];
    data[0] = id;
    data[1] = sc;
    data[2] = ac;
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0x00, data, 3);
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送设备信息
 * @details 发送本设备的硬件信息、软件版本等
 * @param[in] daddr 目标设备地址
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendDevInfo(uint8_t daddr)
{
    uint8_t data[50];  // 足够大的缓冲区
    uint8_t _cnt = 0;

    // 调试信息：记录设备信息发送
    static uint32_t dev_info_send_count = 0;
    dev_info_send_count++;

    data[_cnt++] = ANOPTV8_MYDEVID;
    uint16_t _tmp = ANOPTV8_HWVER;
    data[_cnt++] = BYTE0(_tmp);
    data[_cnt++] = BYTE1(_tmp);
    _tmp = ANOPTV8_SWVER;
    data[_cnt++] = BYTE0(_tmp);
    data[_cnt++] = BYTE1(_tmp);
    _tmp = ANOPTV8_BLVER;
    data[_cnt++] = BYTE0(_tmp);
    data[_cnt++] = BYTE1(_tmp);
    _tmp = ANOPTV8_PTVER;
    data[_cnt++] = BYTE0(_tmp);
    data[_cnt++] = BYTE1(_tmp);

    uint8_t i = 0;
    char *str = "CDM602_125KW";
    while(*(str+i) != '\0' && i < 20)//发送设备名称字符串
    {
        data[_cnt++] = *(str+i++);
    }

    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xE3, data, _cnt);
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送字符串
 * @details 发送带颜色的字符串消息
 * @param[in] daddr 目标设备地址
 * @param[in] string_color 字符串颜色
 * @param[in] str 字符串内容
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendStr(uint8_t daddr, uint8_t string_color, char *str)
{
    uint8_t data[101];  // 足够大的缓冲区：1字节颜色 + 最多100字节字符串
    uint8_t _cnt = 0;
    
    // 添加颜色字节
    data[_cnt++] = string_color;
    
    // 添加字符串内容
    uint8_t i = 0;
    while(*(str+i) != '\0' && i < 100)
    {
        data[_cnt++] = *(str+i++);
    }
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xA0, data, _cnt);
}

/**
 * @brief 发送带数值的字符串
 * @details 发送一个浮点数值及其描述字符串
 * @param[in] daddr 目标设备地址
 * @param[in] val 浮点数值
 * @param[in] str 描述字符串
 */
void AnoPTv8SendValStr(uint8_t daddr, float val, char *str)
{
    uint8_t data[55];  // 足够大的缓冲区：4字节浮点数 + 最多51字节字符串
    uint8_t _cnt = 0;
    
    // 添加浮点数值（小端模式）
    uint8_t *p = (uint8_t *)&val;
    data[_cnt++] = p[0];
    data[_cnt++] = p[1];
    data[_cnt++] = p[2];
    data[_cnt++] = p[3];
    
    // 添加描述字符串
    uint8_t i = 0;
    while(*(str+i) != '\0' && i < 50)
    {
        data[_cnt++] = *(str+i++);
    }
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xA1, data, _cnt);
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送参数数量
 * @details 发送系统参数总数
 * @param[in] daddr 目标设备地址
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendParNum(uint8_t daddr)
{
    uint8_t data[3];
    
    data[0] = 1;
    uint16_t _tmp = AnoPTv8ParGetCount();
    data[1] = BYTE0(_tmp);
    data[2] = BYTE1(_tmp);
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xE0, data, 3);
}

/**
 * @brief 发送参数值
 * @details 发送指定参数ID的参数值，根据参数类型发送相应长度的数据
 * @param[in] daddr 目标设备地址
 * @param[in] parid 参数ID
 */
void AnoPTv8SendParVal(uint8_t daddr, uint16_t parid)
{
    uint8_t data[6];
    uint8_t _cnt = 0;
    
    // 参数ID
    data[_cnt++] = BYTE0(parid);
    data[_cnt++] = BYTE1(parid);
    
    // 通过查找表获取参数类型
    uint8_t param_type = AnoPTv8GetParamType(parid);
    const _st_par_info *p_info = AnoPTv8ParGetInfo(parid);
    
    if(p_info != 0) {
        switch(param_type) {
            case 0: { // uint8类型 (1字节)
                uint8_t val = *((uint8_t*)p_info->pval);
                data[_cnt++] = val;
                break;
            }
            case 1: { // int8类型 (1字节)
                int8_t val = *((int8_t*)p_info->pval);
                data[_cnt++] = (uint8_t)val;
                break;
            }
            case 2: { // uint16类型 (2字节)
                uint16_t val = *((uint16_t*)p_info->pval);
                data[_cnt++] = BYTE0(val);
                data[_cnt++] = BYTE1(val);
                break;
            }
            case 3: { // int16类型 (2字节)
                int16_t val = *((int16_t*)p_info->pval);
                data[_cnt++] = BYTE0(val);
                data[_cnt++] = BYTE1(val);
                break;
            }
            case 4: { // uint32类型 (4字节)
                uint32_t val = *((uint32_t*)p_info->pval);
                data[_cnt++] = BYTE0(val);
                data[_cnt++] = BYTE1(val);
                data[_cnt++] = BYTE2(val);
                data[_cnt++] = BYTE3(val);
                break;
            }
            case 5: { // int32类型 (4字节)
                int32_t val = *((int32_t*)p_info->pval);
                data[_cnt++] = BYTE0(val);
                data[_cnt++] = BYTE1(val);
                data[_cnt++] = BYTE2(val);
                data[_cnt++] = BYTE3(val);
                break;
            }
            case 8: // float类型 (4字节)
            default: {
                float val = *((float*)p_info->pval);
                uint8_t *p = (uint8_t *)&val;
                data[_cnt++] = p[0];
                data[_cnt++] = p[1];
                data[_cnt++] = p[2];
                data[_cnt++] = p[3];
                break;
            }
        }
    }
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xE1, data, _cnt);
}

/**
 * @brief 发送参数信息
 * @details 发送指定参数ID的详细信息(参数ID、参数类型、参数名称、参数说明)
 * @param[in] daddr 目标设备地址
 * @param[in] parid 参数ID
 * @note 帧格式(0xE2):
 *       - DATA[0:1]: 参数ID (uint16_t)
 *       - DATA[2]: 参数类型 (uint8_t) - 固定为8(float类型)
 *       - DATA[3:22]: 参数名称 (char[20], 不足补0)
 *       - DATA[23:N]: 参数说明 (char[], 可变长度)
 */
void AnoPTv8SendParInfo(uint8_t daddr, uint16_t parid)
{
    uint8_t data[100];  // 足够大的缓冲区
    uint8_t _cnt = 0;

    if(parid >= AnoPTv8ParGetCount())
        return;

    if(AnoPTv8ParGetInfo(parid) == 0)
        return;

    const _st_par_info _p = *AnoPTv8ParGetInfo(parid);

    // 参数ID
    data[_cnt++] = BYTE0(parid);
    data[_cnt++] = BYTE1(parid);
    
    // 参数类型（从mul字段读取，用作类型标识）
    // 0=uint8, 1=int8, 2=uint16, 3=int16, 4=uint32, 5=int32, 6=uint64, 7=int64, 8=float, 9=double
    data[_cnt++] = _p.mul;
    
    // 参数名称（20字节，不足补0）
    for(uint8_t i = 0; i < 20; i++)
        data[_cnt++] = _p.name[i];
    
    // 参数说明
    uint8_t i = 0;
    while(_p.info[i] != '\0' && i < 70)
    {
        data[_cnt++] = _p.info[i++];
    }
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xE2, data, _cnt);
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @brief 发送命令数量
 * @details 发送系统命令总数
 * @param[in] daddr 目标设备地址
 */
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void AnoPTv8SendCmdNum(uint8_t daddr)
{
    uint8_t data[3];
    
    data[0] = 0;
    uint16_t _tmp = AnoPTv8CmdGetCount();
    data[1] = BYTE0(_tmp);
    data[2] = BYTE1(_tmp);
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xC1, data, 3);
}

/**
 * @brief 发送命令信息
 * @details 发送指定命令的详细信息(命令ID、参数值、名称、说明等)
 * @param[in] daddr 目标设备地址
 * @param[in] cmd 命令ID
 */
void AnoPTv8SendCmdInfo(uint8_t daddr, uint16_t cmd)
{
    uint8_t data[100];  // 足够大的缓冲区
    uint8_t _cnt = 0;

    if(cmd >= AnoPTv8CmdGetCount())
        return;

    if(AnoPTv8CmdGetInfo(cmd) == 0)
        return;

    const _st_cmd_info _p = *AnoPTv8CmdGetInfo(cmd);

    // 命令ID
    data[_cnt++] = BYTE0(cmd);
    data[_cnt++] = BYTE1(cmd);
    
    // 命令参数
    data[_cnt++] = _p.cmd.cmdid0;
    data[_cnt++] = _p.cmd.cmdid1;
    data[_cnt++] = _p.cmd.cmdid2;
    data[_cnt++] = _p.cmd.cmdval0;
    data[_cnt++] = _p.cmd.cmdval1;
    data[_cnt++] = _p.cmd.cmdval2;
    data[_cnt++] = _p.cmd.cmdval3;
    data[_cnt++] = _p.cmd.cmdval4;
    data[_cnt++] = _p.cmd.cmdval5;
    data[_cnt++] = _p.cmd.cmdval6;
    data[_cnt++] = _p.cmd.cmdval7;

    // 命令名称（20字节）
    for(uint8_t i = 0; i < 20; i++)
        data[_cnt++] = _p.name[i];
    
    // 命令说明
    uint8_t i = 0;
    while(_p.info[i] != '\0' && i < 50)
    {
        data[_cnt++] = _p.info[i++];
    }
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xC2, data, _cnt);
}

/**
 * @brief 发送任意帧
 * @details 发送自定义数据帧
 * @param[in] daddr 目标设备地址
 * @param[in] fid 功能码(帧ID)
 * @param[in] dat 数据指针
 * @param[in] len 数据长度
 */
void AnoPTv8SendAnyFrame(uint8_t daddr, uint8_t fid, const uint8_t *dat, const uint8_t len)
{
    // 直接使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, fid, (uint8_t*)dat, len);
}

/**
 * @brief 发送IAP命令
 * @details 发送IAP(在线升级)相关命令
 * @param[in] daddr 目标设备地址
 * @param[in] dat 命令数据指针
 * @param[in] len 数据长度
 */
void AnoPTv8SendIapCmd(uint8_t daddr, const uint8_t *dat, const uint8_t len)
{
    uint8_t _buf[20];  // 足够大的缓冲区

    _buf[0] = 0;
    memcpy(&_buf[1], dat, len);
    
    // 使用AnoPTv8SendBuf发送
    AnoPTv8SendBuf(daddr, 0xF0, _buf, len+1);
}
