#ifndef __SPI_FLASH_H        
#define __SPI_FLASH_H

#include "at32a423.h"

#define SPI_TRANS_DMA

#define FLASH_CS_HIGH()                  gpio_bits_set(GPIOA, GPIO_PINS_15)      // Flash片选引脚置高
#define FLASH_CS_LOW()                   gpio_bits_reset(GPIOA, GPIO_PINS_15)    // Flash片选引脚置低

#define W25Q256                          0xEF18    // W25Q256芯片ID

//SPI Flash操作定义
#define SPIF_CHIP_SIZE                   0x2000000  // Flash芯片总容量32MB
#define SPIF_SECTOR_SIZE                 4096       // 扇区大小4KB
#define SPIF_PAGE_SIZE                   256        // 页大小256B

#define SPIF_WRITEENABLE                 0x06       // 写使能命令
#define SPIF_WRITEDISABLE                0x04       // 写禁止命令
#define SPIF_ENABLE4BYTEADDR             0xB7       // 启用4字节地址命令
#define SPIF_DISABLE4BYTEADDR            0xE9       // 禁用4字节地址命令
/* s7-s0 */                                         // 状态寄存器1(bit7-0)
#define SPIF_READSTATUSREG1              0x05       // 读状态寄存器1命令
#define SPIF_WRITESTATUSREG1             0x01       // 写状态寄存器1命令

/* s15-s8 */                                        // 状态寄存器2(bit15-8)
#define SPIF_READSTATUSREG2              0x35       // 读状态寄存器2命令
#define SPIF_WRITESTATUSREG2             0x31       // 写状态寄存器2命令
/* s23-s16 */                                       // 状态寄存器3(bit23-16)
#define SPIF_READSTATUSREG3              0x15       // 读状态寄存器3命令
#define SPIF_WRITESTATUSREG3             0x11       // 写状态寄存器3命令
#define SPIF_READDATA                    0x03       // 读数据命令
#define SPIF_FASTREADDATA                0x0B       // 快速读数据命令
#define SPIF_FASTREADDUAL                0x3B       // 双线快速读命令
#define SPIF_PAGEPROGRAM                 0x02       // 页编程命令
/* block size:64kb */                               // 块大小64KB
#define SPIF_BLOCKERASE                  0xD8       // 块擦除命令
#define SPIF_SECTORERASE                 0x20       // 扇区擦除命令
#define SPIF_CHIPERASE                   0xC7       // 芯片擦除命令
#define SPIF_POWERDOWN                   0xB9       // 掉电命令
#define SPIF_RELEASEPOWERDOWN            0xAB       // 释放掉电命令
#define SPIF_DEVICEID                    0xAB       // 读设备ID命令
#define SPIF_MANUFACTDEVICEID            0x90       // 读制造商ID命令
#define SPIF_JEDECDEVICEID               0x9F       // 读JEDEC ID命令
#define FLASH_SPI_DUMMY_BYTE             0xA5       // SPI空字节

// 添加状态寄存器3的ADS位定义
#define SPIF_SR3_ADS                     0x01       // 状态寄存器3的ADS位(bit0)

void spiflash_init(void);                                                             // Flash初始化函数
void spiflash_write(uint8_t *pbuffer, uint32_t write_addr, uint32_t length);          // Flash写入函数
void spiflash_read(uint8_t *pbuffer, uint32_t read_addr, uint32_t length);            // Flash读取函数
void spiflash_sector_erase(uint32_t erase_addr);                                      // 扇区擦除函数
void spiflash_write_nocheck(uint8_t *pbuffer, uint32_t write_addr, uint32_t length);  // 无检查写入函数
void spiflash_page_write(uint8_t *pbuffer, uint32_t write_addr, uint32_t length);     // 页写入函数
void spi_bytes_write(uint8_t *pbuffer, uint32_t length);                              // SPI字节写入函数
void spi_bytes_read(uint8_t *pbuffer, uint32_t length);                               // SPI字节读取函数
void spiflash_wait_busy(void);                                                        // 等待Flash空闲函数
uint8_t spiflash_read_sr1(void);                                                      // 读状态寄存器1函数
void spiflash_write_enable(void);                                                     // 写使能函数
uint16_t spiflash_read_id(void);                                                      // 读ID函数
uint8_t spi_byte_write(uint8_t data);                                                 // SPI单字节写入函数
uint8_t spi_byte_read(void);                                                          // SPI单字节读取函数
void spiflash_enable_4byte_addr(void);     // 使能4字节地址模式函数
uint8_t spiflash_read_sr3(void);                                      // 读状态寄存器3函数
uint8_t spiflash_check_4byte_mode(void);                             // 检查4字节地址模式函数

#endif    
