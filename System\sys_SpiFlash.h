#ifndef __SYS_SPIFLASH_H
#define __SYS_SPIFLASH_H

#include "at32a423.h"
#include "SPI_w25q256.h"

/*******************************************************************************
 * Flash分区定义
 ******************************************************************************/
#define FLASH_BLOCK_SIZE         (64*1024)    // 块大小64KB
#define FLASH_TOTAL_BLOCKS       512          // 总块数(32MB/64KB=512)

/* 分区1：系统参数区(Block 0) */
#define FLASH_PARAM_BLOCK        0
#define FLASH_PARAM_ADDR         (FLASH_PARAM_BLOCK * FLASH_BLOCK_SIZE)

/* 分区2：参数地址索引区(Block 1-15) */
#define FLASH_INDEX_START_BLOCK  1
#define FLASH_INDEX_END_BLOCK    15
#define FLASH_INDEX_START_ADDR   (FLASH_INDEX_START_BLOCK * FLASH_BLOCK_SIZE)
#define FLASH_INDEX_SIZE         (15 * FLASH_BLOCK_SIZE)

/* 分区3：参数数据区(Block 16-499) */
#define FLASH_DATA_START_BLOCK   16
#define FLASH_DATA_END_BLOCK     499
#define FLASH_DATA_START_ADDR    (FLASH_DATA_START_BLOCK * FLASH_BLOCK_SIZE)
#define FLASH_DATA_SIZE          (484 * FLASH_BLOCK_SIZE)

/* 分区4：保留区(Block 500-511) */
#define FLASH_RESERVE_START_BLOCK 500
#define FLASH_RESERVE_END_BLOCK   511

/*******************************************************************************
 * 数据类型定义
 ******************************************************************************/
/* 数据类型标识 */
#define DATA_TYPE_SELFTEST       0xF0    // 自检存储
#define DATA_TYPE_NORMAL         0xF1    // 常规存储
#define DATA_TYPE_WARNING        0xF2    // 告警存储
#define DATA_TYPE_FAULT          0xF3    // 故障存储

/* 电机参数存储区定义 */
#define MOTOR_PARAM_SECTOR       1       // 参数存储扇区号(第0块第1扇区)
#define MOTOR_PARAM_ADDR         0x1000  // 参数起始地址(0x1000-0x1FFF)
#define MOTOR_PARAM_PAGE_SIZE    256     // 页大小
#define MOTOR_PARAM_PAGE_COUNT   8       // 循环存储页数
#define MOTOR_PARAM_MAGIC        0x5AA5  // 参数有效标志

/*******************************************************************************
 * 结构体定义
 ******************************************************************************/
/* 电机参数结构体 */
typedef struct {
    uint16_t magic;              // 魔数(参数有效标志)
    uint16_t version;            // 参数版本号
    
    /* 基本控制参数 */
    float ConTc;                 // 采样时间s
    float GiveUdc;               // 母线电压Udc
    
    /* 电机额定参数 */
    float MotorRatedVoltage;     // 电机额定电压
    float MotorRatedCurrent;     // 电机额定电流
    float MotorRatedFre;         // 电机额定频率
    float MotorRatedPower;       // 电机额定功率
    float MotorRateSpeed;        // 电机额定转速
    float MotorPoleNum;          // 电机极对数
    float MotorRatedSlip;        // 电机额定滑差
    
    /* 永磁同步电机参数 */
    float SynRs;                 // 定子电阻
    float SynLsd;                // d轴电感
    float SynLsq;                // q轴电感
    float SynFlux;               // 电机磁链
    
    /* 速度环PI参数 */
    float SynVcSpeedKi;          // 转速环积分增益
    float SynVcSpeedKp;          // 转速环比例增益
    
    /* 电流环PI参数 */
    float SynVcCurrentKi;        // 电流环积分增益
    float SynVcCurrentKp;        // 电流环比例增益
    float SynVcCurrentKi2;       // 高速PI,Ki
    float SynVcCurrentKp2;       // 高速PI,Kp
    float SynVcCurrentKi1;       // 中速PI,Ki
    float SynVcCurrentKp1;       // 中速PI,Kp
    
    /* 磁链环PI参数 */
    float SynVcFWKp;             // 磁链PI,Kp
    float SynVcFWKi;             // 磁链PI,Ki
    float SynVcLoseSpeedValue;   // 失速速度值
    float fluxidmin;             // 磁链输出最小限幅
    
    /* 其他控制参数 */
    float DBT;                   // 死区时间us
    float DBEN;                  // 死区补偿使能
    float PosCompEN;             // 正补偿使能
    float feedforwordFlag;       // 前馈补偿标志
    float ConRunType;            // 变频器控制方式
    float ConStrategy;           // 控制策略
    float VFDLoadType;           // 负载类型
    float Speed_mid;             // 分段PI，中速
    float Speed_high;            // 分段PI，高速
    float marslpfw;              // LPF滤波系数
} __attribute__((packed)) Motor_Param_t;

/* 参数记录结构体 */
typedef struct {
    /* 基本信息 */
    uint8_t  data_type;          // 数据类型
    uint8_t  relative_time[3];   // 相对时间
    uint8_t  absolute_time[4];   // 绝对时间
    uint16_t run_count;          // 运行次数
    uint16_t motor_speed;        // 转速
    
    /* 测量数据(全部4字节float) */
    float bus_voltage;           // 母线电压
    float id_current;            // d轴电流
    float iq_current;            // q轴电流
    float motor_temp;            // 电机温度
    float cap_temp;              // 电容温度
    float sic_temp;              // 碳化硅温度
    float oil_temp;              // 油温温度
    
    /* 状态记录 */
    uint16_t run_status;         // 运行状态
    uint16_t fault_status;       // 故障/告警状态
    
    /* 预留 */
    uint32_t reserved1;          // 预留1
    uint32_t reserved2;          // 预留2
} __attribute__((packed)) Param_Record_t;

/* 参数记录管理结构体 */
typedef struct {
    uint32_t total_records;      // 总记录数
    uint32_t selftest_count;     // 自检记录数
    uint32_t normal_count;       // 常规记录数
    uint32_t warning_count;      // 告警记录数
    uint32_t fault_count;        // 故障记录数
    uint32_t last_record_addr;   // 最后一条记录地址
} Param_Manager_t;

/* 参数区结构定义 */
typedef struct {
    uint32_t param_magic;        // 参数区魔数
    uint32_t write_count;        // 写入次数计数
    uint32_t last_write_addr;    // 最后一次写入地址
    uint8_t  reserved[52];       // 保留字节(凑够64字节)
} Flash_Param_t;

/* 参数索引结构定义 */
typedef struct {
    uint32_t param_addr;        // 参数存储地址
    uint32_t write_time;        // 写入时间戳
    uint8_t  valid;             // 有效标志
    uint8_t  reserved[3];       // 保留字节(4字节对齐)
} Flash_Index_t;

/* Flash管理结构体 */
typedef struct {
    Flash_Param_t param;         // 系统参数
    Flash_Index_t *index;        // 参数索引指针
    uint32_t current_addr;       // 当前写入地址
    uint8_t initialized;         // 初始化标志
} Flash_Manager_t;

/* 电机参数指针结构体 */
typedef struct {
    /* 基本控制参数 */
    float *ConTc;                 // 采样时间s
    float *GiveUdc;              // 母线电压Udc
    
    /* 电机额定参数 */
    float *MotorRatedVoltage;    // 电机额定电压
    float *MotorRatedCurrent;    // 电机额定电流
    float *MotorRatedFre;        // 电机额定频率
    float *MotorRatedPower;      // 电机额定功率
    float *MotorRateSpeed;       // 电机额定转速
    float *MotorPoleNum;         // 电机极对数
    float *MotorRatedSlip;       // 电机额定滑差
    
    /* 永磁同步电机参数 */
    float *SynRs;                // 定子电阻
    float *SynLsd;               // d轴电感
    float *SynLsq;               // q轴电感
    float *SynFlux;              // 电机磁链
    
    /* 速度环PI参数 */
    float *SynVcSpeedKi;         // 转速环积分增益
    float *SynVcSpeedKp;         // 转速环比例增益
    
    /* 电流环PI参数 */
    float *SynVcCurrentKi;       // 电流环积分增益
    float *SynVcCurrentKp;       // 电流环比例增益
    float *SynVcCurrentKi2;      // 高速PI,Ki
    float *SynVcCurrentKp2;      // 高速PI,Kp
    float *SynVcCurrentKi1;      // 中速PI,Ki
    float *SynVcCurrentKp1;      // 中速PI,Kp
    
    /* 磁链环PI参数 */
    float *SynVcFWKp;            // 磁链PI,Kp
    float *SynVcFWKi;            // 磁链PI,Ki
    float *SynVcLoseSpeedValue;  // 失速速度值
    float *fluxidmin;            // 磁链输出最小限幅
    
    /* 其他控制参数 */
    float *DBT;                  // 死区时间us
    float *DBEN;                 // 死区补偿使能
    float *PosCompEN;            // 正补偿使能
    float *feedforwordFlag;      // 前馈补偿标志
    float *ConRunType;           // 变频器控制方式
    float *ConStrategy;          // 控制策略
    float *VFDLoadType;          // 负载类型
    float *Speed_mid;            // 分段PI，中速
    float *Speed_high;           // 分段PI，高速
    float *marslpfw;             // LPF滤波系数
} MotorParamPtrs_t;

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
/* 电机参数操作函数 */
uint8_t Flash_Write_Motor_Param(Motor_Param_t *param);    // 写入电机参数
uint8_t Flash_Read_Motor_Param(Motor_Param_t *param);     // 读取电机参数 

/* Flash基本操作函数 */
void Flash_Init(void);                                    // Flash初始化
uint8_t Flash_Write_Params(uint8_t *data, uint32_t length);    // 写入参数
uint8_t Flash_Read_Params(uint8_t *data, uint32_t length);     // 读取参数
void Flash_Erase_All(void);                              // 擦除所有数据
uint32_t Flash_Get_Free_Size(void);                      // 获取剩余空间

/* 记录操作函数 */
uint8_t Flash_Write_Record(Param_Record_t *record);      // 写入一条记录
uint8_t Flash_Read_Last_Record(Param_Record_t *record);  // 读取最后一条记录
uint8_t Flash_Read_Record_ByType(uint8_t type,          // 按类型读取记录
                                uint32_t index, 
                                Param_Record_t *record);
uint32_t Flash_Get_Record_Count(uint8_t type);          // 获取指定类型的记录数

/* 全局变量声明 */
extern Flash_Manager_t gFlash_Manager;

/* 函数声明 */
void Pack_Motor_Param(MotorParamPtrs_t *src, Motor_Param_t *dst);
void Unpack_Motor_Param(Motor_Param_t *src, MotorParamPtrs_t *dst);

#endif
