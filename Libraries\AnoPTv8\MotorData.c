#include "MotorData.h"
#include "MotorCmd.h"
#include "MotorParams.h"
#include "AnoPTv8Run.h"
#include "Sys_TimerEvent.h"

// 全局数据实例定义，使用默认值初始化
MotorData_t gMotorData = MOTOR_DATA_DEFAULTS;

/**
 * @brief  发送指定类型的数据帧
 * @param  frame_type: 要发送的帧类型
 * @retval none
 */
void MotorDataSendFrame(FrameType_t frame_type)
{
    uint8_t bytes;

    switch(frame_type) {
        case FRAME_F1:
            bytes = gMotorData.f1.channel_count * 4;
            if(bytes > F1_MAX_BYTES) {
                bytes = F1_MAX_BYTES;
            }
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF1, (uint8_t*)(&gMotorData.f1), bytes);
            break;

        case FRAME_F2:
            bytes = gMotorData.f2.channel_count * 4;
            if(bytes > F2_MAX_BYTES) {
                bytes = F2_MAX_BYTES;
            }
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF2, (uint8_t*)(&gMotorData.f2), bytes);
            break;

        case FRAME_F3:
            bytes = gMotorData.f3.channel_count * 4;
            if(bytes > F3_MAX_BYTES) {
                bytes = F3_MAX_BYTES;
            }
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF3, (uint8_t*)(&gMotorData.f3), bytes);
            break;

        case FRAME_F4:
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF4, (uint8_t*)(&gMotorData.f4), 16);
            break;

        case FRAME_F5:
            bytes = gMotorData.f5.channel_count * 4; // 每通道4字节
            if(bytes > F5_MAX_BYTES) {
                bytes = F5_MAX_BYTES;
            }
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF5, (uint8_t*)(&gMotorData.f5), bytes);
            break;

        case FRAME_F6:
            bytes = gMotorData.f6.channel_count * 2; // 每通道2字节
            if(bytes > F6_MAX_BYTES) {
                bytes = F6_MAX_BYTES;
            }
            AnoPTv8SendBuf(ANOPTV8DEVID_SWJ, 0xF6, (uint8_t*)(&gMotorData.f6), bytes);
            break;
        default:
            break;
    }
}

/**
 * @brief 更新F5&F6帧ADC数据
 * @note  检查使能状态，不使能则跳过处理
 */
void UpdateF5ADCData(void)
{
//    // 检查F5帧是否使能
//    if(gFrameFlags.bits.f5_en) {
//        // F5帧：直接memcpy前15个float值（ADC普通通道结果，按序列排序）
//        memcpy(gMotorData.f5.actual_values, &gADC_Result, ADC_REGULAR_CHANNEL_NUM * 4);
//        // 发送F5帧
//        MotorDataSendFrame(FRAME_F5);
//    }

//    // 检查F6帧是否使能
//    if(gFrameFlags.bits.f6_en) {
//        memcpy(gMotorData.f6.actual_values, gADC_Manager.ADC_value, 2*ADC_REGULAR_CHANNEL_NUM);
//        // 发送F6帧
//      MotorDataSendFrame(FRAME_F6);
//    }
}

/**
 * @brief 更新F2帧传感器数据（高频，1ms调用）
 * @note  直接获取原始值，不进行有效性判断，方便上位机判断故障
 */
void UpdateF2SensorData(void)
{
    // 检查F2帧是否使能
    if(gFrameFlags.bits.f2_en == 0) return;
    gMotorData.f2.channel_count = 5;


    // 发送F2帧
    MotorDataSendFrame(FRAME_F2);
}

/**
 * @brief 更新F3帧温度数据（低频，100ms调用）
 * @note  检查使能状态，不使能则跳过处理，发送由外部控制
 */
void UpdateF3TempData(void)
{
    // 检查F3帧是否使能
    if(gFrameFlags.bits.f3_en == 0) return;
    
//    // F3帧：直接赋值温度相关数据
//    gMotorData.f3.temp_mot1 = gADC_Result.temp_mot1;
//    gMotorData.f3.temp_mot2 = gADC_Result.temp_mot2;
//    gMotorData.f3.temp_cap1 = gADC_Result.temp_cap1;
//    gMotorData.f3.temp_cap2 = gADC_Result.temp_cap2;
//    gMotorData.f3.temp_oll = gADC_Result.temp_oll;
//    gMotorData.f3.temp_bmf_u = gADC_Result.temp_bmf_u;
//    gMotorData.f3.temp_bmf_v = gADC_Result.temp_bmf_v;
//    gMotorData.f3.temp_bmf_w = gADC_Result.temp_bmf_w;
//    gMotorData.f3.temp_PT100_6 = gADC_Result.temp_PT100_6;
//    gMotorData.f3.temp_PT1000_7 = gADC_Result.temp_PT1000_7;
    
    // 发送F3帧
    MotorDataSendFrame(FRAME_F3);
}

/**
 * @brief 更新F1帧FOC数据
 * @note  检查使能状态，不使能则跳过处理，发送由外部控制
 */
void UpdateF1FocData(void)
{
    // 检查F1帧是否使能
    if(gFrameFlags.bits.f1_en == 0) return;
    gMotorData.f1.channel_count = 16;
    gMotorData.f1.i_phase_a = 1.0f;
    gMotorData.f1.i_phase_b = 2.0f;
    MotorDataSendFrame(FRAME_F1);
}

/**
 * @brief F2帧数据测试函数
 * @note  用于测试F2帧数据更新功能
 */
void MotorDataTest(void)
{
    static uint32_t last_test_time = 0;

    // 每2秒测试一次F2帧数据更新
    if(Get_Runtime_Ms() - last_test_time > 2000) {
        last_test_time = Get_Runtime_Ms();

        // 强制使能F2帧并更新数据
        gFrameFlags.bits.f2_en = 1;
        UpdateF2SensorData();
    }
}

