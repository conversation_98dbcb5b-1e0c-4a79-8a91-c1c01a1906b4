/**
 * @file Sys_TimerEvent.h
 * @brief 系统定时器事件驱动头文件
 * @details 提供500us基准的定时器事件驱动功能
 */

#ifndef __SYS_TIMER_EVENT_H
#define __SYS_TIMER_EVENT_H

#include "at32a423.h"

/* 定时标志 */
typedef struct {
    uint8_t flag_500us;    // 500us标志
    uint8_t flag_1ms;      // 1ms标志
    uint8_t flag_1s;       // 1s标志
    uint8_t flag_1min;     // 1min标志
} TimerFlag_t;

/* 定时计数器 */
typedef struct {
    uint16_t cnt_1ms;      // 1ms计数(范围0-1)
    uint16_t cnt_1s;       // 1s计数(范围0-999)
    uint16_t cnt_1min;     // 1min计数(范围0-59)
} TimerCount_t;

/* 任务函数指针类型 */
typedef void (*TimerCallback_t)(void);

/* 任务结构体 */
typedef struct {
    TimerCallback_t callback;   // 回调函数
    uint16_t period;            // 任务周期(以500us为单位)
    uint16_t counter;           // 任务计数器
    uint8_t enable;             // 使能标志
} TimerTask_t;

/* 任务管理结构体 */
typedef struct {
    TimerTask_t* tasks;        // 任务数组指针
    uint16_t taskCount;        // 当前任务数量
    uint16_t maxTasks;         // 最大任务数量
} TimerTaskManager_t;

// 外部声明
extern TimerFlag_t gTimerFlag;
extern volatile uint32_t sys_runtime_ms;

// 函数声明
void TimerEvent_Init(uint16_t maxTasks);
void TimerEvent_DeInit(void);
uint16_t TimerEvent_AddTask(TimerCallback_t callback, uint16_t period);
void TimerEvent_RemoveTask(uint16_t taskId);
void TimerEvent_EnableTask(uint16_t taskId);
void TimerEvent_DisableTask(uint16_t taskId);
void TimerEvent_SetTaskPeriod(uint16_t taskId, uint16_t period);
void TimerEvent_Handler(void);
// uint32_t Get_Runtime_Ms(void);
#define Get_Runtime_Ms() sys_runtime_ms


#endif 