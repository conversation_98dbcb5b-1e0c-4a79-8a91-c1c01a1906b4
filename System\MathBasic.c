// //#include "Sys_GlobalVar.h"
// //================================================
// #include "at32a423.h"                   // Device header
// #include "MathBasic.h"
// #include <math.h>
// #include "Rms_out.h"

// /**
//  * @brief 低通滤波器补偿函数
//  * @param[in] fVarIn 当前输入值
//  * @param[in] fVarInOld 上一次输入值
//  * @param[in] fTCon 时间常数
//  * @return 补偿后的输出值
//  */
// float LPFCompensate(float fVarIn,float fVarInOld,float fTCon)
// {
// 	float Temp1,Temp2,fResultCal;//
// 						   //
// 	Temp1 = 1.0 + fTCon;//
// 					 //
// 	Temp2 = Temp1 * fVarIn - fVarInOld;//
// 									//
// 	fResultCal = Temp2/fTCon;//
// 						  //
// 	return(fResultCal);
// }

// //===================================================
// /**
//  * @brief 低通滤波器函数
//  * @param[in] fLPFVarIn 当前输入值
//  * @param[in] fLPFTCon 时间常数
//  * @param[in] fLPFOutOld 上一次输出值
//  * @return 滤波后的输出值
//  */
// float LPF(float fLPFVarIn,float fLPFTCon,float fLPFOutOld)
// {
// 	float Temp1,Temp2,Temp3,fResultCal;
// 									//
// 	Temp1 = 1.0 + fLPFTCon;//1+Ts/T
// 						//
// 	Temp2 = fLPFTCon * fLPFVarIn;//fLFPTCon = Ts/T
// 							  //
// 	Temp3 = Temp2 + fLPFOutOld;//
// 							//
// 	fResultCal = Temp3 / Temp1;//
// 						  //
// 	return(fResultCal);
        
// }

// /**
//  * @brief 低通滤波器函数(基于截止频率)
//  * @param[in] fLPFVarIn 当前输入值
//  * @param[in] fLPFVarInOld 上一次输入值
//  * @param[in] fLPFVarOutOld 上一次输出值
//  * @param[in] fLPFCutFre 截止频率
//  * @return 滤波后的输出值
//  */
// float LPFKY(float fLPFVarIn,float fLPFVarInOld,float fLPFVarOutOld,float fLPFCutFre)
// {
// 	float Temp1 = 0.0;
// 	float fA0 = 0.0;
// 	float fA1 = 0.0;
// 	float fB0 = 0.0;
// 	float fCalResult = 0.0;
// 	Temp1 = PI2 * fLPFCutFre/PowerCalLPFSampFre;
// 	fA0 = Temp1/(Temp1 + 2.0);
// 	fA1 = fA0;
// 	fB0 = (Temp1 - 2.0)/(Temp1 + 2.0);
// 	fCalResult = fA0 * fLPFVarIn + fA1 * fLPFVarInOld - fB0 * fLPFVarOutOld;
// 	return(fCalResult);
// }

// /**
//  * @brief 三个浮点数取最大值
//  * @param[in] a 输入值1
//  * @param[in] b 输入值2
//  * @param[in] c 输入值3
//  * @return 三个输入值中的最大值
//  */
// float Max(float a,float b,float c)
// {
// 	float temp1;
// 	float z1;
// 	temp1 = a>b? a:b;

// 	z1 = temp1>c? temp1:c;
// 	return(z1);
// }

// /**
//  * @brief 三个浮点数取最小值
//  * @param[in] a 输入值1
//  * @param[in] b 输入值2
//  * @param[in] c 输入值3
//  * @return 三个输入值中的最小值
//  */
// float Min(float a,float b,float c)
// {
// 	float temp2;
// 	float z2;
// 	temp2 = a<b? a:b;
// 	z2 = temp2<c? temp2:c;
// 	return(z2);
// }

// /**
//  * @brief 三个无符号整数取最大值
//  * @param[in] a 输入值1
//  * @param[in] b 输入值2
//  * @param[in] c 输入值3
//  * @return 三个输入值中的最大值
//  */
// uint16_t uMax(uint16_t a,uint16_t b,uint16_t c)
// {
// 	uint16_t Temp1;
// 	uint16_t Temp2;
// 	Temp1 = a > b ? a : b;
// 	Temp2 = Temp1 > c ? Temp1 : c;
// 	return(Temp2);
// }

// /**
//  * @brief 三个无符号整数取最小值
//  * @param[in] a 输入值1
//  * @param[in] b 输入值2
//  * @param[in] c 输入值3
//  * @return 三个输入值中的最小值
//  */
// uint16_t uMin(uint16_t a,uint16_t b,uint16_t c)
// {
// 	uint16_t Temp1;
// 	uint16_t Temp2;
// 	Temp1 = a < b ? a : b;
// 	Temp2 = Temp1 < c ? Temp1 : c;
// 	return(Temp2);
// }

// /**
//  * @brief 三个无符号整数取中间值
//  * @param[in] a 输入值1
//  * @param[in] b 输入值2
//  * @param[in] c 输入值3
//  * @return 三个输入值中的中间值
//  */
// uint16_t uMid(uint16_t a,uint16_t b,uint16_t c)
// {
// 	uint16_t Temp1;
// 	uint16_t Temp2;
// 	uint16_t Temp3;
// 	Temp1 = uMax(a,b,c);
// 	Temp2 = uMin(a,b,c);
// 	Temp3 = a + b + c - Temp1 - Temp2;
// 	return(Temp3);
// }

// /**
//  * @brief 三选一多路选择器
//  * @param[in] SelectSig 选择信号(0-2)
//  * @param[in] fIn0 输入值0
//  * @param[in] fIn1 输入值1
//  * @param[in] fIn2 输入值2
//  * @return 根据选择信号返回对应的输入值
//  */
// float Mux3s1(uint16_t SelectSig,float fIn0,float fIn1,float fIn2)//3选1多路复选器
// {
// 	float fCalResult;
// 	switch(SelectSig)
// 	{
// 		case 0:
// 		{
// 			fCalResult = fIn0;
// 			break;
// 		}
// 		case 1:
// 		{
// 			fCalResult = fIn1;
// 			break;
// 		} 
// 		case 2:
// 		{
// 			fCalResult = fIn2;
// 			break;
// 		}
// 		default:
// 		{
// 			fCalResult = -9999.9999;		// 异常数报错
// 			break;
// 		}
// 	}
// 	return(fCalResult);
// }

// /**
//  * @brief 限幅函数
//  * @param[in] fMinIn 最小限幅值
//  * @param[in] fMaxIn 最大限幅值
//  * @param[in] fDateIn 输入值
//  * @return 限幅后的输出值
//  */
// float Limit(float fMinIn,float fMaxIn,float fDateIn)
// {
// 	float fCalResult;//
// 					//
// 	if(fDateIn > 0.0)
// 	{
// 		if(fDateIn > fMaxIn)//
// 					   //
// 			fCalResult = fMaxIn;//

// 		else if(fDateIn < fMinIn)//
// 							//
// 			fCalResult = fMinIn;
// 		else
// 			fCalResult = fDateIn;
// 	}
// 	else
// 	{
// 		if(fabs(fDateIn) > fMaxIn)//
// 							 //
// 			fCalResult = -fMaxIn;//
// 						  //
// 		else if(fabs(fDateIn) < fMinIn)//
// 								  //
// 			fCalResult = -fMinIn;//
// 		else
// 			fCalResult = fDateIn;
// 	}
// 	return(fCalResult);
// }


// // No more.
// //===========================================================================
