/**********************************************************
 * @file     motor_current_loop.h
 * @brief    高性能电流环控制头文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     基于VESC架构的裸机电流环实现
 *           支持编码器和非线性磁链观测器
 *           不包含高频注入相关实现
***********************************************************/

#ifndef _MOTOR_CURRENT_LOOP_H
#define _MOTOR_CURRENT_LOOP_H

#include "at32a423.h"
#include "arm_math.h"
#include "sysTypeDef.h"
#include "adc_pmsm.h"

/*============================ 宏定义 ============================*/

// 数学常数定义
#define SQRT3_DIV_2         0.866025403784f     // √3/2
#define TWO_DIV_3           0.666666666667f     // 2/3
#define ONE_DIV_SQRT3       0.577350269190f     // 1/√3
#define TWO_DIV_SQRT3       1.154700538379f     // 2/√3
#define PI                  3.141592653590f     // π
#define TWO_PI              6.283185307180f     // 2π
#define PI_DIV_2            1.570796326795f     // π/2
#define SQRT3               1.732050807569f     // √3

// 控制周期定义
#define CURRENT_LOOP_FREQ_HZ    20000.0f        // 电流环频率 20kHz
#define CURRENT_LOOP_DT         0.00005f        // 电流环周期 50us

// 电流环配置参数
#define MAX_DUTY_CYCLE          0.95f           // 最大占空比
#define MIN_DUTY_CYCLE          0.05f           // 最小占空比
#define MAX_CURRENT_A           100.0f          // 最大电流限制(A)
#define MAX_VOLTAGE_V           400.0f          // 最大电压限制(V)

// PI控制器默认参数
#define DEFAULT_CURRENT_KP      0.03f           // 默认比例增益
#define DEFAULT_CURRENT_KI      50.0f           // 默认积分增益
#define DEFAULT_D_GAIN_SCALE    0.8f            // d轴增益缩放因子

/*============================ 枚举定义 ============================*/

/**
 * @brief 电机控制状态枚举
 */
typedef enum {
    MOTOR_STATE_STOP = 0,           // 停止状态
    MOTOR_STATE_RUNNING,            // 运行状态
    MOTOR_STATE_FAULT,              // 故障状态
    MOTOR_STATE_BRAKE               // 制动状态
} motor_state_e;

/**
 * @brief 电机控制模式枚举
 */
typedef enum {
    CONTROL_MODE_CURRENT = 0,       // 电流控制模式
    CONTROL_MODE_SPEED,             // 速度控制模式
    CONTROL_MODE_POSITION,          // 位置控制模式
    CONTROL_MODE_DUTY_CYCLE,        // 占空比控制模式
    CONTROL_MODE_BRAKE              // 制动模式
} control_mode_e;

/**
 * @brief 传感器模式枚举
 */
typedef enum {
    SENSOR_MODE_ENCODER = 0,        // 编码器模式
    SENSOR_MODE_OBSERVER,           // 观测器模式
    SENSOR_MODE_HYBRID              // 混合模式
} sensor_mode_e;

/**
 * @brief 解耦控制模式枚举
 */
typedef enum {
    DECOUPLING_DISABLED = 0,        // 禁用解耦
    DECOUPLING_CROSS,               // 交叉解耦
    DECOUPLING_BEMF,                // 反电动势解耦
    DECOUPLING_CROSS_BEMF           // 交叉+反电动势解耦
} decoupling_mode_e;

/*============================ 结构体定义 ============================*/

/**
 * @brief PI控制器结构体
 */
typedef struct {
    float kp;                       // 比例增益
    float ki;                       // 积分增益
    float integral;                 // 积分累积值
    float max_integral;             // 积分限幅
    float max_output;               // 输出限幅
    float error_prev;               // 上次误差值
    bool_t anti_windup_enabled;     // 积分抗饱和使能
} pi_controller_t;

/**
 * @brief 电机参数结构体
 */
typedef struct {
    // 基本电机参数
    float rs;                       // 定子电阻(Ω)
    float ld;                       // d轴电感(H)
    float lq;                       // q轴电感(H)
    float flux_linkage;             // 永磁体磁链(Wb)
    uint16_t pole_pairs;            // 极对数

    // 电机额定参数
    float rated_current;            // 额定电流(A)
    float rated_voltage;            // 额定电压(V)
    float rated_power;              // 额定功率(W)
    float rated_speed;              // 额定转速(rpm)

    // 温度补偿参数
    float temp_coeff_rs;            // 电阻温度系数(/°C)
    float temp_ref;                 // 参考温度(°C)
} motor_params_t;

/**
 * @brief 角度补偿参数结构体
 */
typedef struct {
    // 编码器补偿
    float encoder_offset;           // 编码器零点偏移(rad)
    float encoder_ratio;            // 编码器传动比
    bool_t encoder_inverted;        // 编码器方向反转

    // 相位补偿
    float phase_advance;            // 相位超前补偿(rad)
    float observer_offset;          // 观测器偏移补偿

    // 开关频率补偿
    bool_t switching_freq_comp;     // 开关频率补偿使能
    float comp_factor;              // 补偿因子
} angle_compensation_t;

/**
 * @brief 电流环配置参数结构体
 */
typedef struct {
    // PI控制器参数
    float current_kp;               // 电流环比例增益
    float current_ki;               // 电流环积分增益
    float d_gain_scale_start;       // d轴增益缩放起始点
    float d_gain_scale_max_mod;     // d轴增益缩放最大调制度

    // 电机参数
    motor_params_t motor;           // 电机参数

    // 角度补偿参数
    angle_compensation_t angle_comp; // 角度补偿参数

    // 限制参数
    float max_current;              // 最大电流(A)
    float max_voltage;              // 最大电压(V)
    float max_duty;                 // 最大占空比

    // 解耦控制
    decoupling_mode_e decoupling_mode;  // 解耦模式
    bool_t temp_comp_enabled;       // 温度补偿使能

    // 传感器配置
    sensor_mode_e sensor_mode;      // 传感器模式
} current_loop_config_t;

/**
 * @brief 电流环状态变量结构体
 */
typedef struct {
    // 三相电流(A)
    float ia, ib, ic;               // 三相瞬时电流
    float ia_filt, ib_filt, ic_filt; // 三相滤波电流
    
    // αβ坐标系电流(A)
    float i_alpha, i_beta;          // αβ轴电流
    float i_alpha_filt, i_beta_filt; // αβ轴滤波电流
    float i_abs;                    // 电流幅值
    float i_abs_filt;               // 滤波电流幅值
    
    // dq坐标系电流(A)
    float id, iq;                   // dq轴电流
    float id_filt, iq_filt;         // dq轴滤波电流
    float id_target, iq_target;     // dq轴目标电流
    
    // αβ坐标系电压(V)
    float v_alpha, v_beta;          // αβ轴电压
    
    // dq坐标系电压(V)
    float vd, vq;                   // dq轴电压
    float vd_int, vq_int;           // dq轴积分项
    
    // 三相电压(V)
    float va, vb, vc;               // 三相电压
    
    // 调制信号
    float mod_alpha, mod_beta;      // αβ轴调制信号
    float mod_d, mod_q;             // dq轴调制信号
    
    // PWM占空比
    float duty_a, duty_b, duty_c;   // 三相PWM占空比
    
    // 角度和速度
    float theta_elec;               // 电角度(rad)
    float omega_elec;               // 电角速度(rad/s)
    float sin_theta, cos_theta;     // 角度的正弦和余弦值
    
    // 母线参数
    float v_bus;                    // 母线电压(V)
    float i_bus;                    // 母线电流(A)
    
    // SVPWM相关
    uint8_t svpwm_sector;           // SVPWM扇区
    float t1, t2, t0;               // SVPWM时间计算
    
    // 解耦控制
    float dec_vd, dec_vq;           // 解耦电压
    float dec_bemf;                 // 反电动势补偿

    // 温度补偿
    float temp_motor;               // 电机温度(°C)
    float ki_temp_comp;             // 温度补偿后的积分增益
    float rs_temp_comp;             // 温度补偿后的电阻

    // 角度相关
    float theta_elec_raw;           // 原始电角度(rad)
    float theta_elec_comp;          // 补偿后电角度(rad)
    float omega_elec_filt;          // 滤波后电角速度(rad/s)

    // 观测器相关(预留给无感算法)
    float observer_x1, observer_x2; // 观测器状态变量
    float observer_phase;           // 观测器相位
    float observer_speed;           // 观测器速度
    bool_t using_encoder;           // 是否使用编码器

} current_loop_state_t;

/**
 * @brief 电流环主控制结构体
 */
typedef struct {
    // 配置和状态
    current_loop_config_t config;   // 配置参数
    current_loop_state_t state;     // 状态变量
    
    // PI控制器
    pi_controller_t pi_id;          // d轴PI控制器
    pi_controller_t pi_iq;          // q轴PI控制器
    
    // 控制模式和状态
    motor_state_e motor_state;      // 电机状态
    control_mode_e control_mode;    // 控制模式
    
    // 使能标志
    bool_t enabled;                 // 电流环使能
    bool_t output_enabled;          // 输出使能
    bool_t fault_flag;              // 故障标志

    // 循环计数器
    uint32_t loop_counter;          // 循环计数器

} current_loop_t;

/*============================ 硬件接口宏定义 ============================*/

// 电流采样接口(使用adc_pmsm模块)
#define GET_CURRENT_A()         ADC_GET_IA_FILTERED()
#define GET_CURRENT_B()         ADC_GET_IB_FILTERED()
#define GET_CURRENT_C()         ADC_GET_IC_FILTERED()

// 母线电压采样接口
#define GET_BUS_VOLTAGE()       Get_Bus_Voltage()

// 电机温度采样接口
#define GET_MOTOR_TEMP()        Get_Motor_Temperature()

// 编码器角度接口
#define GET_ELEC_ANGLE()        GetElectricalAngle_ENC()

// PWM输出接口(需要用户根据硬件实现)
#define SET_PWM_DUTY_A(duty)    /* 用户实现 */
#define SET_PWM_DUTY_B(duty)    /* 用户实现 */
#define SET_PWM_DUTY_C(duty)    /* 用户实现 */

// PWM使能控制
#define ENABLE_PWM_OUTPUT()     /* 用户实现 */
#define DISABLE_PWM_OUTPUT()    /* 用户实现 */

/*============================ 函数声明 ============================*/

// 初始化和配置函数
ret_t current_loop_init(current_loop_t *loop);
ret_t current_loop_config_set(current_loop_t *loop, const current_loop_config_t *config);
ret_t current_loop_reset(current_loop_t *loop);

// 主控制函数
ret_t current_loop_execute(current_loop_t *loop);
ret_t current_loop_enable(current_loop_t *loop, bool_t enable);

// 设定值函数
ret_t current_loop_set_current_dq(current_loop_t *loop, float id_ref, float iq_ref);
ret_t current_loop_set_current_target(current_loop_t *loop, float i_target);

// 状态获取函数
float current_loop_get_id(const current_loop_t *loop);
float current_loop_get_iq(const current_loop_t *loop);
float current_loop_get_vd(const current_loop_t *loop);
float current_loop_get_vq(const current_loop_t *loop);
float current_loop_get_duty_cycle(const current_loop_t *loop);

// 坐标变换函数
void clarke_transform(float ia, float ib, float ic, float *i_alpha, float *i_beta);
void park_transform(float i_alpha, float i_beta, float sin_theta, float cos_theta, float *id, float *iq);
void inverse_park_transform(float vd, float vq, float sin_theta, float cos_theta, float *v_alpha, float *v_beta);

// SVPWM函数
ret_t svpwm_calculate(float v_alpha, float v_beta, float v_bus, float *duty_a, float *duty_b, float *duty_c);

// PI控制器函数
void pi_controller_init(pi_controller_t *pi, float kp, float ki, float max_integral, float max_output);
float pi_controller_update(pi_controller_t *pi, float error, float dt);
void pi_controller_reset(pi_controller_t *pi);

// 角度补偿函数
float angle_compensation_apply(current_loop_t *loop, float raw_angle);
ret_t angle_compensation_set_encoder_offset(current_loop_t *loop, float offset);
ret_t angle_compensation_set_phase_advance(current_loop_t *loop, float advance);

// 电机参数函数
ret_t motor_params_set(current_loop_t *loop, const motor_params_t *params);
ret_t motor_params_get(const current_loop_t *loop, motor_params_t *params);
float motor_params_get_temp_compensated_rs(const current_loop_t *loop);

// 观测器接口函数(预留)
ret_t observer_update(current_loop_t *loop, float dt);
float observer_get_phase(const current_loop_t *loop);
float observer_get_speed(const current_loop_t *loop);

// 工具函数
void limit_value(float *value, float min_val, float max_val);
void normalize_angle(float *angle);
float low_pass_filter(float input, float prev_output, float alpha);
float angle_difference(float angle1, float angle2);

// 全局实例获取函数
current_loop_t* current_loop_get_instance(void);

// 中断处理函数
void motor_current_loop_interrupt_handler(void);

// 快速初始化和控制函数
ret_t motor_current_loop_quick_init(void);
ret_t motor_current_loop_start(float target_current);
ret_t motor_current_loop_stop(void);

#endif /* _MOTOR_CURRENT_LOOP_H */
