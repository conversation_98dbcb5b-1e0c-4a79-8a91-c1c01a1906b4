#ifndef __LBQ_DESIGN_H
#define __LBQ_DESIGN_H

#include <stdint.h>

typedef struct {
    // 数据相关
    uint16_t *buf;        // 数据缓冲区指针
    uint32_t sum;         // 累积和
    uint16_t avg_out;     // 上一次计算的平均值结果

    // 状态控制
    uint16_t head;        // 头指针（下一个要存储的位置）
    uint16_t count;       // 有效数据计数
    uint16_t len;         // 滤波器长度，必须是2的幂
    uint8_t  shift;       // 右移位数，等于log2(len)
    uint8_t  reserved;    // 保留字节用于字节对齐
} MA_Filter_t;

/* 滑动滤波器长度定义 */
#define FILTER_LEN  16 // 所有滑动滤波器统一长度

// typedef struct {
//     // 原有字段
//     uint16_t *buf;        // 数据缓冲区指针
//     uint32_t sum;         // 累积和
//     uint16_t head;        // 头指针
//     uint16_t count;       // 有效数据计数
//     uint16_t len;         // 滤波器长度，必须是2的幂
//     uint8_t shift;        // 右移位数，等于log2(len)
//     // 新增去极值
//     uint16_t max_val;     // 当前窗口最大值
//     uint16_t min_val;     // 当前窗口最小值
//     uint16_t max_idx;     // 最大值索引
//     uint16_t min_idx;     // 最小值索引
// } MA_Filter_t;

/* 函数声明 */
void MA_Filter_Init(MA_Filter_t *filter, uint16_t *buffer, uint16_t length);
uint16_t MA_Filter_Update(MA_Filter_t *filter, uint16_t new_value);
uint16_t MA_Filter_Update_Ultra(MA_Filter_t *filter, uint16_t new_value);
#define IIR_Filter(input, prev_output) ((IIR_ALPHA) * (prev_output) + (1.0f - (IIR_ALPHA)) * (input))
uint16_t Check_Temp_Sensor_Valid(uint16_t adc_value, uint8_t invalid_flag_index);

/* 温度计算函数声明 - 新的线性插值实现 */
float ADC_Get_PT100_Temp(uint16_t adc_value);
float ADC_Get_PT1000_Temp(uint16_t adc_value);

/* PT100温度范围定义 */
#define PT100_TEMP_MIN    -40.0f  // 最低温度
#define PT100_TEMP_MAX    215.0f  // 最高温度
#define PT100_TEMP_STEP   1.0f    // 温度步进

/* PT1000温度范围定义 */
#define PT1000_TEMP_MIN   -40.0f  // 最低温度
#define PT1000_TEMP_MAX   215.0f  // 最高温度
#define PT1000_TEMP_STEP  1.0f    // 温度步进

#define PT100_TABLE_SIZE  256  // 温度表大小(-40°C到215°C)
#define PT1000_TABLE_SIZE 256  // 温度表大小(-40°C到215°C)

// PT100温度对照表(-40°C到215°C)
// static const uint16_t PT100_ADC_Table[PT100_TABLE_SIZE] = {
//     116,  136,  156,  176,  196,  216,  235,  255,  274,  294,     // -40℃ ~ -31℃
//     313,  333,  352,  371,  391,  410,  429,  448,  467,  486,     // -30℃ ~ -21℃
//     505,  524,  543,  562,  580,  599,  617,  636,  654,  672,     // -20℃ ~ -11℃
//     691,  709,  728,  746,  764,  782,  800,  818,  836,  854,     // -10℃ ~ -1℃
//     871,  889,  907,  925,  942,  960,  977,  995,  1012, 1029,    //  0℃ ~  9℃
//     1047, 1064, 1081, 1098, 1116, 1133, 1150, 1167, 1184, 1200,    //  10℃ ~ 19℃
//     1217, 1234, 1251, 1267, 1284, 1300, 1317, 1334, 1350, 1367,    //  20℃ ~ 29℃
//     1383, 1399, 1416, 1431, 1448, 1464, 1480, 1496, 1512, 1528,    //  30℃ ~ 39℃
//     1544, 1560, 1576, 1591, 1607, 1623, 1639, 1654, 1670, 1685,    //  40℃ ~ 49℃
//     1701, 1716, 1732, 1747, 1762, 1777, 1793, 1808, 1823, 1838,    //  50℃ ~ 59℃
//     1853, 1868, 1883, 1898, 1913, 1928, 1943, 1958, 1972, 1987,    //  60℃ ~ 69℃
//     2002, 2016, 2031, 2045, 2060, 2075, 2089, 2103, 2118, 2132,    //  70℃ ~ 79℃
//     2146, 2161, 2175, 2189, 2203, 2217, 2231, 2246, 2260, 2273,    //  80℃ ~ 89℃
//     2287, 2301, 2315, 2329, 2343, 2356, 2370, 2384, 2398, 2411,    //  90℃ ~ 99℃
//     2425, 2438, 2451, 2465, 2478, 2492, 2505, 2519, 2532, 2545,    // 100℃ ~ 109℃
//     2558, 2572, 2585, 2598, 2611, 2624, 2637, 2650, 2663, 2676,    // 110℃ ~ 119℃
//     2689, 2702, 2715, 2728, 2740, 2753, 2766, 2778, 2791, 2804,    // 120℃ ~ 129℃
//     2816, 2829, 2841, 2854, 2866, 2879, 2891, 2904, 2916, 2928,    // 130℃ ~ 139℃
//     2940, 2953, 2965, 2977, 2989, 3002, 3014, 3026, 3038, 3050,    // 140℃ ~ 149℃
//     3062, 3074, 3086, 3098, 3110, 3121, 3133, 3145, 3157, 3168,    // 150℃ ~ 159℃
//     3180, 3192, 3204, 3215, 3227, 3238, 3250, 3262, 3273, 3284,    // 160℃ ~ 169℃
//     3296, 3307, 3319, 3330, 3342, 3353, 3364, 3375, 3387, 3398,    // 170℃ ~ 179℃
//     3409, 3420, 3431, 3442, 3453, 3464, 3475, 3486, 3497, 3508,    // 180℃ ~ 189℃
//     3519, 3530, 3541, 3552, 3563, 3574, 3584, 3595, 3606, 3616,    // 190℃ ~ 199℃
//     3627, 3638, 3648, 3659, 3670, 3680, 3691, 3701, 3711, 3722,    // 200℃ ~ 209℃
//     3733, 3744, 3755, 3766, 3777, 3788                             // 210℃ ~ 215℃
// };
// PT100温度对照表(-40°C到215°C)
static const uint16_t PT100_ADC_Table[PT100_TABLE_SIZE] = {
    22,   26,   30,   34,   38,   42,   46,   50,   53,   57,     // -40℃ ~ -31℃
    61,   65,   69,   73,   77,   81,   84,   88,   92,   96,     // -30℃ ~ -21℃
    100,  104,  108,  112,  115,  119,  123,  127,  131,  135,    // -20℃ ~ -11℃
    139,  142,  146,  150,  154,  158,  162,  166,  169,  173,    // -10℃ ~ -1℃
    177,  181,  185,  189,  192,  196,  200,  204,  208,  212,    //  0℃ ~  9℃
    215,  219,  223,  227,  231,  235,  238,  242,  246,  250,    //  10℃ ~ 19℃
    254,  258,  261,  265,  269,  273,  277,  280,  284,  288,    //  20℃ ~ 29℃
    292,  296,  300,  303,  307,  311,  315,  319,  322,  326,    //  30℃ ~ 39℃
    330,  334,  337,  341,  345,  349,  353,  356,  360,  364,    //  40℃ ~ 49℃
    368,  372,  375,  379,  383,  387,  391,  394,  398,  402,    //  50℃ ~ 59℃
    406,  410,  413,  417,  421,  425,  428,  432,  436,  440,    //  60℃ ~ 69℃
    443,  447,  451,  455,  458,  462,  466,  470,  473,  477,    //  70℃ ~ 79℃
    481,  485,  488,  492,  496,  500,  503,  507,  511,  515,    //  80℃ ~ 89℃
    518,  522,  526,  530,  533,  537,  541,  545,  548,  552,    //  90℃ ~ 99℃
    556,  560,  563,  567,  571,  574,  578,  582,  586,  589,    // 100℃ ~ 109℃
    593,  597,  601,  604,  608,  612,  615,  619,  623,  626,    // 110℃ ~ 119℃
    630,  634,  638,  641,  645,  649,  652,  656,  660,  664,    // 120℃ ~ 129℃
    667,  671,  675,  678,  682,  686,  689,  693,  697,  700,    // 130℃ ~ 139℃
    704,  708,  711,  715,  719,  723,  726,  730,  734,  737,    // 140℃ ~ 149℃
    741,  745,  748,  752,  756,  759,  763,  767,  770,  774,    // 150℃ ~ 159℃
    778,  781,  785,  789,  792,  796,  800,  803,  807,  811,    // 160℃ ~ 169℃
    814,  818,  821,  825,  829,  832,  836,  840,  843,  847,    // 170℃ ~ 179℃
    851,  854,  858,  862,  865,  869,  873,  876,  880,  883,    // 180℃ ~ 189℃
    887,  891,  894,  898,  902,  905,  909,  912,  916,  920,    // 190℃ ~ 199℃
    923,  927,  930,  934,  938,  941,  945,  949,  952,  956,    // 200℃ ~ 209℃
    959,  963,  967,  970,  974,  977                             // 210℃ ~ 215℃
};




// PT1000温度对照表(-40°C到215°C)
static const uint16_t PT1000_ADC_Table[PT1000_TABLE_SIZE] = {
    116,  136,  156,  176,  196,  216,  235,  255,  275,  294,     // -40℃ ~ -31℃
    314,  333,  352,  372,  391,  410,  429,  448,  467,  486,     // -30℃ ~ -21℃
    505,  524,  543,  561,  580,  599,  617,  636,  654,  673,     // -20℃ ~ -11℃
    691,  709,  728,  746,  764,  782,  800,  818,  836,  854,     // -10℃ ~ -1℃
    871,  889,  907,  925,  942,  960,  977,  995, 1012, 1030,     //   0℃ ~  9℃
    1047, 1064, 1081, 1098, 1116, 1133, 1150, 1167, 1184, 1200,    //  10℃ ~ 19℃
    1217, 1234, 1251, 1267, 1284, 1301, 1317, 1334, 1350, 1366,    //  20℃ ~ 29℃
    1383, 1399, 1415, 1432, 1448, 1464, 1480, 1496, 1512, 1528,    //  30℃ ~ 39℃
    1544, 1560, 1576, 1591, 1607, 1623, 1638, 1654, 1670, 1685,    //  40℃ ~ 49℃
    1701, 1716, 1731, 1747, 1762, 1777, 1793, 1808, 1823, 1838,    //  50℃ ~ 59℃
    1853, 1868, 1883, 1898, 1913, 1928, 1943, 1958, 1972, 1987,    //  60℃ ~ 69℃
    2002, 2016, 2031, 2045, 2060, 2074, 2089, 2103, 2118, 2132,    //  70℃ ~ 79℃
    2146, 2161, 2175, 2189, 2203, 2217, 2231, 2245, 2259, 2273,    //  80℃ ~ 89℃
    2287, 2301, 2315, 2329, 2343, 2356, 2370, 2384, 2397, 2411,    //  90℃ ~ 99℃
    2425, 2438, 2452, 2465, 2479, 2492, 2505, 2519, 2532, 2545,    // 100℃ ~ 109℃
    2558, 2572, 2585, 2598, 2611, 2624, 2637, 2650, 2663, 2676,    // 110℃ ~ 119℃
    2689, 2702, 2715, 2728, 2740, 2753, 2766, 2778, 2791, 2804,    // 120℃ ~ 129℃
    2816, 2829, 2841, 2854, 2866, 2879, 2891, 2904, 2916, 2928,    // 130℃ ~ 139℃
    2941, 2953, 2965, 2977, 2989, 3002, 3014, 3026, 3038, 3050,    // 140℃ ~ 149℃
    3062, 3074, 3086, 3098, 3110, 3121, 3133, 3145, 3157, 3169,    // 150℃ ~ 159℃
    3180, 3192, 3204, 3215, 3227, 3238, 3250, 3261, 3273, 3284,    // 160℃ ~ 169℃
    3296, 3307, 3319, 3330, 3341, 3353, 3364, 3375, 3386, 3398,    // 170℃ ~ 179℃
    3409, 3420, 3431, 3442, 3453, 3464, 3475, 3486, 3497, 3508,    // 180℃ ~ 189℃
    3519, 3530, 3541, 3552, 3563, 3573, 3584, 3595, 3606, 3616,    // 190℃ ~ 199℃
    3627, 3638, 3648, 3659, 3669, 3680, 3691, 3701, 3712, 3722,    // 200℃ ~ 209℃
    3733, 3744, 3755, 3766, 3777, 3788                             // 210℃ ~ 215℃
};

#endif
