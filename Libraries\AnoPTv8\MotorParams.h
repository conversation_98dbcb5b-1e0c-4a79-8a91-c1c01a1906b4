#ifndef __MOTOR_PARAMS_H
#define __MOTOR_PARAMS_H

// 定义参数数量
#define MOTOR_PARAM_NUM 59

// 参数数组声明
extern float gMotorParams[MOTOR_PARAM_NUM];

//参数指针宏定义，用于外部访问
#define pCsvParameConTc              (gMotorParams+0)     // 采样时间s
#define pCsvParamGiveUdc             (gMotorParams+1)     // 母线电压Udc
#define pCsvParamMotorRatedVoltage   (gMotorParams+2)     // 电机额定电压
#define pCsvParamMotorRatedCurrent   (gMotorParams+3)     // 电机额定电流
#define pCsvParamMotorRatedFre       (gMotorParams+4)     // 电机额定频率
#define pCsvParamMotorRatedPower     (gMotorParams+5)     // 电机额定功率
#define pCsvParamMotorRateSpeed      (gMotorParams+6)     // 电机额定转速
#define pCsvParamMotorPoleNum        (gMotorParams+7)     // 电机极对数
#define pCsvParamMotorRatedSlip      (gMotorParams+8)     // 电机额定滑差
#define pCsvParamSynRs               (gMotorParams+9)     // 定子电阻
#define pCsvParamSynLsd              (gMotorParams+10)    // d轴电感
#define pCsvParamSynLsq              (gMotorParams+11)    // q轴电感
#define pCsvParamSynFlux             (gMotorParams+12)    // 电机磁链

// 低速区域参数
#define pCsvParamSynVcSpeedKi        (gMotorParams+13)    // 低速转速环积分增益
#define pCsvParamSynVcSpeedKp        (gMotorParams+14)    // 低速转速环比例增益
#define pCsvParamSynVcCurrentDKi     (gMotorParams+15)    // 低速d轴电流环积分增益
#define pCsvParamSynVcCurrentDKp     (gMotorParams+16)    // 低速d轴电流环比例增益
#define pCsvParamSynVcCurrentQKi     (gMotorParams+17)    // 低速q轴电流环积分增益
#define pCsvParamSynVcCurrentQKp     (gMotorParams+18)    // 低速q轴电流环比例增益

// 中速区域参数
#define pCsvParamSynVcSpeedKi1       (gMotorParams+19)    // 中速转速环积分增益
#define pCsvParamSynVcSpeedKp1       (gMotorParams+20)    // 中速转速环比例增益
#define pCsvParamSynVcCurrentDKi1    (gMotorParams+21)    // 中速d轴电流环积分增益
#define pCsvParamSynVcCurrentDKp1    (gMotorParams+22)    // 中速d轴电流环比例增益
#define pCsvParamSynVcCurrentQKi1    (gMotorParams+23)    // 中速q轴电流环积分增益
#define pCsvParamSynVcCurrentQKp1    (gMotorParams+24)    // 中速q轴电流环比例增益

// 高速区域参数
#define pCsvParamSynVcSpeedKi2       (gMotorParams+25)    // 高速转速环积分增益
#define pCsvParamSynVcSpeedKp2       (gMotorParams+26)    // 高速转速环比例增益
#define pCsvParamSynVcCurrentDKi2    (gMotorParams+27)    // 高速d轴电流环积分增益
#define pCsvParamSynVcCurrentDKp2    (gMotorParams+28)    // 高速d轴电流环比例增益
#define pCsvParamSynVcCurrentQKi2    (gMotorParams+29)    // 高速q轴电流环积分增益
#define pCsvParamSynVcCurrentQKp2    (gMotorParams+30)    // 高速q轴电流环比例增益

// 磁链PI参数
#define pCsvParamSynVcFWKp           (gMotorParams+31)    // 磁链PI比例增益
#define pCsvParamSynVcFWKi           (gMotorParams+32)    // 磁链PI积分增益

// 其他参数
#define pCsvParamSynVcLoseSpeedValue (gMotorParams+33)    // 失速速度值
#define pCsvParamfluxidmin           (gMotorParams+34)    // 磁链输出最小限幅
#define pCsvParamDBT                 (gMotorParams+35)    // 死区时间us
#define pCsvParamDBEN                (gMotorParams+36)    // 死区补偿使能
#define pCsvParamPosCompEN           (gMotorParams+37)    // 正补偿使能
#define pCsvParamfeedforwordFlag     (gMotorParams+38)    // 前馈补偿标志
#define pCsvParaConRunType           (gMotorParams+39)    // 变频器控制方式
#define pCsvParaConStrategy          (gMotorParams+40)    // 控制策略
#define pCsvParaVFDLoadType          (gMotorParams+41)    // 负载类型
#define pCsvParamSpeed_mid           (gMotorParams+42)    // 分段PI，中速定义
#define pCsvParamSpeed_high          (gMotorParams+43)    // 分段PI，高速定义
#define pCsvParammarslpfw            (gMotorParams+44)    // LPF滤波系数
#define pCsvParamSynVcUqmax          (gMotorParams+45)    // 电流PI输出限幅
#define pCsvParamSynVcIqmax          (gMotorParams+46)    // 速度PI输出限幅
#define pCsvParamCurrentLimit        (gMotorParams+47)    // 电流限幅  
#define pCsvParamPreid_ref           (gMotorParams+48)    // 预定位电流(A)  
#define pCsvParamId_ref              (gMotorParams+49)    // 给定id电流(A) 
#define pCsvParamIq_ref              (gMotorParams+50)    // 给定iq电流(A)
#define pCsvParamSpeed_ref           (gMotorParams+51)    // 给定速度(rmp) 
#define pCsvControlLoopMode          (gMotorParams+52)    // 控制环路模式
#define pCsvParamOpenLoopFreq        (gMotorParams+53)    // 开环频率设定值(Hz)
#define pCsvParamOpenLoopFreqStep    (gMotorParams+54)    // 频率步长增量(Hz/s)  
#define pCsvParamOpenLoopFreqMax     (gMotorParams+55)    // 最大频率限制(Hz)
#define pCsvParamOpenLoopAngleCoef   (gMotorParams+56)    // 开环角度增量系数
#define pCsvParamPrePositionEN       (gMotorParams+57)    // 预定位使能
#define pCsvParamSetAngle            (gMotorParams+58)    // 设置固定角度

// 电流环兼容参数 - 保留向后兼容
#define pCsvParamSynVcCurrentKi     pCsvParamSynVcCurrentDKi     // 低速电流环积分增益(兼容)
#define pCsvParamSynVcCurrentKp     pCsvParamSynVcCurrentDKp     // 低速电流环比例增益(兼容)
#define pCsvParamSynVcCurrentKi1    pCsvParamSynVcCurrentDKi1    // 中速电流环积分增益(兼容)
#define pCsvParamSynVcCurrentKp1    pCsvParamSynVcCurrentDKp1    // 中速电流环比例增益(兼容
#define pCsvParamSynVcCurrentKi2    pCsvParamSynVcCurrentDKi2    // 高速电流环积分增益(兼容)
#define pCsvParamSynVcCurrentKp2    pCsvParamSynVcCurrentDKp2    // 高速电流环比例增益(兼容)


// 参数初始化函数
void MotorParamsInit(void);

#endif 
