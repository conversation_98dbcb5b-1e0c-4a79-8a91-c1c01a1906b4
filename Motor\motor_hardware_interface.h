/**********************************************************
 * @file     motor_hardware_interface.h
 * @brief    电机控制硬件接口适配头文件
 * <AUTHOR> Assistant
 * @date     2025-08-05
 * @version  V1.0.0
 * @note     定义硬件相关的宏和接口函数
 *           用户需要根据具体硬件平台实现这些接口
***********************************************************/

#ifndef _MOTOR_HARDWARE_INTERFACE_H
#define _MOTOR_HARDWARE_INTERFACE_H

#include "at32a423.h"
#include "sysTypeDef.h"

/*============================ PWM相关宏定义 ============================*/

// PWM定时器相关定义(用户需要根据实际硬件修改)
#define PWM_TIMER               TMR1                    // PWM定时器
#define PWM_TIMER_FREQ_HZ       20000                   // PWM频率 20kHz
#define PWM_TIMER_PERIOD        (SystemCoreClock / PWM_TIMER_FREQ_HZ / 2)  // PWM周期

// PWM通道定义
#define PWM_CHANNEL_A           TMR_SELECT_CHANNEL_1    // A相PWM通道
#define PWM_CHANNEL_B           TMR_SELECT_CHANNEL_2    // B相PWM通道  
#define PWM_CHANNEL_C           TMR_SELECT_CHANNEL_3    // C相PWM通道

// PWM引脚定义(用户需要根据实际硬件修改)
#define PWM_A_GPIO_PORT         GPIOA
#define PWM_A_GPIO_PIN          GPIO_PINS_8
#define PWM_A_GPIO_MUX          GPIO_MUX_1

#define PWM_B_GPIO_PORT         GPIOA  
#define PWM_B_GPIO_PIN          GPIO_PINS_9
#define PWM_B_GPIO_MUX          GPIO_MUX_1

#define PWM_C_GPIO_PORT         GPIOA
#define PWM_C_GPIO_PIN          GPIO_PINS_10
#define PWM_C_GPIO_MUX          GPIO_MUX_1

// 互补PWM引脚定义
#define PWM_AN_GPIO_PORT        GPIOB
#define PWM_AN_GPIO_PIN         GPIO_PINS_13
#define PWM_AN_GPIO_MUX         GPIO_MUX_1

#define PWM_BN_GPIO_PORT        GPIOB
#define PWM_BN_GPIO_PIN         GPIO_PINS_14  
#define PWM_BN_GPIO_MUX         GPIO_MUX_1

#define PWM_CN_GPIO_PORT        GPIOB
#define PWM_CN_GPIO_PIN         GPIO_PINS_15
#define PWM_CN_GPIO_MUX         GPIO_MUX_1

// 死区时间定义(ns)
#define PWM_DEAD_TIME_NS        1000                    // 1μs死区时间

/*============================ ADC相关宏定义 ============================*/

// ADC定时器和通道定义(用户需要根据实际硬件修改)
#define CURRENT_ADC             ADC1                    // 电流采样ADC
#define VOLTAGE_ADC             ADC2                    // 电压采样ADC

// 电流采样通道定义
#define ADC_CHANNEL_IA          ADC_CHANNEL_1           // A相电流采样通道
#define ADC_CHANNEL_IB          ADC_CHANNEL_2           // B相电流采样通道
#define ADC_CHANNEL_IC          ADC_CHANNEL_3           // C相电流采样通道

// 电压采样通道定义
#define ADC_CHANNEL_VBUS        ADC_CHANNEL_4           // 母线电压采样通道
#define ADC_CHANNEL_TEMP        ADC_CHANNEL_5           // 温度采样通道

// ADC采样精度和参考电压
#define ADC_RESOLUTION          4096.0f                 // 12位ADC分辨率
#define ADC_VREF                3.3f                    // ADC参考电压(V)

// 电流采样参数
#define CURRENT_SENSE_GAIN      20.0f                   // 电流传感器增益(V/A)
#define CURRENT_SENSE_OFFSET    (ADC_VREF / 2.0f)       // 电流传感器零点偏移(V)

// 电压采样参数  
#define VOLTAGE_SENSE_RATIO     0.01f                   // 电压分压比(实际电压/采样电压)

/*============================ 编码器相关宏定义 ============================*/

// 编码器接口定义(用户需要根据实际硬件修改)
#define ENCODER_SPI             SPI1                    // 编码器SPI接口
#define ENCODER_CS_PORT         GPIOA                   // 编码器CS引脚端口
#define ENCODER_CS_PIN          GPIO_PINS_4             // 编码器CS引脚

// 编码器分辨率
#define ENCODER_RESOLUTION      16384                   // 编码器分辨率(14位)
#define ENCODER_ANGLE_SCALE     (TWO_PI / ENCODER_RESOLUTION)  // 角度缩放因子

/*============================ 保护相关宏定义 ============================*/

// 过流保护阈值
#define OVERCURRENT_THRESHOLD   80.0f                   // 过流保护阈值(A)
#define OVERCURRENT_DELAY_MS    10                      // 过流保护延时(ms)

// 过压保护阈值
#define OVERVOLTAGE_THRESHOLD   450.0f                  // 过压保护阈值(V)
#define UNDERVOLTAGE_THRESHOLD  100.0f                  // 欠压保护阈值(V)

// 过温保护阈值
#define OVERTEMP_THRESHOLD      85.0f                   // 过温保护阈值(°C)

/*============================ 硬件接口函数声明 ============================*/

// PWM相关函数
ret_t motor_pwm_init(void);
ret_t motor_pwm_enable(bool_t enable);
ret_t motor_pwm_set_duty(float duty_a, float duty_b, float duty_c);
ret_t motor_pwm_emergency_stop(void);

// ADC相关函数
ret_t motor_adc_init(void);
ret_t motor_adc_start_conversion(void);
bool_t motor_adc_is_conversion_done(void);
ret_t motor_adc_get_currents(float *ia, float *ib, float *ic);
ret_t motor_adc_get_bus_voltage(float *vbus);
ret_t motor_adc_get_temperature(float *temp);

// 编码器相关函数
ret_t motor_encoder_init(void);
ret_t motor_encoder_read_angle(float *angle);
ret_t motor_encoder_get_speed(float *speed);

// 保护相关函数
ret_t motor_protection_init(void);
bool_t motor_protection_check_overcurrent(float current);
bool_t motor_protection_check_overvoltage(float voltage);
bool_t motor_protection_check_overtemp(float temperature);
ret_t motor_protection_trigger_fault(uint32_t fault_code);

// 中断相关函数
ret_t motor_interrupt_init(void);
ret_t motor_interrupt_enable(bool_t enable);

/*============================ 硬件接口宏实现 ============================*/

// 电流采样接口实现
#define MOTOR_GET_CURRENT_A()   motor_adc_get_current_a()
#define MOTOR_GET_CURRENT_B()   motor_adc_get_current_b()  
#define MOTOR_GET_CURRENT_C()   motor_adc_get_current_c()

// 母线电压采样接口实现
#define MOTOR_GET_BUS_VOLTAGE() motor_adc_get_bus_voltage_direct()

// 电机温度采样接口实现
#define MOTOR_GET_MOTOR_TEMP()  motor_adc_get_temperature_direct()

// 编码器角度接口实现
#define MOTOR_GET_ELEC_ANGLE()  motor_encoder_get_electrical_angle()

// PWM输出接口实现
#define MOTOR_SET_PWM_DUTY_A(duty)  motor_pwm_set_duty_a(duty)
#define MOTOR_SET_PWM_DUTY_B(duty)  motor_pwm_set_duty_b(duty)
#define MOTOR_SET_PWM_DUTY_C(duty)  motor_pwm_set_duty_c(duty)

// PWM使能控制实现
#define MOTOR_ENABLE_PWM_OUTPUT()   motor_pwm_enable(TRUE)
#define MOTOR_DISABLE_PWM_OUTPUT()  motor_pwm_enable(FALSE)

// 故障保护接口实现
#define MOTOR_TRIGGER_EMERGENCY_STOP()  motor_pwm_emergency_stop()

/*============================ 内联函数实现 ============================*/

/**
 * @brief 获取A相电流(内联函数)
 * @return A相电流(A)
 */
static inline float motor_adc_get_current_a(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return Sensor_Drive_Get_U_Current();
}

/**
 * @brief 获取B相电流(内联函数)  
 * @return B相电流(A)
 */
static inline float motor_adc_get_current_b(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return Sensor_Drive_Get_V_Current();
}

/**
 * @brief 获取C相电流(内联函数)
 * @return C相电流(A)
 */
static inline float motor_adc_get_current_c(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return Sensor_Drive_Get_W_Current();
}

/**
 * @brief 获取母线电压(内联函数)
 * @return 母线电压(V)
 */
static inline float motor_adc_get_bus_voltage_direct(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return Get_Bus_Voltage();
}

/**
 * @brief 获取电机温度(内联函数)
 * @return 电机温度(°C)
 */
static inline float motor_adc_get_temperature_direct(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return Get_Motor_Temperature();
}

/**
 * @brief 获取电角度(内联函数)
 * @return 电角度(rad)
 */
static inline float motor_encoder_get_electrical_angle(void)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    return GetElectricalAngle_ENC();
}

/**
 * @brief 设置A相PWM占空比(内联函数)
 * @param duty 占空比(0.0-1.0)
 */
static inline void motor_pwm_set_duty_a(float duty)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    uint32_t compare_value = (uint32_t)(duty * PWM_TIMER_PERIOD);
    tmr_channel_value_set(PWM_TIMER, PWM_CHANNEL_A, compare_value);
}

/**
 * @brief 设置B相PWM占空比(内联函数)
 * @param duty 占空比(0.0-1.0)
 */
static inline void motor_pwm_set_duty_b(float duty)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    uint32_t compare_value = (uint32_t)(duty * PWM_TIMER_PERIOD);
    tmr_channel_value_set(PWM_TIMER, PWM_CHANNEL_B, compare_value);
}

/**
 * @brief 设置C相PWM占空比(内联函数)
 * @param duty 占空比(0.0-1.0)
 */
static inline void motor_pwm_set_duty_c(float duty)
{
    // 用户需要根据实际硬件实现
    // 示例实现:
    uint32_t compare_value = (uint32_t)(duty * PWM_TIMER_PERIOD);
    tmr_channel_value_set(PWM_TIMER, PWM_CHANNEL_C, compare_value);
}

#endif /* _MOTOR_HARDWARE_INTERFACE_H */
