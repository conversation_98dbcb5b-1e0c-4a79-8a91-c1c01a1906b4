/**********************************************************
 * @file     Sys_Can.c
 * @brief    系统CAN通信功能实现
 * <AUTHOR>
 * @date     2025-03-12
 * @version  V1.0.0
 *
 * 设计思路与功能:
 * ----------------------------------------------------------
 * 本模块实现了基于双冗余CAN接口的航空电机控制通信系统，主要功能包括：
 * 1. 命令接收：实时接收飞管转速控制指令并执行
 * 2. 状态反馈：周期性上报电机运行参数和工作状态
 * 3. 自动切换：支持在通信超时时自动切换CAN接口
 * 4. 错误检测：包含丢包检测、校验和超时监控机制
 * 
 * 技术实现:
 * ----------------------------------------------------------
 * 1. 双缓冲接收：使用接收与处理双缓冲区提高实时性能和吞吐量，减少堵塞
 * 2. 多帧通信：实现三个数据帧分时传输电机参数
 * 3. 安全校验：应用累加和校验(LRC)确保数据完整性
 * 4. 冗余切换：在通信故障时自动切换至备用CAN接口
 * 5. 状态位图：用位域结构高效管理设备状态信息
 * 
 * 内存分配设计:
 * ----------------------------------------------------------
 * 1. 通信缓冲区: 
 *    - 接收双缓冲区: 两个12字节缓冲区用于无阻塞接收
 *    - 发送数据区: 三个8字节帧缓冲区分别存储不同类型数据
 * 
 * 2. 状态管理区:
 *    - 系统状态位图: 使用4个字节存储32种运行状态标志
 *    - 通信状态结构: 记录通信接口、错误统计和切换控制信息
 * 
 * 3. 帧格式设计:
 *    - 帧1: 电机转矩、转速和电压数据(8字节)
 *    - 帧2: 电流和温度数据(8字节)
 *    - 帧3: 控制器和系统状态(8字节)
 * 
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 通信中断处理须确保时效性，避免长时间阻塞
 * 2. 校验和计算采用8位累加和取反加一方式(LRC)
 * 3. 自动切换功能可能导致短暂通信中断，关键控制须容错
 * 4. 状态位管理须与整机状态保持同步，确保反馈准确性
 **********************************************************/

#include "at32a423.h"                   
#include "Sys_Can.h"
#include "SysFSM.h"
#include "Sensor_Drive.h"
#include "MotorParams.h"
#include <string.h>

/* 全局变量定义 */
 CanRxDblBuffer_TypeDef can_rx_buffer = {0};       // CAN接收双缓冲
 CanComStatus_TypeDef can_status = CanComStatus_DEFAULTS;            // CAN通信状态

/* 发送模式说明:
 * CAN_TX_MODE  0 模式1: 每20ms发送一个包，循环发送，包1到下一个包1间隔60ms
 * CAN_TX_MODE  1 模式2: 在20ms内分散发送三个包，包1到下一个包1间隔20ms
 */
#define CAN_TX_MODE  1  /* 当前使用模式2 */

/* 临时实现的函数声明 */
uint8_t Sys_SetWarning(uint32_t warn, uint8_t level) { return 0; }         // 系统告警设置函数
uint8_t Is_Controller_Ready(void) { return 0; }                            // 控制器就绪状态检查
uint8_t Is_Rotor_Locked(void) { return 0; }                                // 转子锁定状态检查
uint8_t Is_Motor_Running(void) { return 0; }                               // 电机运行状态检查
uint8_t Is_Controller_Fault(void) { return 0; }                            // 控制器故障状态检查
uint8_t Is_Bus_Overvoltage(void) { return 0; }                             // 母线过压检查
uint8_t Is_Bus_Undervoltage(void) { return 0; }                            // 母线欠压检查
uint8_t Is_V28B_Overvoltage(void) { return 0; }                            // 28V备用电源过压检查
uint8_t Is_V28B_Undervoltage(void) { return 0; }                           // 28V备用电源欠压检查
uint8_t Is_V28M_Overvoltage(void) { return 0; }                            // 28V主电源过压检查
uint8_t Is_V28M_Undervoltage(void) { return 0; }                           // 28V主电源欠压检查
uint8_t Is_Position_Sensor_Fault(void) { return 0; }                       // 位置传感器故障检查
uint8_t Is_HW_Overcurrent(void) { return 0; }                              // 硬件过流检查
uint8_t Is_HW_Selftest_Fault(void) { return 0; }                           // 硬件自检故障检查
uint8_t Is_Speed_Track_Fault(void) { return 0; }                           // 转速跟踪故障检查
uint8_t Is_SW_Overcurrent(void) { return 0; }                              // 软件过流检查
uint8_t Is_Motor_Temp_Warning(void) { return 0; }                          // 电机温度预警检查
uint8_t Is_Motor_Temp_Fault(void) { return 0; }                            // 电机温度故障检查
uint8_t Is_Controller_Temp_Warning(void) { return 0; }                     // 控制器温度预警检查
uint8_t Is_Motor_Overspeed_Warning(void) { return 0; }                     // 电机超速预警检查
uint8_t Is_Motor_Overspeed_Fault(void) { return 0; }                       // 电机超速故障检查
uint8_t Get_Motor_State(void) { return 0; }                                // 获取电机状态
uint8_t Get_System_Fault(void) { return 0; }                               // 获取系统故障


uint8_t Set_Target_Speed(int16_t speed) { return 0; }


/* 全局状态字变量定义 */
CanSystemStatus_TypeDef can_system_status;

/**
  * @brief  CAN接口初始化
  * @param  None
  * @retval None
  */
void CAN1_CAN2_Init(void)
{
    // 初始化GPIO和CAN基础参数
    gpio_init_type gpio_init_struct;
    can_base_type can_base_struct;
    can_baudrate_type can_baudrate_struct;
    can_filter_init_type can_filter_init_struct;
    
    // 初始化接收缓冲区
    memset(&can_rx_buffer, 0, sizeof(CanRxDblBuffer_TypeDef));
    
    // 初始化双缓冲指针
    can_rx_buffer.receiving_buffer = &can_rx_buffer.buffer_a;
    can_rx_buffer.processing_buffer = &can_rx_buffer.buffer_b;
    
    can_rx_buffer.last_rx_time = Get_Runtime_Ms();
    can_rx_buffer.buffer_ready = 0;
    
    /* GPIO配置 - CAN1 */
    gpio_default_para_init(&gpio_init_struct);
    
    // 配置CAN1的TX引脚(PB9)
    gpio_pin_mux_config(GPIOB, GPIO_PINS_SOURCE9, GPIO_MUX_9);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_MODERATE;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_9;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOB, &gpio_init_struct);
    
    // 配置CAN1的RX引脚(PB8)
    gpio_pin_mux_config(GPIOB, GPIO_PINS_SOURCE8, GPIO_MUX_9);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_MODERATE;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_8;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOB, &gpio_init_struct);
    
    /* GPIO配置 - CAN2 */
    // 配置CAN2的TX引脚(PB6)
    gpio_pin_mux_config(GPIOB, GPIO_PINS_SOURCE6, GPIO_MUX_9);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_MODERATE;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_6;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOB, &gpio_init_struct);
    
    // 配置CAN2的RX引脚(PB5)
    gpio_pin_mux_config(GPIOB, GPIO_PINS_SOURCE5, GPIO_MUX_9);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_MODERATE;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_5;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOB, &gpio_init_struct);
    
    /* CAN1基本参数配置 */
    can_default_para_init(&can_base_struct);
    can_base_struct.mode_selection = CAN_MODE_COMMUNICATE;      // 正常通信模式
    can_base_struct.ttc_enable = FALSE;                         // 禁用时间触发通信
    can_base_struct.aebo_enable = TRUE;                         // 使能自动退出总线关闭管理
    can_base_struct.aed_enable = TRUE;                          // 使能自动退出休眠模式
    can_base_struct.prsf_enable = FALSE;                        // 发送失败时允许重传
    can_base_struct.mdrsel_selection = CAN_DISCARDING_FIRST_RECEIVED; // 溢出时丢弃最先接收的
    can_base_struct.mmssr_selection = CAN_SENDING_BY_REQUEST;   // 按请求顺序发送
    
    can_base_init(CAN1, &can_base_struct);
    
    /* CAN2基本参数配置 */
    can_base_init(CAN2, &can_base_struct);
    
    /* CAN波特率设置 - 500kbps */
    /* 波特率计算: baudrate = fpclk/(baudrate_div *(1 + bts1_size + bts2_size)) */
    /* 假设fpclk=120MHz，配置为500kbps */
    can_baudrate_default_para_init(&can_baudrate_struct);
    can_baudrate_struct.baudrate_div = 15;                       /*value: 1~0xFFF*/
    can_baudrate_struct.rsaw_size = CAN_RSAW_1TQ;                /*value: 1~4*/
    can_baudrate_struct.bts1_size = CAN_BTS1_8TQ;                /*value: 1~16*/
    can_baudrate_struct.bts2_size = CAN_BTS2_1TQ;                /*value: 1~8*/
    
    can_baudrate_set(CAN1, &can_baudrate_struct);
    can_baudrate_set(CAN2, &can_baudrate_struct);
    
    /* 配置过滤器接收电机控制指令 */
    can_filter_default_para_init(&can_filter_init_struct);
    /* 构建标识符和掩码 */
    /* 仲裁段格式(D28-D0): 
       - 发送设备(D28-D25): 0x01 (飞管)
       - 接收设备(D24-D21): 0x03 (电机控制器1)
       - 状态(D20-D18): 0x01 (单帧数据)
       - 指令(D17-D12): 0x02 (电机控制器控制指令)
       - 帧计数(D11-D0): 0x000 (任意)
    */
    // 组合成29位标准标识符
    uint32_t filter_id = CAN_RX_FRAME_ID;
    
    // 掩码配置: 只关注高20位，低12位(帧计数)忽略
    uint32_t filter_mask = CAN_ID_MASK;
    
    /* CAN1过滤器0配置 */
   can_filter_init_struct.filter_activate_enable = TRUE;
   can_filter_init_struct.filter_number = 0;
   can_filter_init_struct.filter_fifo = CAN_FILTER_FIFO0;
   can_filter_init_struct.filter_bit = CAN_FILTER_32BIT;
   can_filter_init_struct.filter_mode = CAN_FILTER_MODE_ID_MASK;

    // can_filter_init_struct.filter_id_high = (((0x02642000 << 3) >> 16) & 0xFFFF);
    // can_filter_init_struct.filter_id_low = ((0x02642000 << 3) & 0xFFFF) | 0x04;
    // can_filter_init_struct.filter_mask_high = ((0x01FFF000 << 3) >> 16) & 0xFFFF;
    // can_filter_init_struct.filter_mask_low = ((0x01FFF000 << 3) & 0xFFFF) | 0x04 | 0x02;
    can_filter_init_struct.filter_id_high = ((filter_id << 3) >> 16) & 0xFFFF;
    can_filter_init_struct.filter_id_low = (filter_id << 3) & 0xFFFF ;
    can_filter_init_struct.filter_mask_high = ((filter_mask << 3) >> 16) & 0xFFFF;
    can_filter_init_struct.filter_mask_low = (filter_mask << 3) & 0xFFFF ;
    
    can_filter_init(CAN1, &can_filter_init_struct);
    
    /* CAN2过滤器14配置 */
    can_filter_init_struct.filter_number = 0;
    can_filter_init(CAN2, &can_filter_init_struct);
    
    /* 配置CAN1和CAN2的过滤器13为测试专用过滤器，消息存储到FIFO1 */
    can_filter_init_struct.filter_activate_enable = TRUE;
    can_filter_init_struct.filter_number = 13;
    can_filter_init_struct.filter_fifo = CAN_FILTER_FIFO1;
    can_filter_init_struct.filter_bit = CAN_FILTER_32BIT;
    can_filter_init_struct.filter_mode = CAN_FILTER_MODE_ID_LIST;
    
    // 配置为接收ID1 (扩展ID格式)
    can_filter_init_struct.filter_id_high = ((( 0x07AE << 3) >> 16) & 0xFFFF);
    can_filter_init_struct.filter_id_low = ((0x7FE << 3) & 0xFFFF) | 0x04;  // 0x04表示扩展帧
    
    // 配置为接收ID2 (扩展ID格式)
    can_filter_init_struct.filter_mask_high = (((0x07AF << 3) >> 16) & 0xFFFF);
    can_filter_init_struct.filter_mask_low = ((0x7FF << 3) & 0xFFFF) | 0x04;  // 0x04表示扩展帧
    
    // 为CAN1配置过滤器13
    can_filter_init(CAN1, &can_filter_init_struct);
    
    // 为CAN2配置同样的过滤器13
    can_filter_init(CAN2, &can_filter_init_struct);

    /* 使能CAN1接收中断 */
    nvic_irq_enable(CAN1_RX0_IRQn, 1, 0);
    can_interrupt_enable(CAN1, CAN_RF0MIEN_INT, TRUE);
    
    // /* 使能CAN2接收中断 */
    // nvic_irq_enable(CAN2_RX0_IRQn, 1, 0);
    // can_interrupt_enable(CAN2, CAN_RF0MIEN_INT, TRUE);
}

/**
  * @brief  切换CAN通信接口
  * @param  interface: 要切换到的CAN接口
  * @retval None
  */
void CAN_SwitchInterface(CAN_Interface_TypeDef interface)
{
    static uint8_t switch_count = 0;       // 切换计数器
    static uint32_t last_switch_time = 0;  // 上次切换时间
    uint32_t current_time = Get_Runtime_Ms();
    
    // 1. 如果已经是当前接口，无需切换
    if(interface == can_status.active_interface)
    {
        return;
    }
    
    // 2. 检测频繁切换 (5秒内超过3次)
    if(current_time - last_switch_time < 5000)
    {
        switch_count++;
        if(switch_count >= 3)
        {
            // 频繁切换告警
            // Sys_SetWarning(WARN_CAN_FREQUENT_SWITCH, WARN_LEVEL_MINOR);
            switch_count = 0; 
        }
    }
    else
    {
        // 超过时间窗口，重置计数器
        switch_count = 1; 
    }
    
    // 更新切换时间
    last_switch_time = current_time;
    
    // 3. 关闭所有CAN接口中断
    can_interrupt_enable(CAN1, CAN_RF0MIEN_INT, FALSE);
    can_interrupt_enable(CAN2, CAN_RF0MIEN_INT, FALSE);
    
    // 4. 切换接口
    can_status.active_interface = interface;
    
    // 5. 使能新接口的中断
    if(interface == CAN_INTERFACE_1)
    {
        nvic_irq_enable(CAN1_RX0_IRQn, 1, 0);
        can_interrupt_enable(CAN1, CAN_RF0MIEN_INT, TRUE);
    }
    else  // CAN_INTERFACE_2
    {
        nvic_irq_enable(CAN2_RX0_IRQn, 1, 0);
        can_interrupt_enable(CAN2, CAN_RF0MIEN_INT, TRUE);
    }
    
    // 6. 重置通信状态
    can_status.timeout_flag = 0;
    can_rx_buffer.last_rx_time = current_time;
    
    // 7. 清空接收缓冲区
    memset(&can_rx_buffer.buffer_a, 0, sizeof(CanRxCmd_TypeDef));
    memset(&can_rx_buffer.buffer_b, 0, sizeof(CanRxCmd_TypeDef));
    can_rx_buffer.buffer_ready = 0;
}

/**
  * @brief  计算LRC校验和(累加和取反加一)
  * @param  data: 数据指针
  * @param  len: 数据长度
  * @retval 校验和
  */
uint8_t Calculate_LRC(uint8_t *data, uint8_t len)
{
    if(data == NULL || len == 0)
    {
        return 0;
    }
    uint8_t sum = 0;
    for(uint8_t i = 0; i < len; i++) {
        sum += data[i];
    }
    return (uint8_t)(~sum + 1);
}

/**
  * @brief  更新状态字1
  * @param  None
  * @retval 状态字1值
  */
uint8_t UpdateStatusWord1(void)
{
    // 清除所有位
    can_system_status.status1.all = 0;
    
    // 设置控制器就绪位
    can_system_status.status1.bits.controller_ready = 1;
    
    // 设置电机运行状态位
    if(Get_Motor_State() == 1) {
        can_system_status.status1.bits.motor_running = 1;
    }
    
    // 设置锁桨状态位
    if(Get_Motor_State() == 0) {
        can_system_status.status1.bits.lock_rotor = 1;
    }
    
    // 设置控制器故障位
    if(Get_System_Fault() != 0) {
        can_system_status.status1.bits.controller_fault = 1;
    }
    
    return can_system_status.status1.all;
}

/**
  * @brief  更新状态字2
  * @param  None
  * @retval 状态字2值
  */
uint8_t UpdateStatusWord2(void)
{
    // 清除所有位
    can_system_status.status2.all = 0;
    
    // 检查母线电压
    if(Is_Bus_Overvoltage()) {
        can_system_status.status2.bits.bus_overvol = 1;
    }
    if(Is_Bus_Undervoltage()) {
        can_system_status.status2.bits.bus_undervol = 1;
    }
    
    // 检查28V备用电源
    if(Is_V28B_Overvoltage()) {
        can_system_status.status2.bits.v28b_overvol = 1;
    }
    if(Is_V28B_Undervoltage()) {
        can_system_status.status2.bits.v28b_undervol = 1;
    }
    
    // 检查28V主电源
    if(Is_V28M_Overvoltage()) {
        can_system_status.status2.bits.v28m_overvol = 1;
    }
    if(Is_V28M_Undervoltage()) {
        can_system_status.status2.bits.v28m_undervol = 1;
    }
    
    return can_system_status.status2.all;
}

/**
  * @brief  更新状态字3
  * @param  None
  * @retval 状态字3值
  */
uint8_t UpdateStatusWord3(void)
{
    // 清除所有位
    can_system_status.status3.all = 0;
    
    // 检查CAN通信状态
    if(can_status.timeout_flag) {
        can_system_status.status3.bits.can_fault = 1;
    }
    
    // 检查位置传感器
    if(Is_Position_Sensor_Fault()) {
        can_system_status.status3.bits.position_fault = 1;
    }
    
    // 检查硬件过流
    if(Is_HW_Overcurrent()) {
        can_system_status.status3.bits.hw_overcur = 1;
    }
    
    // 检查硬件自检
    if(Is_HW_Selftest_Fault()) {
        can_system_status.status3.bits.hw_selftest = 1;
    }
    
    // 检查转速跟踪
    if(Is_Speed_Track_Fault()) {
        can_system_status.status3.bits.speed_track = 1;
    }
    
    // 检查软件过流
    if(Is_SW_Overcurrent()) {
        can_system_status.status3.bits.sw_overcur = 1;
    }
    
    return can_system_status.status3.all;
}

/**
  * @brief  更新状态字4
  * @param  None
  * @retval 状态字4值
  */
uint8_t UpdateStatusWord4(void)
{
    // 清除所有位
    can_system_status.status4.all = 0;
    
    // 检查电机温度
    if(Is_Motor_Temp_Warning()) {
        can_system_status.status4.bits.motor_temp_warn = 1;
    }
    if(Is_Motor_Temp_Fault()) {
        can_system_status.status4.bits.motor_temp_fault = 1;
    }
    
    // 检查控制器温度
    if(Is_Controller_Temp_Warning()) {
        can_system_status.status4.bits.ctrl_temp_warn = 1;
    }
    
    // 检查电机超速
    if(Is_Motor_Overspeed_Warning()) {
        can_system_status.status4.bits.motor_overspd_warn = 1;
    }
    if(Is_Motor_Overspeed_Fault()) {
        can_system_status.status4.bits.motor_overspd_fault = 1;
    }
    
    return can_system_status.status4.all;
}

/**
  * @brief  发送帧1(转矩、速度、电压)
  * @param  None
  * @retval 发送结果(0:失败, 1:成功)
  */
uint8_t CAN_SendPacket1(void)
{
    static uint8_t frame1_counter = 0;  // 帧1独立计数器
    can_tx_message_type tx_message;
    uint8_t data_buffer[8];  // 临时数据缓冲区
    
    // 递增帧1计数器
    frame1_counter++;
    
    // 准备数据到临时缓冲区
    data_buffer[0] = frame1_counter;  // 使用帧1独立计数器
    
    // 转矩数据 (int16_t)
    int16_t torque = (int16_t)(Get_Motor_Torque() * 10.0f);  // 0.1N·m/LSB
    data_buffer[1] = (uint8_t)(torque & 0xFF);
    data_buffer[2] = (uint8_t)((torque >> 8) & 0xFF);
    
    // 速度数据 (int16_t)
    int16_t speed = (int16_t)Get_Motor_Speed();  // rpm
    data_buffer[3] = (uint8_t)(speed & 0xFF);
    data_buffer[4] = (uint8_t)((speed >> 8) & 0xFF);
    
    // 电压数据
    data_buffer[5] = (uint8_t)(Get_Bus_Voltage() / 4.0f);  // 4V/LSB
    data_buffer[6] = (uint8_t)(Get_V28_Main() / 0.2f);     // 0.2V/LSB
    
    // 计算校验和 (前7个字节的累加和取反加1)
    data_buffer[7] = Calculate_LRC(data_buffer, 7);
    
    // 配置发送消息
    tx_message.standard_id = 0;
    tx_message.extended_id = CAN_RX_FRAME_ID;
    tx_message.id_type = CAN_ID_EXTENDED;
    tx_message.frame_type = CAN_TFT_DATA;
    tx_message.dlc = 8;
    memcpy(tx_message.data, data_buffer, 8);
    
    // // 根据当前活动接口发送消息
    if(can_status.active_interface == 0) {
        return (can_message_transmit(CAN1, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    } else {
        return (can_message_transmit(CAN2, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    }
}

/**
  * @brief  发送帧2(电流、温度)
  * @param  None
  * @retval 发送结果(0:失败, 1:成功)
  */
uint8_t CAN_SendPacket2(void)
{
    static uint8_t frame2_counter = 0;  // 帧2独立计数器
    can_tx_message_type tx_message;
    uint8_t data_buffer[8];  // 临时数据缓冲区

    // 递增帧2计数器
    frame2_counter++;
    // 准备数据到临时缓冲区
    data_buffer[0] = frame2_counter;  // 使用帧2独立计数器
    // 备用28V电压
    data_buffer[1] = (uint8_t)(Get_V28_Backup() *5 );  // 0.2V/LSB
    // 母线电流
    data_buffer[2] = (uint8_t)(Get_Bus_Current() *0.5f );  // 2A/LSB
    // Q轴电流
    data_buffer[3] = (int8_t)(Get_Q_Current() * 0.25f );  // 4A/LSB
    // 减速器温度(无此项)
    data_buffer[4] = 0;
    // 电机温度
    data_buffer[5] = (int8_t)(Get_Motor_Temperature() * 0.5f);  // 2℃/LSB
    // 轴承温度(无此项)
    data_buffer[6] = 0;
    // 计算校验和
    data_buffer[7] = Calculate_LRC(data_buffer, 7);
    // 配置发送消息
    tx_message.standard_id = 0;
    tx_message.extended_id = CAN_TX_FRAME2_ID;
    tx_message.id_type = CAN_ID_EXTENDED;
    tx_message.frame_type = CAN_TFT_DATA;
    tx_message.dlc = 8;
    memcpy(tx_message.data, data_buffer, 8);
    
    // 根据当前活动接口直接发送消息
    if(can_status.active_interface == CAN_INTERFACE_1) {
        return (can_message_transmit(CAN1, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    } else {
        return (can_message_transmit(CAN2, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    }
}

/**
  * @brief  发送帧3(状态字)
  * @param  None
  * @retval 发送结果(0:失败, 1:成功)
  */
uint8_t CAN_SendPacket3(void)
{
    static uint8_t frame3_counter = 0;  // 帧3独立计数器
    can_tx_message_type tx_message;
    uint8_t data_buffer[8];  // 临时数据缓冲区
    
    // 递增帧3计数器
    frame3_counter++;
    
    // 准备数据到临时缓冲区
    data_buffer[0] = frame3_counter;  // 使用帧3独立计数器
    // 控制器温度
    data_buffer[1] = (int8_t)(Get_Controller_Temperature() * 0.5f);  // 2℃/LSB
    // 冷却液温度(无此项)
    data_buffer[2] = 0;
    // 更新并获取状态字1-4
    data_buffer[3] = UpdateStatusWord1();
    data_buffer[4] = UpdateStatusWord2();
    data_buffer[5] = UpdateStatusWord3();
    data_buffer[6] = UpdateStatusWord4();
    // 计算校验和
    data_buffer[7] = Calculate_LRC(data_buffer, 7);
    // 配置发送消息
    tx_message.standard_id = 0;
    tx_message.extended_id = CAN_TX_FRAME3_ID;
    tx_message.id_type = CAN_ID_EXTENDED;
    tx_message.frame_type = CAN_TFT_DATA;
    tx_message.dlc = 8;
    memcpy(tx_message.data, data_buffer, 8);
    
    // 根据当前活动接口直接发送消息
    if(can_status.active_interface == CAN_INTERFACE_1) {
        return (can_message_transmit(CAN1, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    } else {
        return (can_message_transmit(CAN2, &tx_message) != CAN_TX_STATUS_NO_EMPTY);
    }
}

/**
  * @brief  CAN通信管理函数 - 在1ms定时器中调用
  * @note   集成发送、超时检测和CAN口切换功能
  * @param  None
  * @retval None
  */
void CAN_ManageCommunication(void)
{
    static uint32_t ms_counter = 0;     // 毫秒计数器
    static uint8_t packet_index = 0;    // 当前发送的包索引
    static uint8_t timeout_check_counter = 0; // 超时检查计数器
    
    // 递增毫秒计数器
    ms_counter++;
    
    // 1. 处理发送任务
#if (CAN_TX_MODE == 0)
    /* 模式1: 每20ms发送一个包，循环发送，包与包间隔60ms */
    if(ms_counter >= CAN_TX_INTERVAL_MS)
    {
        // 发送当前索引对应的包
        switch(packet_index)
        {
            case 0:
                CAN_SendPacket1();
                break;
            case 1:
                CAN_SendPacket2();
                break;
            case 2:
                CAN_SendPacket3();
                break;
        }
        
        // 更新包索引和重置计数器
        packet_index = (packet_index + 1) % 3;
        ms_counter = 0;
    }
    
#else /* CAN_TX_MODE_20MS */
    /* 模式2: 在20ms内分散发送三个包 */
    if(ms_counter == 1)                // 在1ms时发送包1
    {
        CAN_SendPacket1();
    }
    else if(ms_counter == 7)           // 在7ms时发送包2
    {
        CAN_SendPacket2();
    }
    else if(ms_counter == 14)          // 在14ms时发送包3
    {
        CAN_SendPacket3();
    }
    else if(ms_counter >= CAN_TX_INTERVAL_MS)
    {
        // 重置计数器，准备下一轮发送
        ms_counter = 0;
    }
#endif

    // 2. 超时检测 - 每100ms检查一次
    timeout_check_counter++;
    if(timeout_check_counter >= 100)  // 1ms调用，100ms
    {
        timeout_check_counter = 0;
        
        if(!can_status.timeout_flag)
        {
            uint32_t current_time = Get_Runtime_Ms();  
            
            // 检查是否超过接收超时时间
            if((current_time - can_rx_buffer.last_rx_time) > CAN_RX_TIMEOUT_MS)
            {
                // 设置超时标志
                can_status.timeout_flag = 1;
                
                // 如果启用了自动切换功能，则切换CAN接口
                if(can_status.auto_switch_enabled)
                {
                    // 切换到另一个CAN接口
                    CAN_Interface_TypeDef new_interface = 
                        (can_status.active_interface == CAN_INTERFACE_1) ? 
                        CAN_INTERFACE_2 : CAN_INTERFACE_1;
                    
                    CAN_SwitchInterface(new_interface);
                }
            }
        }
    }
    
    // 3. 处理接收到的命令 ，主循环1ms处理
    // if(can_rx_buffer.buffer_ready)
    // {
    //     Process_Motor_Command();
    // }
}

/**
  * @brief  CAN接收辅助处理函数 - 处理被延迟的非关键操作
  * @note   在主循环中调用，处理中断中被延迟的操作以保证中断性能
  * @param  None
  * @retval None
  */
void CAN_ProcessDelayedOperations(void)
{
    static uint32_t last_update_time = 0;
    uint32_t current_time = Get_Runtime_Ms();
    
    // 每10ms执行一次，避免过于频繁
    if((current_time - last_update_time) < 10) {
        return;
    }
    last_update_time = current_time;
    
    // 更新最后接收时间（用于超时检测）
    // 注意：这里我们使用简化的时间戳更新
    static uint32_t last_rx_time = 0;
    if(can_status.timeout_flag == 0) {
        last_rx_time = current_time;
    }
    
    // 检查接收超时
    if((current_time - last_rx_time) > CAN_RX_TIMEOUT_MS) {
        can_status.timeout_flag = 1;
    }
    
    // 其他延迟操作可以在这里添加：
    // - 详细的错误统计
    // - 通信质量监控
    // - 双缓冲区状态维护
}

/**
  * @brief  CAN1接收中断处理函数
  * @note   最小化函数调用
  * @param  None
  * @retval None
  */
void CAN1_RX0_IRQ_Callback(void)
{
    can_rx_message_type rx_message;
    
    // 接收CAN消息 
    can_message_receive(CAN1, CAN_RX_FIFO0, &rx_message);
    
    // 快速ID检查
    uint32_t received_id_masked = rx_message.extended_id & CAN_ID_MASK;
    if(received_id_masked != EXPECTED_ID_MASKED && received_id_masked != COMPAT_ID_MASKED) {
        return; // 快速退出，无效ID
    }
    
    // 直接从接收数据解析
    uint8_t cmd_word = rx_message.data[1];
    
    // 快速命令有效性检查
    if(cmd_word > 0x02) {
        return; // 无效命令，快速退出
    }
    
    // 校验和检查
    uint8_t calc_checksum = Calculate_LRC(rx_message.data, 7);
    if(calc_checksum != rx_message.data[7]) {
        can_status.can1_error_count++;
        return; // 校验失败，快速退出
    }
    
    // 内联命令处理
    switch(cmd_word) {
        case 0x00:  // 停止电机
            g_SystemStatus.cmd.bits.motor_stop = 1;
            g_SystemStatus.cmd.bits.motor_start = 0;
            break;
            
        case 0x01:  // 启动电机
            g_SystemStatus.cmd.bits.motor_start = 1;
            g_SystemStatus.cmd.bits.motor_stop = 0;
            
            // 内联速度解析和设置
            int16_t speed_cmd = GET_SPEED_CMD(rx_message.data[3], rx_message.data[2]);
            if(speed_cmd <= MOTOR_MAX_SPEED && speed_cmd >= MOTOR_MIN_SPEED) {
                can_status.speed_setpoint = speed_cmd;
                // 设置转速到电机参数
                //*pCsvParamSpeed_ref = (float)speed_cmd;
            }
            
            // 启动命令处理完成
            gpio_bits_toggle(GPIOE, GPIO_PINS_2);
            break;
            
        case 0x02:  // 锁桨 (前旋翼无此功能)
            // 空操作
            break;
    }
    
    // 状态更新
    can_status.timeout_flag = 0;  // 清除超时标志
}

/**
  * @brief  CAN2接收中断处理函数
  * @note   最小化函数调用
  * @param  None
  * @retval None
  */
void CAN2_RX0_IRQ_Callback(void)
{
    can_rx_message_type rx_message;
    
    // 接收CAN消息
    can_message_receive(CAN2, CAN_RX_FIFO0, &rx_message);
    
    // 快速ID检查 - 预计算常量，避免重复计算
    uint32_t received_id_masked = rx_message.extended_id & CAN_ID_MASK;
    if(received_id_masked != EXPECTED_ID_MASKED && received_id_masked != COMPAT_ID_MASKED) {
        return; // 快速退出，无效ID
    }
    
    // 直接从接收数据解析，避免缓冲区拷贝
    uint8_t cmd_word = rx_message.data[1];
    
    // 快速命令有效性检查
    if(cmd_word > 0x02) {
        return; // 无效命令，快速退出
    }
    
    // 校验和检查
    uint8_t calc_checksum = Calculate_LRC(rx_message.data, 7);
    if(calc_checksum != rx_message.data[7]) {
        can_status.can2_error_count++;
        return; // 校验失败，快速退出
    }
    
    // 内联命令处理 - 直接操作，避免函数调用开销
    switch(cmd_word) {
        case 0x00:  // 停止电机
            g_SystemStatus.cmd.bits.motor_stop = 1;
            g_SystemStatus.cmd.bits.motor_start = 0;
            break;
            
        case 0x01:  // 启动电机
            g_SystemStatus.cmd.bits.motor_start = 1;
            g_SystemStatus.cmd.bits.motor_stop = 0;
            
            // 内联速度解析和设置
            int16_t speed_cmd = GET_SPEED_CMD(rx_message.data[3], rx_message.data[2]);
            if(speed_cmd <= MOTOR_MAX_SPEED && speed_cmd >= MOTOR_MIN_SPEED) {
                can_status.speed_setpoint = speed_cmd;
                // 设置转速到电机参数
                //*pCsvParamSpeed_ref = (float)speed_cmd;
                
                // 如果系统就绪，直接设置目标转速
                if(g_SystemStatus.work.bits.sys_ready) {
                    Set_Target_Speed(speed_cmd);
                }
            }
            
            // 性能指示 - 启动命令处理完成
            gpio_bits_toggle(GPIOE, GPIO_PINS_2);
            break;
            
        case 0x02:  // 锁桨 (前旋翼无此功能)
            // 空操作，快速跳过
            break;
    }
    
    // 最小化状态更新
    can_status.timeout_flag = 0;  // 清除超时标志
}

/**
  * @brief  CAN发送测试函数 - 在20ms定时器中调用
  * @param  None
  * @retval None
  */
void CAN_TestTransmit(void)
{
    static uint8_t packet_index = 0;
    static uint8_t msg_counter = 0;
    can_tx_message_type tx_message;
    uint8_t data_buffer[8];
    
    msg_counter++;  // 递增消息计数器
    
    /* 轮流发送三个数据包 */
    switch(packet_index)
    {
        case 0:  // 发送帧1 - 转矩、速度、电压
        {
            // 准备数据到临时缓冲区
            data_buffer[0] = msg_counter;  // 消息计数器
            
            // 转矩数据 (int16_t) - 25.0 N·m
            int16_t torque = 250;
            data_buffer[1] = (uint8_t)(torque & 0xFF);
            data_buffer[2] = (uint8_t)((torque >> 8) & 0xFF);
            
            // 速度数据 (int16_t) - 3000 rpm
            int16_t speed = 3000;
            data_buffer[3] = (uint8_t)(speed & 0xFF);
            data_buffer[4] = (uint8_t)((speed >> 8) & 0xFF);
            
            // 电压数据
            data_buffer[5] = 175;  // 700V (175 * 4V)
            data_buffer[6] = 140;  // 28V (140 * 0.2V)
            
            // 计算校验和
            data_buffer[7] = Calculate_LRC(data_buffer, 7);
            
            // 配置发送消息
            tx_message.standard_id = 0;
            tx_message.extended_id = CAN_TX_FRAME1_ID;
            tx_message.id_type = CAN_ID_EXTENDED;
            tx_message.frame_type = CAN_TFT_DATA;
            tx_message.dlc = 8;
            memcpy(tx_message.data, data_buffer, 8);
            
            // 发送消息
            can_message_transmit(CAN1, &tx_message);
            break;
        }
        
        case 1:  // 发送帧2 - 电流、温度
        {
            // 准备数据到临时缓冲区
            data_buffer[0] = msg_counter;  // 消息计数器
            data_buffer[1] = 140;          // 28V备用电源 (140 * 0.2V)
            data_buffer[2] = 50;           // 母线电流 100A (50 * 2A)
            data_buffer[3] = 25;           // Q轴电流 100A (25 * 4A)
            data_buffer[4] = 0;            // 减速器温度 (不适用)
            data_buffer[5] = 35;           // 电机温度 70°C (35 * 2°C)
            data_buffer[6] = 0;            // 轴承温度 (不适用)
            
            // 计算校验和
            data_buffer[7] = Calculate_LRC(data_buffer, 7);
            
            // 配置发送消息
            tx_message.standard_id = 0;
            tx_message.extended_id = CAN_TX_FRAME2_ID;
            tx_message.id_type = CAN_ID_EXTENDED;
            tx_message.frame_type = CAN_TFT_DATA;
            tx_message.dlc = 8;
            memcpy(tx_message.data, data_buffer, 8);
            
            // 发送消息
            can_message_transmit(CAN1, &tx_message);
            break;
        }
        
        case 2:  // 发送帧3 - 状态字
        {
            // 准备数据到临时缓冲区
            data_buffer[0] = msg_counter;  // 消息计数器
            data_buffer[1] = 30;           // 控制器温度 60°C (30 * 2°C)
            data_buffer[2] = 0;            // 冷却液温度 (不适用)
            data_buffer[3] = STATUS1_CONTROLLER_READY | STATUS1_MOTOR_RUNNING;  // 状态字1
            data_buffer[4] = 0;            // 状态字2 - 无故障
            data_buffer[5] = 0;            // 状态字3 - 无故障
            data_buffer[6] = 0;            // 状态字4 - 无故障
            
            // 计算校验和
            data_buffer[7] = Calculate_LRC(data_buffer, 7);
            
            // 配置发送消息
            tx_message.standard_id = 0;
            tx_message.extended_id = CAN_TX_FRAME3_ID;
            tx_message.id_type = CAN_ID_EXTENDED;
            tx_message.frame_type = CAN_TFT_DATA;
            tx_message.dlc = 8;
            memcpy(tx_message.data, data_buffer, 8);
            
            // 发送消息
            can_message_transmit(CAN1, &tx_message);
            break;
        }
    }
    
    /* 更新包索引，轮流发送三个包 */
    packet_index = (packet_index + 1) % 3;
}